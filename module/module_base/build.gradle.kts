plugins {
    alias(libs.plugins.common.gradle)
}

dependencies {
    api(project(":base:base_network"))
    api(project(":base:base_router"))
    api(project(":base:base_res"))
    api(project(":base:base_complex_imageview"))
    api(project(":base:base_multi_type"))
    api(project(":base:base_game"))
    api(project(":base:base_dialog"))
    implementation(project(":base:base_multi_state_page"))
    api(project(":base:base_crash"))
    implementation(project(":common:common_widget"))
    api(libs.androidx.core.ktx)
    api(libs.androidx.appcompat)
    api(libs.constraintlayout)
    api(libs.navigation.ui)
    api(libs.navigation.fragment)

    compileOnly(project(":base:base_ffmpeg"))
    compileOnly(project(":base:base_audio_player"))
    implementation(libs.refresh.layout.kernel)      //核心必须依赖
}