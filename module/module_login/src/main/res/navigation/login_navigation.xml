<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/login_navigation"
    app:startDestination="@id/loginFragment">

    <fragment
        android:id="@+id/loginFragment"
        android:name="com.snails.module.login.ui.LoginFragment"
        android:label="fragment_login"
        tools:layout="@layout/fragment_login">
        <action
            android:id="@+id/action_loginFragment_to_phoneLoginRegisterFragment"
            app:destination="@id/phoneLoginRegisterFragment" />
        <action
            android:id="@+id/action_loginFragment_to_bindPhoneFragment"
            app:destination="@id/bindPhoneFragment" />
    </fragment>

    <fragment
        android:id="@+id/phoneLoginRegisterFragment"
        android:name="com.snails.module.login.ui.PhoneLoginRegisterFragment"
        android:label="PhoneLoginRegisterFragment"
        tools:layout="@layout/fragment_phone_login_register">
        <action
            android:id="@+id/action_phoneLoginRegisterFragment_to_regionSelectionFragment"
            app:destination="@id/regionSelectionFragment" />
        <action
            android:id="@+id/action_phoneLoginRegisterFragment_to_bindPhoneFragment"
            app:destination="@id/bindPhoneFragment" />
    </fragment>
    <fragment
        android:id="@+id/bindPhoneFragment"
        android:name="com.snails.module.login.ui.BindPhoneFragment"
        android:label="BindPhoneFragment"
        tools:layout="@layout/fragment_bind_phone">
        <action
            android:id="@+id/action_bindPhoneFragment_to_regionSelectionFragment"
            app:destination="@id/regionSelectionFragment" />
    </fragment>
    <fragment
        android:id="@+id/regionSelectionFragment"
        android:name="com.snails.module.login.ui.RegionSelectionFragment"
        android:label="RegionSelectionFragment"
        tools:layout="@layout/fragment_region_selection" />
</navigation>