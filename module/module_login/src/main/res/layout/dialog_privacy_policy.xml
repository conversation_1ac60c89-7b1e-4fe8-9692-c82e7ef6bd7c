<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/base_sw_dp_311"
    android:layout_height="@dimen/base_sw_dp_278"
    android:layout_gravity="center">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/base_sw_dp_311"
        android:layout_height="@dimen/base_sw_dp_278"
        android:background="@drawable/com_dialog_white_20_shape"
        android:padding="@dimen/base_sw_dp_20">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_dialog_scan_code_tips"
            android:textColor="@color/text_headline"
            android:textSize="@dimen/headline_h4"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTipsDesc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_8"
            android:textSize="@dimen/text_body_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle"
            tools:text="欢迎使用蜗牛阅读，为保证您的合法权益，请仔细阅读并理解《中国移动认证服务协议》、《用户服务协议》、《用户隐私协议》。为了继续使用我们的服务，请点击“同意”以表示您接受这些条款" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/btnClose"
            android:layout_width="0dp"
            android:layout_height="@dimen/base_sw_dp_48"
            android:layout_marginTop="@dimen/base_sw_dp_16"
            android:layout_marginEnd="@dimen/base_sw_dp_6"
            android:background="@drawable/com_btn_stroke_pb_bg_shape"
            android:elevation="@dimen/base_sw_dp_0"
            android:gravity="center"
            android:text="@string/str_close"
            android:textColor="@color/text_headline"
            android:textSize="@dimen/button_h1"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvAgree"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvAgree"
            android:layout_width="0dp"
            android:layout_height="@dimen/base_sw_dp_48"
            android:layout_marginStart="@dimen/base_sw_dp_6"
            android:layout_marginTop="@dimen/base_sw_dp_16"
            android:background="@drawable/com_btn_pb_bg_shape"
            android:elevation="@dimen/base_sw_dp_0"
            android:gravity="center"
            android:text="@string/str_agree"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/button_h1"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/btnClose" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>