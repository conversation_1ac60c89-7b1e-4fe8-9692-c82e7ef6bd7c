<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/base_sw_dp_16"
    android:paddingEnd="@dimen/base_sw_dp_16">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvRegionCode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:layout_marginBottom="@dimen/base_sw_dp_20"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="+86" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvRegionChecked"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_10"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:layout_marginBottom="@dimen/base_sw_dp_20"
        android:text="@string/str_region_checked"
        android:textColor="@color/text_link"
        android:textSize="@dimen/text_body_medium"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvRegionCode"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvRegionName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:layout_marginBottom="@dimen/base_sw_dp_20"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="中国" />

    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:background="@color/border_divider"
        app:layout_constraintTop_toBottomOf="@+id/tvRegionCode" />

</androidx.constraintlayout.widget.ConstraintLayout>