<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    android:fitsSystemWindows="true"
    tools:context=".ui.LoginFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_520"
        android:layout_centerInParent="true">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivLoginLogo"
            android:layout_width="@dimen/base_sw_dp_88"
            android:layout_height="@dimen/base_sw_dp_88"
            android:background="@drawable/ic_app_logo_hd"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/tvLoginPhone"
            android:layout_width="@dimen/base_sw_dp_295"
            android:layout_height="@dimen/base_sw_dp_48"
            android:layout_marginTop="@dimen/base_sw_dp_40"
            android:background="@drawable/com_btn_sb_bg_shape"
            android:enabled="false"
            android:gravity="center"
            android:textColor="@color/text_describe"
            android:textSize="@dimen/headline_h3"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivLoginLogo"
            tools:text="135****1234" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLoginServiceProviders"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_12"
            android:textColor="@color/text_describe"
            android:textSize="@dimen/text_body_footnote"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvLoginPhone"
            tools:text="中国移动提供认证服务" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLoginOneClickLogin"
            android:layout_width="@dimen/base_sw_dp_295"
            android:layout_height="@dimen/base_sw_dp_48"
            android:layout_marginTop="@dimen/base_sw_dp_40"
            android:background="@drawable/com_btn_pb_bg_shape"
            android:gravity="center"
            android:text="@string/str_login_one_click_login"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/button_h1"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvLoginServiceProviders" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLoginOtherPhoneLogin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_12"
            android:text="@string/str_login_other_phone_login"
            android:textColor="@color/text_link"
            android:textSize="@dimen/text_body_small"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvLoginOneClickLogin" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:paddingBottom="@dimen/base_sw_dp_42">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLoginOtherWaysLogin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_12"
            android:text="@string/str_login_other_ways_login"
            android:textColor="@color/text_headline"
            android:textSize="@dimen/text_body_small"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/vLoginLeftLine"
            android:layout_width="@dimen/base_sw_dp_98"
            android:layout_height="2px"
            android:layout_marginEnd="@dimen/base_sw_dp_8"
            android:background="@color/border_divider"
            app:layout_constraintBottom_toBottomOf="@+id/tvLoginOtherWaysLogin"
            app:layout_constraintEnd_toStartOf="@+id/tvLoginOtherWaysLogin"
            app:layout_constraintTop_toTopOf="@+id/tvLoginOtherWaysLogin"
            tools:ignore="PxUsage" />

        <View
            android:id="@+id/vLoginRightLine"
            android:layout_width="@dimen/base_sw_dp_98"
            android:layout_height="2px"
            android:layout_marginStart="@dimen/base_sw_dp_8"
            android:background="@color/border_divider"
            app:layout_constraintBottom_toBottomOf="@+id/tvLoginOtherWaysLogin"
            app:layout_constraintStart_toEndOf="@+id/tvLoginOtherWaysLogin"
            app:layout_constraintTop_toTopOf="@+id/tvLoginOtherWaysLogin"
            tools:ignore="PxUsage" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivLoginWeChat"
            android:layout_width="@dimen/base_sw_dp_44"
            android:layout_height="@dimen/base_sw_dp_44"
            android:layout_marginTop="@dimen/base_sw_dp_24"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            android:src="@drawable/svg_wechat"
            app:layout_constraintEnd_toStartOf="@+id/ivLoginWeChatQrCode"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvLoginOtherWaysLogin" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivLoginWeChatQrCode"
            android:layout_width="@dimen/base_sw_dp_44"
            android:layout_height="@dimen/base_sw_dp_44"
            android:layout_marginStart="@dimen/base_sw_dp_16"
            android:layout_marginTop="@dimen/base_sw_dp_24"
            android:src="@drawable/svg_wechat_qrcode"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivLoginWeChat"
            app:layout_constraintTop_toBottomOf="@+id/tvLoginOtherWaysLogin" />

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbLoginAgreeAllAgreement"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_40"
            android:layout_marginTop="@dimen/base_sw_dp_40"
            android:layout_marginEnd="@dimen/base_sw_dp_40"
            android:button="@drawable/cb_checkbox_selector"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivLoginWeChat" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLoginAllAgreement"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_5"
            android:layout_marginEnd="@dimen/base_sw_dp_40"
            android:textSize="@dimen/text_body_footnote"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/cbLoginAgreeAllAgreement"
            app:layout_constraintTop_toTopOf="@+id/cbLoginAgreeAllAgreement"
            tools:text="同意中国移动认证服务条款、用户注册协议、隐私协议、儿童隐私政策" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>