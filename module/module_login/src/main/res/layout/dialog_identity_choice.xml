<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/base_sw_dp_311"
    android:layout_height="@dimen/base_sw_dp_334"
    android:layout_gravity="center">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/base_sw_dp_311"
        android:layout_height="@dimen/base_sw_dp_334"
        android:background="@drawable/com_dialog_white_20_shape">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_24"
            android:text="@string/str_identity_choice_tips"
            android:textColor="@color/text_headline"
            android:textSize="@dimen/headline_h4"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/vTeacher"
            android:layout_width="0dp"
            android:layout_height="@dimen/base_sw_dp_84"
            android:layout_marginHorizontal="@dimen/base_sw_dp_20"
            android:layout_marginTop="@dimen/base_sw_dp_20"
            android:background="@drawable/shape_identity_choice_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivTeacher"
            android:layout_width="@dimen/base_sw_dp_60"
            android:layout_height="@dimen/base_sw_dp_60"
            android:layout_marginStart="@dimen/base_sw_dp_12"
            android:background="@drawable/svg_teacher"
            app:layout_constraintBottom_toBottomOf="@+id/vTeacher"
            app:layout_constraintStart_toStartOf="@+id/vTeacher"
            app:layout_constraintTop_toTopOf="@+id/vTeacher" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_8"
            android:text="@string/str_am_teacher"
            android:textColor="@color/text_body"
            android:textSize="@dimen/headline_h4"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/vTeacher"
            app:layout_constraintStart_toEndOf="@+id/ivTeacher"
            app:layout_constraintTop_toTopOf="@+id/vTeacher" />

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbTeacher"
            android:layout_width="@dimen/base_sw_dp_16"
            android:layout_height="@dimen/base_sw_dp_16"
            android:layout_marginEnd="@dimen/base_sw_dp_12"
            android:background="@drawable/cb_checkbox_selector"
            android:button="@null"
            android:checked="true"
            android:enabled="false"
            app:layout_constraintBottom_toBottomOf="@+id/vTeacher"
            app:layout_constraintEnd_toEndOf="@+id/vTeacher"
            app:layout_constraintTop_toTopOf="@+id/vTeacher" />

        <View
            android:id="@+id/vStudent"
            android:layout_width="0dp"
            android:layout_height="@dimen/base_sw_dp_84"
            android:layout_marginHorizontal="@dimen/base_sw_dp_20"
            android:layout_marginTop="@dimen/base_sw_dp_8"
            android:background="@drawable/shape_identity_choice_unselect_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vTeacher" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivStudent"
            android:layout_width="@dimen/base_sw_dp_60"
            android:layout_height="@dimen/base_sw_dp_60"
            android:layout_marginStart="@dimen/base_sw_dp_12"
            android:background="@drawable/svg_student"
            app:layout_constraintBottom_toBottomOf="@+id/vStudent"
            app:layout_constraintStart_toStartOf="@+id/vStudent"
            app:layout_constraintTop_toTopOf="@+id/vStudent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_8"
            android:text="@string/str_am_student"
            android:textColor="@color/text_body"
            android:textSize="@dimen/headline_h4"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/vStudent"
            app:layout_constraintStart_toEndOf="@+id/ivStudent"
            app:layout_constraintTop_toTopOf="@+id/vStudent" />

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbStudent"
            android:layout_width="@dimen/base_sw_dp_16"
            android:layout_height="@dimen/base_sw_dp_16"
            android:layout_marginEnd="@dimen/base_sw_dp_12"
            android:background="@drawable/cb_checkbox_selector"
            android:button="@null"
            android:enabled="false"
            app:layout_constraintBottom_toBottomOf="@+id/vStudent"
            app:layout_constraintEnd_toEndOf="@+id/vStudent"
            app:layout_constraintTop_toTopOf="@+id/vStudent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvConfirm"
            android:layout_width="0dp"
            android:layout_height="@dimen/base_sw_dp_48"
            android:layout_marginHorizontal="@dimen/base_sw_dp_64"
            android:layout_marginTop="@dimen/base_sw_dp_20"
            android:background="@drawable/com_btn_pb_bg_shape"
            android:elevation="@dimen/base_sw_dp_0"
            android:gravity="center"
            android:text="@string/str_confirm"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/button_h1"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vStudent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>