<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/base_sw_dp_311"
    android:layout_height="@dimen/base_sw_dp_334"
    android:layout_gravity="center">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/base_sw_dp_311"
        android:layout_height="@dimen/base_sw_dp_334"
        android:background="@drawable/com_dialog_white_20_shape"
        android:paddingBottom="@dimen/base_sw_dp_48">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivCloseDialog"
            android:layout_width="@dimen/base_sw_dp_24"
            android:layout_height="@dimen/base_sw_dp_24"
            android:layout_margin="@dimen/base_sw_dp_16"
            android:src="@drawable/svg_dialog_close"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_32"
            android:text="@string/str_login_wechat_scan_code"
            android:textColor="@color/text_headline"
            android:textSize="@dimen/headline_h4"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTipsDesc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_8"
            android:gravity="center"
            android:text="@string/str_login_wechat_scan_code_tips"
            android:textColor="@color/text_body"
            android:textSize="@dimen/text_body_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

        <View
            android:id="@+id/vScanCodeBg"
            android:layout_width="@dimen/base_sw_dp_180"
            android:layout_height="@dimen/base_sw_dp_180"
            android:layout_marginTop="@dimen/base_sw_dp_20"
            android:background="@drawable/wechat_scan_code_bg_shape"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTipsDesc" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivScanCode"
            android:layout_width="@dimen/base_sw_dp_168"
            android:layout_height="@dimen/base_sw_dp_168"
            android:background="@drawable/svg_login_scan_code"
            app:layout_constraintBottom_toBottomOf="@+id/vScanCodeBg"
            app:layout_constraintEnd_toEndOf="@+id/vScanCodeBg"
            app:layout_constraintStart_toStartOf="@+id/vScanCodeBg"
            app:layout_constraintTop_toTopOf="@+id/vScanCodeBg" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivScanCodeState"
            android:layout_width="@dimen/base_sw_dp_72"
            android:layout_height="@dimen/base_sw_dp_72"
            app:layout_constraintBottom_toBottomOf="@+id/vScanCodeBg"
            app:layout_constraintEnd_toEndOf="@+id/vScanCodeBg"
            app:layout_constraintStart_toStartOf="@+id/vScanCodeBg"
            app:layout_constraintTop_toTopOf="@+id/vScanCodeBg" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>