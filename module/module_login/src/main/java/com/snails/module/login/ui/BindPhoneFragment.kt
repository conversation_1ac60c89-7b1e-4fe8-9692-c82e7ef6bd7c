package com.snails.module.login.ui

import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.Navigation
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.BaseVBFragment
import com.snails.module.login.databinding.FragmentBindPhoneBinding
import com.snails.module.login.dialog.IdentityChoiceDialog
import com.snails.module.login.viewmodel.LoginViewModel

class BindPhoneFragment : BaseVBFragment<FragmentBindPhoneBinding>() {

    private val loginViewModel: LoginViewModel by activityViewModels()
    private var openId: String? = null

    override fun initData() {
        arguments?.let {
            openId = it.getString("openId") //微信登录 openid
        }
    }

    override fun initView() {
        //返回
        binding.commonTitleView.setBackClickListener { v ->
            Navigation.findNavController(v).popBackStack()
        }

        binding.phoneNumberInputView.apply {
            //设置区号选择点击事件
            setOnCountryCodeClickListener { v ->
//                Navigation.findNavController(v)
//                    .navigate(R.id.action_bindPhoneFragment_to_regionSelectionFragment)
            }
            //监听手机号输入改变
            addPhoneChangeListener { enable ->
                //改变获取验证码按钮状态
                binding.verificationCodeView.changeOperateVerificationCodeState(enable)
                //改变绑定手机号按钮状态
                changeBindPhoneBtnState()
            }
        }

        binding.verificationCodeView.apply {
            //设置获取验证码点击事件
            setGetVerificationCodeClickListener {
                val phone = binding.phoneNumberInputView.getPhoneNumber()
                val countryCode = binding.phoneNumberInputView.getCountryCode()
                loginViewModel.getGetVerificationCode(phone, countryCode)
            }

            //监听验证码输入改变
            addVerificationCodeChangeListener {
                //改变绑定手机号按钮状态
                changeBindPhoneBtnState()
            }
        }
    }

    override fun initObserve() {
        loginViewModel.currentSelectedRegion.observe(viewLifecycleOwner) {
            it.prefix?.let { it1 -> binding.phoneNumberInputView.setCountryCode(it1) }
        }
        loginViewModel.verificationCodeLiveData.observe(viewLifecycleOwner) {
            if (it) {
                binding.verificationCodeView.startCountdown(lifecycleScope)
            }
        }
    }

    /**
     * 改变绑定手机号按钮状态
     */
    private fun changeBindPhoneBtnState() {
        val hadPhone = binding.phoneNumberInputView.hadPhone()
        val hadCode = binding.verificationCodeView.hadCode()
        binding.tvBindPhone.isEnabled = (hadPhone && hadCode)
    }

    override fun initClick() {
        binding.tvBindPhone.singleClick {
            val phone = binding.phoneNumberInputView.getPhoneNumber()
            val code = binding.verificationCodeView.getVerificationCode()
            val countryCode = binding.phoneNumberInputView.getCountryCode()
            openId?.let { openId ->
                loginViewModel.bindPhone("$countryCode-$phone", code, openId) { isTeacher ->
                    if (isTeacher == true) {
                        IdentityChoiceDialog {
                            if (it) {
                                jumpTeacherHome()
                            } else {
                                jumpHome()
                            }
                        }.show(childFragmentManager, "")
                    } else {
                        jumpHome()
                    }
                }
            }
        }
    }

    /**
     * 跳转主页
     */
    private fun jumpHome() {
        HRouter.navigation(RouterPath.MAIN_BASE)
        requireActivity().finish()
    }

    /**
     * 跳转教师端主页
     */
    private fun jumpTeacherHome() {
        HRouter.navigation(RouterPath.TEACHER_MAIN_BASE)
        requireActivity().finish()
    }

}