package com.snails.module.login.widget

import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.login.PhoneAreaCodInfo
import com.snails.module.login.viewbinder.RegionSelectionViewBinder

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月19日 14:07:48
 */
class RegionSelectionView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    var itemClickListener: ItemClickListener? = null
    private val listAdapter = MultiTypeAdapter()

    init {
        initView()
    }

    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(
                PhoneAreaCodInfo::class.java,
                RegionSelectionViewBinder {
                    itemClickListener?.itemClick(it)
                }
            )
        }
        adapter = listAdapter
    }

    @Suppress("UNCHECKED_CAST")
    fun setData(new: List<PhoneAreaCodInfo>) {
        val old = listAdapter.items as List<PhoneAreaCodInfo>
        val diffResult: DiffUtil.DiffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition].javaClass == new[newItemPosition].javaClass
            }

            override fun getOldListSize(): Int {
                return old.size
            }

            override fun getNewListSize(): Int {
                return new.size
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition] == new[newItemPosition]
            }

        })
        listAdapter.items = new
        diffResult.dispatchUpdatesTo(listAdapter)
    }

    interface ItemClickListener {
        fun itemClick(item: PhoneAreaCodInfo)
    }
}