package com.snails.module.login.viewbinder

import android.annotation.SuppressLint
import com.blankj.utilcode.util.ColorUtils
import com.snails.base.network.repository.info.login.PhoneAreaCodInfo
import com.snails.base.res.R
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.login.databinding.ItemRegionSelectionBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月19日 14:11:27
 */
class RegionSelectionViewBinder(private val itemClickCallBack: (PhoneAreaCodInfo) -> Unit) :
    ViewBindingDelegate<PhoneAreaCodInfo, ItemRegionSelectionBinding>() {


    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ItemRegionSelectionBinding>,
        item: PhoneAreaCodInfo
    ) {
        holder.binding.tvRegionCode.text = "+${item.prefix}"
        holder.binding.tvRegionName.text = item.countryName
        if (item.selected) {
            holder.binding.tvRegionChecked.visible()
            holder.binding.tvRegionCode.setTextColor(ColorUtils.getColor(R.color.text_link))
            holder.binding.tvRegionName.setTextColor(ColorUtils.getColor(R.color.text_link))
        } else {
            holder.binding.tvRegionChecked.gone()
            holder.binding.tvRegionCode.setTextColor(ColorUtils.getColor(R.color.text_headline))
            holder.binding.tvRegionName.setTextColor(ColorUtils.getColor(R.color.text_headline))
        }

        holder.itemView.singleClick {
            itemClickCallBack.invoke(item)
        }
    }
}