package com.snails.module.login.ext

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import androidx.appcompat.widget.AppCompatTextView
import com.blankj.utilcode.util.SpanUtils
import com.snails.base.network.ext.PrivacyPolicyConstants
import com.snails.base.network.repository.info.login.OneKeyLoginInfo
import com.snails.base.router.HRouter
import com.snails.module.login.R

/**
 * @Description
 * <AUTHOR> 隐私协议拓展
 * @CreateTime 2024年08月16日 18:11:25
 */
fun AppCompatTextView.setPrivacyPolicy(
    context: Context?, oneKeyLoginInfo: OneKeyLoginInfo? = null
) {
    val ctx = context ?: return
    val describeColor = ctx.getColor(R.color.text_describe)
    val linkColor = ctx.getColor(R.color.text_link)
    this.highlightColor = Color.TRANSPARENT
    SpanUtils.with(this).append("同意").setForegroundColor(describeColor).apply {
        if (oneKeyLoginInfo?.protocolName?.isNotEmpty() == true) {
            this.append("${oneKeyLoginInfo.protocolName}、").setForegroundColor(linkColor)
                .setClickSpan(linkColor, false) {
                    oneKeyLoginInfo.protocolUrl.let { it1 ->
                        HRouter.navigation(
                            it1, Bundle().apply {
                                putString("title", oneKeyLoginInfo.protocolName)
                            }
                        )
                    }
                }
        }
    }.append("用户注册协议、").setForegroundColor(linkColor).setClickSpan(linkColor, false) {
        HRouter.navigation(PrivacyPolicyConstants.USER_SERVICE_AGREEMENT, Bundle().apply {
            putString("title", "用户服务协议")
        })
    }.append("用户隐私协议").setForegroundColor(linkColor).setClickSpan(linkColor, false) {
        HRouter.navigation(PrivacyPolicyConstants.USER_PRIVACY_AGREEMENT, Bundle().apply {
            putString("title", "用户隐私协议")
        })
    }.append("、儿童隐私协议").setForegroundColor(linkColor).setClickSpan(linkColor, false) {
        HRouter.navigation(PrivacyPolicyConstants.CHILDREN_PRIVACY_AGREEMENT, Bundle().apply {
            putString("title", "儿童隐私协议")
        })
    }.create()
}