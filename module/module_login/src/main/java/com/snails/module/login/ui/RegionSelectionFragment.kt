package com.snails.module.login.ui

import androidx.fragment.app.activityViewModels
import androidx.navigation.Navigation
import com.snails.base.network.repository.info.login.PhoneAreaCodInfo
import com.snails.module.base.BaseStateFragment
import com.snails.module.login.databinding.FragmentRegionSelectionBinding
import com.snails.module.login.viewmodel.LoginViewModel
import com.snails.module.login.widget.RegionSelectionView

/**
 * 选择国家或者地区
 */
class RegionSelectionFragment : BaseStateFragment<FragmentRegionSelectionBinding>() {
    private val loginViewModel: LoginViewModel by activityViewModels()

    override fun initView() {
        binding.regionSelectionView.let {
            it.itemClickListener = object : RegionSelectionView.ItemClickListener {
                override fun itemClick(item: PhoneAreaCodInfo) {
                    loginViewModel.currentSelectedRegion.postValue(item)
                    //返回数据并关闭页面
                    Navigation.findNavController(it).popBackStack()
                }
            }
        }
    }

    override fun createViewModel() = loginViewModel

    override fun initData() {
        loginViewModel.getRegionSelectionData()
    }

    override fun initObserve() {
        loginViewModel.regionSelectionLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                binding.regionSelectionView.setData(it)
            }
        }
    }

    override fun initClick() {
        binding.commonTitleView.setBackClickListener { v ->
            Navigation.findNavController(v).popBackStack()
        }
    }
}