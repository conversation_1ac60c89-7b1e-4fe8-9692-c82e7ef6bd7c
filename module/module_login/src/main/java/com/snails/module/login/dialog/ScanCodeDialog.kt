package com.snails.module.login.dialog

import android.content.DialogInterface
import android.os.Bundle
import androidx.navigation.findNavController
import com.snails.base.dialog.BaseDialog
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.base.wechat.listener.AuthFinishListener
import com.snails.module.login.R
import com.snails.module.login.bean.ScanCodeState
import com.snails.module.login.databinding.DialogScanCodeBinding
import com.snails.module.login.viewmodel.LoginViewModel

/**
 * @Description 隐私协议弹窗
 * <AUTHOR>
 * @CreateTime 2024年08月16日 10:10:24
 */
class ScanCodeDialog() : BaseDialog<DialogScanCodeBinding>() {
    private var loginViewModel: LoginViewModel? = null

    constructor(
        loginViewModel: LoginViewModel
    ) : this() {
        this.loginViewModel = loginViewModel
    }

    var authFinishListener: AuthFinishListener? = null

    override fun initData() {
        loginViewModel?.getWeChatCodeSignature()
    }

    override fun initObserve() {
        loginViewModel?.scanCodeStateLiveData?.observe(viewLifecycleOwner) {
            setScanCodeState(it)
        }
        loginViewModel?.scanCodeBitmapLiveData?.observe(viewLifecycleOwner) {
            binding.ivScanCodeState.gone()
            binding.ivScanCode.setImageBitmap(it)
        }
        loginViewModel?.loginInfoLiveData?.observe(viewLifecycleOwner) { loginInfo ->
            loginInfo?.let {
                if (it.status == 1) {
                    //未绑定手机号
                    binding.ivScanCode.findNavController()
                        .navigate(R.id.action_loginFragment_to_bindPhoneFragment, Bundle().apply {
                            this.putString("openId", it.openId)
                        })
                } else {
                    //跳转主页
                    HRouter.navigation(RouterPath.MAIN_BASE)
                    requireActivity().finish()
                }
                dismiss()
            }
        }
        loginViewModel?.authFinishLiveData?.observe(viewLifecycleOwner) {
            authFinishListener?.authFinish(it)
            dismiss()
        }
    }

    override fun initClick() {
        binding.ivCloseDialog.singleClick {
            dismiss()
        }
        binding.ivScanCodeState.singleClick {
            if (loginViewModel?.scanCodeStateLiveData?.value == ScanCodeState.LOAD_FAILED) {
                loginViewModel?.getWeChatCodeSignature()
            }
        }
    }

    private fun setScanCodeState(state: ScanCodeState) {
        binding.ivScanCodeState.visible()
        binding.ivScanCode.setBackgroundResource(R.drawable.svg_login_scan_code)
        when (state) {
            ScanCodeState.LOADING -> {
                //加载中
                binding.ivScanCodeState.setBackgroundResource(R.drawable.svg_scan_code_loading)
            }

            ScanCodeState.LOAD_FAILED -> {
                //加载失败
                binding.ivScanCodeState.setBackgroundResource(R.drawable.svg_scan_code_retry)
            }

            ScanCodeState.SCAN_SUCCEED -> {
                //扫码成功
                binding.ivScanCodeState.setBackgroundResource(R.drawable.svg_scan_code_succeed)
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        authFinishListener = null
    }

    override fun dismiss() {
        super.dismiss()
        authFinishListener = null
    }
}