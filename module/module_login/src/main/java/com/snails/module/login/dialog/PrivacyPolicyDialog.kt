package com.snails.module.login.dialog

import android.content.DialogInterface
import android.graphics.Color
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.blankj.utilcode.util.SpanUtils
import com.snails.base.dialog.BaseDialog
import com.snails.base.network.ext.PrivacyPolicyConstants
import com.snails.base.network.repository.storage.OneKeyLoginStorage
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.singleClick
import com.snails.module.login.R
import com.snails.module.login.databinding.DialogPrivacyPolicyBinding

/**
 * @Description 隐私协议弹窗
 * <AUTHOR>
 * @CreateTime 2024年08月16日 10:10:24
 */
class PrivacyPolicyDialog() : BaseDialog<DialogPrivacyPolicyBinding>() {

    private var callBack: (() -> Unit)? = null
    private var close: (() -> Unit)? = null

    constructor(callBack: () -> Unit, close: (() -> Unit)? = null) : this() {
        this.callBack = callBack
        this.close = close
    }

    override fun initView() {
        buildSpanned()
    }

    override fun initClick() {
        binding.btnClose.singleClick {
            dismiss()
            close?.invoke()
        }

        binding.tvAgree.singleClick {
            callBack?.invoke()
            dismiss()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialog?.apply {
            setCanceledOnTouchOutside(false)//点击屏幕不消失
            setOnKeyListener(DialogInterface.OnKeyListener { _, keyCode, _ ->
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    return@OnKeyListener true
                }
                false
            })
        }
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    private fun buildSpanned() {
        val ctx = context ?: return
        val describeColor = ctx.getColor(R.color.text_describe)
        val linkColor = ctx.getColor(R.color.text_link)
        binding.tvTipsDesc.highlightColor = Color.TRANSPARENT
        val oneKeyLoginInfo = OneKeyLoginStorage.me.getOneKeyLoginInfo()
        SpanUtils.with(binding.tvTipsDesc)
            .append("欢迎使用蜗牛阅读，为保证您的合法权益，请仔细阅读并理解")
            .setForegroundColor(describeColor)
            .apply {
                if (oneKeyLoginInfo?.protocolName?.isNotEmpty() == true) {
                    this.append("《${oneKeyLoginInfo.protocolName}》、").setForegroundColor(linkColor)
                        .setClickSpan(linkColor, false) {
                            oneKeyLoginInfo.protocolUrl.let { it1 ->
                                HRouter.navigation(
                                    it1,
                                    Bundle().apply {
                                        putString("title", oneKeyLoginInfo.protocolName)
                                    }
                                )
                            }
                        }
                }
            }
            .append("《用户服务协议》、").setForegroundColor(linkColor)
            .setClickSpan(linkColor, false) {
                HRouter.navigation(PrivacyPolicyConstants.USER_SERVICE_AGREEMENT, Bundle().apply {
                    putString("title", "用户服务协议")
                })
            }
            .append("《用户隐私协议》").setForegroundColor(linkColor)
            .setClickSpan(linkColor, false) {
                HRouter.navigation(PrivacyPolicyConstants.USER_PRIVACY_AGREEMENT, Bundle().apply {
                    putString("title", "用户隐私协议")
                })
            }
            .append("、《儿童隐私协议》").setForegroundColor(linkColor)
            .setClickSpan(linkColor, false) {
                HRouter.navigation(
                    PrivacyPolicyConstants.CHILDREN_PRIVACY_AGREEMENT,
                    Bundle().apply {
                        putString("title", "儿童隐私协议")
                    })
            }
            .append("。为了继续使用我们的服务，请点击“同意”以表示您接受这些条款")
            .setForegroundColor(describeColor).setClickSpan(describeColor, false) {
            }
            .create()
    }
}