package com.snails.module.login

import android.content.Intent
import android.content.pm.ActivityInfo
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.module.base.BaseVBActivity
import com.snails.module.login.databinding.ActivityLoginBinding
import com.therouter.router.Route

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月12日 16:30:06
 */
@Route(path = RouterPath.BASE_LOGIN)
class LoginActivity : BaseVBActivity<ActivityLoginBinding>() {
    override fun initData() {
        checkRouter(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.let {
            checkRouter(it)
        }
    }

    private fun checkRouter(intent: Intent) {
        HRouter.getOriginalPath(intent)?.let { targetFragmentPath ->
            dealRouter(targetFragmentPath)
        }
    }

    private fun dealRouter(fragmentPath: String?) {
        val navController =
            binding.navHostFragment.getFragment<NavHostFragment>().findNavController()
        val navGraph = navController.navInflater.inflate(R.navigation.login_navigation)
        val startDestinationId = when (fragmentPath) {
            RouterPath.LOGIN_HOME -> {
                //一键登录
                R.id.loginFragment
            }

            RouterPath.PHONE_LOGIN -> {
                //手机号登录注册
                R.id.phoneLoginRegisterFragment
            }

            RouterPath.PHONE_BIND -> {
                //绑定手机号
                R.id.bindPhoneFragment
            }

            else -> {
                //手机号登录注册
                R.id.phoneLoginRegisterFragment
            }
        }
        navGraph.setStartDestination(startDestinationId)
        navController.graph = navGraph
    }

    override fun setRequestedOrientation(requestedOrientation: Int) {
        super.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED)
    }
}