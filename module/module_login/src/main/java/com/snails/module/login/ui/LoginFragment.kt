package com.snails.module.login.ui

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.Navigation
import com.blankj.utilcode.util.StringUtils
import com.blankj.utilcode.util.ToastUtils
import com.snails.base.network.repository.storage.OneKeyLoginStorage
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.base.utils.ext.singleClick
import com.snails.base.wechat.WeChatClient
import com.snails.base.wechat.listener.AuthFinishListener
import com.snails.base.wechat.listener.OnWeChatAuthLoginListener
import com.snails.base.wechat.utils.WeChatUtils
import com.snails.module.base.BaseVBFragment
import com.snails.module.login.BuildConfig
import com.snails.module.login.R
import com.snails.module.login.databinding.FragmentLoginBinding
import com.snails.module.login.dialog.IdentityChoiceDialog
import com.snails.module.login.dialog.PrivacyPolicyDialog
import com.snails.module.login.dialog.ScanCodeDialog
import com.snails.module.login.ext.setPrivacyPolicy
import com.snails.module.login.viewmodel.LoginViewModel

/**
 * 登录主界面，一键登录
 */
class LoginFragment : BaseVBFragment<FragmentLoginBinding>() {

    private var clickCount = 0
    private val clickThreshold = 5 // 连续点击五次
    private var lastClickTime: Long = 0
    private val clickInterval: Long = 300 // 设置点击间隔为300毫秒

    private val loginViewModel: LoginViewModel by activityViewModels()
    private var scanCodeDialog: ScanCodeDialog? = null

    override fun createViewModel() = loginViewModel

    override fun initView() {
        val oneKeyLoginInfo = OneKeyLoginStorage.me.getOneKeyLoginInfo()
        binding.tvLoginAllAgreement.setPrivacyPolicy(context, oneKeyLoginInfo)
        binding.tvLoginPhone.text = oneKeyLoginInfo?.number
        binding.tvLoginServiceProviders.text = getServiceProvider(oneKeyLoginInfo?.telecom)
    }

    /**
     * @param telecom 运营商类型,CMCC（移动）；CUCC（联通）；CTCC（电信）；UNKNOWN_OPERATOR（未知）
     */
    private fun getServiceProvider(telecom: String?): String {
        return when (telecom) {
            "CMCC" -> {
                "中国移动提供认证服务"
            }

            "CUCC" -> {
                "中国联通提供认证服务"
            }

            "CTCC" -> {
                "中国电信提供认证服务"
            }

            else -> {
                ""
            }
        }
    }

    override fun initClick() {
        //一键登录
        binding.tvLoginOneClickLogin.singleClick {
            oneClickLogin()
        }
        //其他手机号登录
        binding.tvLoginOtherPhoneLogin.singleClick {
            Navigation.findNavController(binding.tvLoginOtherPhoneLogin)
                .navigate(R.id.action_loginFragment_to_phoneLoginRegisterFragment)
        }
        //微信登录
        binding.ivLoginWeChat.singleClick {
            //是否安装微信
            weChatLogin()
        }
        //微信扫码登录
        binding.ivLoginWeChatQrCode.singleClick {
            showWeChatCodeDialog()
        }

        binding.ivLoginLogo.setOnClickListener {
            if (BuildConfig.BUILD_TYPE != "release") {
                val currentTime = System.currentTimeMillis()

                // 如果两次点击的时间差超过设置的点击间隔，重置点击次数
                if (currentTime - lastClickTime > clickInterval) {
                    clickCount = 0
                }

                lastClickTime = currentTime
                clickCount++

                if (clickCount >= clickThreshold) {
                    // 达到连续点击5次，执行事件
                    HRouter.navigation(RouterPath.ENVIRONMENT_SETTING)
                    // 重置点击计数器
                    clickCount = 0
                }
            }
        }
    }

    override fun initObserve() {
        loginViewModel.loginInfoLiveData.observe(viewLifecycleOwner) { loginInfo ->
            loginInfo?.let {
                if (it.status == 1) {
                    //未绑定手机号
                    Navigation.findNavController(binding.ivLoginWeChat)
                        .navigate(R.id.action_loginFragment_to_bindPhoneFragment, Bundle().apply {
                            this.putString("openId", it.openId)
                        })
                } else {
                    if (it.token.basicInfo.isTeacher()) {
                        IdentityChoiceDialog { isTeacher ->
                            if (isTeacher) {
                                jumpTeacherHome()
                            } else {
                                jumpHome()
                            }
                        }.show(childFragmentManager, "")
                    } else {
                        jumpHome()
                    }
                }
            }
        }
    }

    /**
     * 微信登录
     */
    private fun weChatLogin() {
        checkPrivacyPolicy { agree ->
            if (agree) {
                //是否安装微信
                if (WeChatUtils.isInstalled()) {
                    WeChatClient.authLogin(
                        object : OnWeChatAuthLoginListener {
                            override fun onWeChatAuthLoginSuccess(code: String) {
                                loginViewModel.weChatLogin(code)
                            }

                            override fun onWeChatAuthLoginCancel() {
                                ToastUtils.showShort(StringUtils.getString(R.string.str_wechat_login_cancel))
                            }

                            override fun onWeChatAuthLoginAuthDenied() {
                                ToastUtils.showShort(StringUtils.getString(R.string.str_wechat_login_denied))
                            }

                            override fun onWeChatAuthLoginError(
                                errorCode: Int?,
                                errorMessage: String?
                            ) {
                                ToastUtils.showShort(StringUtils.getString(R.string.str_wechat_login_error))
                            }
                        }
                    )
                } else {
                    //未安装微信
                    showWeChatCodeDialog()
                }
            }
        }
    }

    /**
     * 显示微信扫码登录弹窗
     */
    private fun showWeChatCodeDialog() {
        checkPrivacyPolicy { agree ->
            if (agree) {
                if (scanCodeDialog == null) {
                    scanCodeDialog = ScanCodeDialog(loginViewModel)
                }
                scanCodeDialog?.authFinishListener = object : AuthFinishListener {
                    override fun authFinish(authCode: String) {
                        loginViewModel.weChatLogin(authCode)
                    }
                }
                scanCodeDialog?.show(childFragmentManager, "")
            }
        }
    }

    /**
     * 一键登录
     */
    private fun oneClickLogin() {
        checkPrivacyPolicy { agree ->
            if (agree) {
                loginViewModel.oneClickLogin { isTeacher ->
                    if (isTeacher == true) {
                        IdentityChoiceDialog {
                            if (it) {
                                jumpTeacherHome()
                            } else {
                                jumpHome()
                            }
                        }.show(childFragmentManager, "")
                    } else {
                        jumpHome()
                    }
                }
            }
        }
    }

    /**
     * 跳转主页
     */
    private fun jumpHome() {
        HRouter.navigation(RouterPath.MAIN_BASE)
        requireActivity().finish()
    }

    /**
     * 跳转教师端主页
     */
    private fun jumpTeacherHome() {
        HRouter.navigation(RouterPath.TEACHER_MAIN_BASE)
        requireActivity().finish()
    }

    /**
     * 检查是否同意协议
     */
    private fun checkPrivacyPolicy(callBack: (Boolean) -> Unit) {
        if (binding.cbLoginAgreeAllAgreement.isChecked) {
            callBack.invoke(true)
            return
        }
        PrivacyPolicyDialog(
            callBack = {
                binding.cbLoginAgreeAllAgreement.isChecked = true
                callBack.invoke(true)
            }
        ).show(childFragmentManager, "")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        scanCodeDialog?.dismiss()
        scanCodeDialog = null
    }
}