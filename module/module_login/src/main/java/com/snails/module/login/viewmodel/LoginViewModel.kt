package com.snails.module.login.viewmodel

import android.graphics.Bitmap
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.ImageUtils
import com.blankj.utilcode.util.StringUtils
import com.blankj.utilcode.util.ToastUtils
import com.chuanglan.shanyan_sdk.OneKeyLoginManager
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.info.login.WeChatLoginInfo
import com.snails.base.network.repository.info.login.PhoneAreaCodInfo
import com.snails.base.network.repository.info.login.WeScanCodeSignatureInfo
import com.snails.base.wechat.WeChatClient
import com.snails.base.wechat.listener.WeChatScanCodeListener
import com.snails.module.base.BaseViewModel
import com.snails.module.base.bean.StateType
import com.snails.module.base.utils.SingleLiveEventLiveData
import com.snails.module.login.R
import com.snails.module.login.bean.ScanCodeState
import com.tencent.mm.opensdk.diffdev.OAuthErrCode
import org.json.JSONObject

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月16日 18:22:12
 */
class LoginViewModel : BaseViewModel() {

    //验证码获取是否成功
    val verificationCodeLiveData: MutableLiveData<Boolean> = SingleLiveEventLiveData()

    //国家或地区列表数据
    val regionSelectionLiveData: MutableLiveData<List<PhoneAreaCodInfo>?> =
        SingleLiveEventLiveData()

    //当前选中的国家或地区
    val currentSelectedRegion: MutableLiveData<PhoneAreaCodInfo> = SingleLiveEventLiveData()

    //登录信息
    val loginInfoLiveData: MutableLiveData<WeChatLoginInfo?> = SingleLiveEventLiveData()

    // 微信扫码状态
    val scanCodeStateLiveData: MutableLiveData<ScanCodeState> = SingleLiveEventLiveData()

    // 微信二维码
    val scanCodeBitmapLiveData: MutableLiveData<Bitmap> = SingleLiveEventLiveData()

    // 微信授权完成
    val authFinishLiveData: MutableLiveData<String> = SingleLiveEventLiveData()

    /**
     * 一键登录
     */
    fun oneClickLogin(success: ((Boolean?) -> Unit)? = null) {
        loadingDialogLiveData.value = true
        OneKeyLoginManager.getInstance().loginAuth { code, result ->
            if (code == 1000) { //code为1000：获取token成功 其他：失败
                val token = JSONObject(result).getString("token")
                reallyOneKeyLogin(token, success)
            } else {
                //登录失败
                ToastUtils.showShort(StringUtils.getString(R.string.str_login_failed_retry))
                loadingDialogLiveData.value = false
            }
        }
    }

    /**
     * @param token 一键登录 token
     */
    private fun reallyOneKeyLogin(token: String, success: ((Boolean?) -> Unit)? = null) {
        request(
            stateType = StateType.DIALOG,
            request = {
                SnailRepository.me.oneKeyLogin(token)
            },
            success = {
                success?.invoke(it?.basicInfo?.isTeacher())
            },
            failed = {
                //登录失败
                ToastUtils.showShort(it)
            }
        )
    }

    /**
     * 手机号+验证码登陆,手机号格式：86-13712120099
     * @param phone 手机号
     * @param countryCode 区号
     * @param code 验证码
     * 渠道 默认 APP
     */
    fun loginSms(
        phone: String,
        countryCode: String,
        code: String,
        success: ((Boolean?) -> Unit)? = null
    ) {
        request(
            request = {
                SnailRepository.me.loginSms("$countryCode-$phone", code)
            },
            success = {
                success?.invoke(it?.basicInfo?.isTeacher())
            },
            failed = {
                //登录失败
                ToastUtils.showShort(it)
            }
        )
    }

    /**
     * 获取手机验证码,手机号格式：86-13712120099
     * @param phone 手机号
     */
    fun getGetVerificationCode(phone: String, countryCode: String) {
        request(
            request = {
                SnailRepository.me.sendSms("$countryCode-$phone")
            },
            success = {
                verificationCodeLiveData.value = true
            },
            failed = {
                ToastUtils.showShort(it)
            }
        )
    }

    /**
     * 获取国际电话区号，如中国：+86
     */
    fun getRegionSelectionData() {
        request(
            stateType = StateType.PAGE,
            request = {
                val data = SnailRepository.me.getPhoneAreaCode()
                if (currentSelectedRegion.value == null) {
                    data.data?.firstOrNull { it.prefix == "86" }?.let {
                        it.selected = true
                        currentSelectedRegion.postValue(it)
                    }
                } else {
                    data.data?.firstOrNull {
                        it.prefix == currentSelectedRegion.value?.prefix
                                && it.countryName == currentSelectedRegion.value?.countryName
                    }
                        ?.let {
                            it.selected = true
                        }
                }
                data
            },
            success = {
                regionSelectionLiveData.value = it
            }
        )
    }

    /**
     * 获取微信二维码签名
     */
    fun getWeChatCodeSignature() {
        request(
            stateType = StateType.NONE,
            showLoading = {
                scanCodeStateLiveData.value = ScanCodeState.LOADING
            },
            request = {
                SnailRepository.me.getWeChatScanCodeSignature()
            },
            success = { data ->
                if (data != null) {
                    authLogin(data)
                }
            },
            failed = { _ ->
                scanCodeStateLiveData.value = ScanCodeState.LOAD_FAILED
            }
        )
    }

    private fun authLogin(it: WeScanCodeSignatureInfo) {
        WeChatClient.authLoginCode(
            it.appId,
            it.scope,
            it.nonceStr,
            it.timestamp,
            it.signature,
            object : WeChatScanCodeListener {
                override fun onAuthGotQrcode(byteArray: ByteArray?) {
                    if (byteArray == null) {
                        scanCodeStateLiveData.value = ScanCodeState.LOAD_FAILED
                    } else {
                        try {
                            val bytes2Bitmap = ImageUtils.bytes2Bitmap(byteArray)
                            scanCodeBitmapLiveData.value = bytes2Bitmap
                        } catch (t: Throwable) {
                            scanCodeStateLiveData.value = ScanCodeState.LOAD_FAILED
                        }
                    }
                }

                override fun onQrcodeScanned() {
                    scanCodeStateLiveData.value = ScanCodeState.SCAN_SUCCEED
                }

                override fun onAuthFinish(p0: OAuthErrCode?, p1: String?) {
                    if (p0?.code == 0) {
                        p1?.let {
                            authFinishLiveData.value = it
                        }
                    } else {
                        scanCodeStateLiveData.value = ScanCodeState.LOAD_FAILED
                    }
                }
            }
        )
    }

    /**
     * 微信登录
     * @param code 微信授权 code
     */
    fun weChatLogin(code: String) {
        request(
            stateType = StateType.DIALOG,
            request = {
                SnailRepository.me.weChatLogin(code)
            },
            success = {
                loginInfoLiveData.value = it
            },
            failed = {
                //登录失败
                ToastUtils.showShort(it)
            }
        )
    }

    /**
     * 绑定手机号
     * @param phone 手机号
     * @param code 验证码
     * @param openId 微信 openid
     */
    fun bindPhone(
        phone: String,
        code: String,
        openId: String,
        success: ((Boolean?) -> Unit)? = null
    ) {
        request(
            stateType = StateType.DIALOG,
            request = {
                SnailRepository.me.bindPhone(phone, code, openId)
            },
            success = {
                success?.invoke(it?.basicInfo?.isTeacher())
            },
            failed = {
                ToastUtils.showShort(it)
            }
        )
    }
}