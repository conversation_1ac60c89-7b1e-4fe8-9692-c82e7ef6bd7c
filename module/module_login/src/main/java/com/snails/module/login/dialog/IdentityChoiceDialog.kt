package com.snails.module.login.dialog

import android.os.Bundle
import android.view.View
import com.snails.base.dialog.BaseDialog
import com.snails.base.network.repository.storage.UserStorage
import com.snails.base.utils.ext.singleClick
import com.snails.module.login.R
import com.snails.module.login.databinding.DialogIdentityChoiceBinding

/**
 * @Description 身份选择弹窗
 * <AUTHOR>
 * @CreateTime 2025年01月08日 16:04:26
 */
class IdentityChoiceDialog() : BaseDialog<DialogIdentityChoiceBinding>() {

    private var isTeacher = true

    private var confirm: ((Bo<PERSON>an) -> Unit)? = null

    constructor(confirm: (Boolean) -> Unit) : this() {
        this.confirm = confirm
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setCanceledOnTouchOutside(false)
        isCancelable = false
    }

    override fun initClick() {
        super.initClick()
        binding.apply {
            vTeacher.singleClick {
                isTeacher = true
                vTeacher.setBackgroundResource(R.drawable.shape_identity_choice_bg)
                cbTeacher.isChecked = true

                vStudent.setBackgroundResource(R.drawable.shape_identity_choice_unselect_bg)
                cbStudent.isChecked = false
            }
            vStudent.singleClick {
                isTeacher = false
                vTeacher.setBackgroundResource(R.drawable.shape_identity_choice_unselect_bg)
                cbTeacher.isChecked = false

                vStudent.setBackgroundResource(R.drawable.shape_identity_choice_bg)
                cbStudent.isChecked = true
            }

            tvConfirm.singleClick {
                UserStorage.me.setIsTeacher(isTeacher)
                confirm?.invoke(isTeacher)
                dismiss()
            }
        }
    }
}