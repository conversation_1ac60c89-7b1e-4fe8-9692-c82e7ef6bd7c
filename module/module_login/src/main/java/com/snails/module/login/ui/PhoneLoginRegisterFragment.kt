package com.snails.module.login.ui

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.Navigation
import com.blankj.utilcode.util.StringUtils
import com.blankj.utilcode.util.ToastUtils
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.base.utils.ext.singleClick
import com.snails.base.wechat.WeChatClient
import com.snails.base.wechat.listener.AuthFinishListener
import com.snails.base.wechat.listener.OnWeChatAuthLoginListener
import com.snails.base.wechat.utils.WeChatUtils
import com.snails.module.base.BaseVBFragment
import com.snails.module.login.BuildConfig
import com.snails.module.login.R
import com.snails.module.login.databinding.FragmentPhoneLoginRegisterBinding
import com.snails.module.login.dialog.IdentityChoiceDialog
import com.snails.module.login.dialog.PrivacyPolicyDialog
import com.snails.module.login.dialog.ScanCodeDialog
import com.snails.module.login.ext.setPrivacyPolicy
import com.snails.module.login.viewmodel.LoginViewModel

class PhoneLoginRegisterFragment : BaseVBFragment<FragmentPhoneLoginRegisterBinding>() {


    private val loginViewModel: LoginViewModel by activityViewModels()
    private var scanCodeDialog: ScanCodeDialog? = null

    override fun initData() {
        loginViewModel.currentSelectedRegion.value?.let {
            it.prefix?.let { it1 -> binding.phoneNumberInputView.setCountryCode(it1) }
        }
    }

    override fun initView() {
        binding.phoneNumberInputView.apply {
            //设置区号选择点击事件
            setOnCountryCodeClickListener { v ->
//                Navigation.findNavController(v)
//                    .navigate(R.id.action_phoneLoginRegisterFragment_to_regionSelectionFragment)
            }
            //监听手机号输入改变
            addPhoneChangeListener { enable ->
                //改变获取验证码按钮状态
                binding.verificationCodeView.changeOperateVerificationCodeState(enable)
                //改变登录注册按钮状态
                changeLoginRegisterBtnState()
            }
        }

        binding.verificationCodeView.apply {
            //设置获取验证码点击事件
            setGetVerificationCodeClickListener {
                val phone = binding.phoneNumberInputView.getPhoneNumber()
                val countryCode = binding.phoneNumberInputView.getCountryCode()
                loginViewModel.getGetVerificationCode(phone, countryCode)
            }
            //监听验证码输入改变
            addVerificationCodeChangeListener {
                //改变登录注册按钮状态
                changeLoginRegisterBtnState()
            }
        }
        binding.tvLoginAllAgreement.setPrivacyPolicy(context)
    }

    override fun initClick() {
        binding.commonTitleView.setBackClickListener { v ->
            if (!Navigation.findNavController(v).popBackStack()) {
                activity?.finish()
            }
        }
        binding.tvLoginRegister.singleClick {
            login()
        }
        //微信登录
        binding.ivLoginWeChat.singleClick {
            //是否安装微信
            weChatLogin()
        }
        //微信扫码登录
        binding.ivLoginWeChatQrCode.singleClick {
            showWeChatCodeDialog()
        }

        binding.tvLoginOrRegister.singleClick {
            if (BuildConfig.BUILD_TYPE != "release") {
                // 达到连续点击5次，执行事件
                HRouter.navigation(RouterPath.ENVIRONMENT_SETTING)
            }
        }
    }

    private fun login() {
        checkPrivacyPolicy { agree ->
            if (agree) {
                val phone = binding.phoneNumberInputView.getPhoneNumber()
                val code = binding.verificationCodeView.getVerificationCode()
                val countryCode = binding.phoneNumberInputView.getCountryCode()
                loginViewModel.loginSms(phone, countryCode, code) { isTeacher ->
                    if (isTeacher == true) {
                        IdentityChoiceDialog { teacher ->
                            if (teacher) {
                                jumpTeacherHome()
                            } else {
                                jumpHome()
                            }
                        }.show(childFragmentManager, "")
                    } else {
                        jumpHome()
                    }
                }
            }
        }
    }

    /**
     * 微信登录
     */
    private fun weChatLogin() {
        checkPrivacyPolicy { agree ->
            if (agree) {
                //是否安装微信
                if (WeChatUtils.isInstalled()) {
                    WeChatClient.authLogin(
                        object : OnWeChatAuthLoginListener {
                            override fun onWeChatAuthLoginSuccess(code: String) {
                                loginViewModel.weChatLogin(code)
                            }

                            override fun onWeChatAuthLoginCancel() {
                                ToastUtils.showShort(StringUtils.getString(R.string.str_wechat_login_cancel))
                            }

                            override fun onWeChatAuthLoginAuthDenied() {
                                ToastUtils.showShort(StringUtils.getString(R.string.str_wechat_login_denied))
                            }

                            override fun onWeChatAuthLoginError(
                                errorCode: Int?,
                                errorMessage: String?
                            ) {
                                ToastUtils.showShort(StringUtils.getString(R.string.str_wechat_login_error))
                            }
                        }
                    )
                } else {
                    //未安装微信
                    showWeChatCodeDialog()
                }
            }
        }
    }

    /**
     * 显示微信扫码登录弹窗
     */
    private fun showWeChatCodeDialog() {
        checkPrivacyPolicy { agree ->
            if (agree) {
                if (scanCodeDialog == null) {
                    scanCodeDialog = ScanCodeDialog(loginViewModel)
                }
                scanCodeDialog?.authFinishListener = object : AuthFinishListener {
                    override fun authFinish(authCode: String) {
                        loginViewModel.weChatLogin(authCode)
                    }
                }
                scanCodeDialog?.show(childFragmentManager, "")
            }
        }
    }

    private fun checkPrivacyPolicy(callBack: (Boolean) -> Unit) {
        if (binding.cbLoginAgreeAllAgreement.isChecked) {
            callBack.invoke(true)
            return
        }
        PrivacyPolicyDialog(
            callBack = {
                binding.cbLoginAgreeAllAgreement.isChecked = true
                callBack.invoke(true)
            }
        ).show(childFragmentManager, "")
    }

    override fun initObserve() {
        loginViewModel.verificationCodeLiveData.observe(viewLifecycleOwner) {
            if (it) {
                binding.verificationCodeView.startCountdown(lifecycleScope)
            }
        }

        loginViewModel.currentSelectedRegion.observe(viewLifecycleOwner) {
            it.prefix?.let { it1 -> binding.phoneNumberInputView.setCountryCode(it1) }
        }

        loginViewModel.loginInfoLiveData.observe(viewLifecycleOwner) { loginInfo ->
            loginInfo?.let {
                if (it.status == 1) {
                    //未绑定手机号
                    Navigation.findNavController(binding.ivLoginWeChat)
                        .navigate(
                            R.id.action_phoneLoginRegisterFragment_to_bindPhoneFragment,
                            Bundle().apply {
                                this.putString("openId", it.openId)
                            })
                } else {
                    if (it.token.basicInfo.isTeacher()) {
                        IdentityChoiceDialog { isTeacher ->
                            if (isTeacher) {
                                jumpTeacherHome()
                            } else {
                                jumpHome()
                            }
                        }.show(childFragmentManager, "")
                    } else {
                        jumpHome()
                    }
                }
            }
        }
    }

    /**
     * 跳转主页
     */
    private fun jumpHome() {
        HRouter.navigation(RouterPath.MAIN_BASE)
        requireActivity().finish()
    }

    /**
     * 跳转教师端主页
     */
    private fun jumpTeacherHome() {
        HRouter.navigation(RouterPath.TEACHER_MAIN_BASE)
        requireActivity().finish()
    }

    /**
     * 改变登录注册按钮状态
     */
    private fun changeLoginRegisterBtnState() {
        val hadPhone = binding.phoneNumberInputView.hadPhone()
        val hadCode = binding.verificationCodeView.hadCode()
        binding.tvLoginRegister.isEnabled = (hadPhone && hadCode)
    }
}