{"v": "5.12.1", "fr": 30, "ip": 0, "op": 60, "w": 200, "h": 50, "nm": "Frame 3466312", "ddd": 0, "assets": [{"id": "comp_0", "nm": "Frame 3466312.png 合成 1", "fr": 30, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [194, 22.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 0, "op": 50, "st": -11, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [194, 22.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 50, "op": 61, "st": 50, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [177, 23.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 0, "op": 40, "st": -21, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [177, 23.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 40, "op": 61, "st": 40, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [160.25, 23, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 0, "op": 30, "st": -31, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [160.25, 23, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 30, "op": 61, "st": 30, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [143, 23.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 0, "op": 20, "st": -41, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [143, 23.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 20, "op": 61, "st": 20, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [126, 24.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 0, "op": 10, "st": -51, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [126, 24.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 10, "op": 61, "st": 10, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [108.75, 23, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 0, "op": 50, "st": -11, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [108.75, 23, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 50, "op": 61, "st": 50, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [91.75, 23.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 0, "op": 40, "st": -21, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [91.75, 23.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 40, "op": 61, "st": 40, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [74.5, 24.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 0, "op": 30, "st": -31, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [74.5, 24.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 30, "op": 61, "st": 30, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 0, "op": 20, "st": -41, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 20, "op": 61, "st": 20, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [41.25, 24.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 0, "op": 10, "st": -51, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [41.25, 24.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 10, "op": 61, "st": 10, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [23.5, 24.25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 0, "op": 50, "st": -11, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [23.5, 24.25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 50, "op": 61, "st": 50, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [6.75, 23.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 0, "op": 40, "st": -21, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 0, "nm": "形状图层 1 合成 1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [6.75, 23.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.5, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 29, "h": 72, "ip": 40, "op": 61, "st": 40, "bm": 0}]}, {"id": "comp_1", "nm": "形状图层 1 合成 1", "fr": 30, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "形状图层 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [5.5, 36.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [-3.376, -0.061], [0, 0], [0, 0], [3.688, 0.224]], "o": [[0, 0], [0, 0], [3.374, 0.127], [0, 0], [0, 0], [-3.937, 0.13]], "v": [[4.375, -3.875], [4.345, 3.25], [8.314, 6.686], [12.47, 3], [12.562, -3.75], [8.499, -7.63]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [-3.376, -0.061], [0, 0], [0, 0], [3.688, 0.224]], "o": [[0, 0], [0, 0], [3.374, 0.127], [0, 0], [0, 0], [-3.937, 0.13]], "v": [[4.312, -19.25], [4.375, 18.875], [8.345, 22.311], [12.5, 18.625], [12.5, -19.125], [8.437, -23.005]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0], [-3.376, -0.061], [0, 0], [0, 0], [3.688, 0.224]], "o": [[0, 0], [0, 0], [3.374, 0.127], [0, 0], [0, 0], [-3.937, 0.13]], "v": [[4.375, -3.875], [4.345, 3.25], [8.314, 6.686], [12.47, 3], [12.562, -3.75], [8.499, -7.63]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 45, "s": [{"i": [[0, 0], [0, 0], [-3.376, -0.061], [0, 0], [0, 0], [3.688, 0.224]], "o": [[0, 0], [0, 0], [3.374, 0.127], [0, 0], [0, 0], [-3.937, 0.13]], "v": [[4.312, -19.25], [4.375, 18.875], [8.345, 22.311], [12.5, 18.625], [12.5, -19.125], [8.437, -23.005]], "c": true}]}, {"t": 60, "s": [{"i": [[0, 0], [0, 0], [-3.376, -0.061], [0, 0], [0, 0], [3.688, 0.224]], "o": [[0, 0], [0, 0], [3.374, 0.127], [0, 0], [0, 0], [-3.937, 0.13]], "v": [[4.375, -3.875], [4.345, 3.25], [8.314, 6.686], [12.47, 3], [12.562, -3.75], [8.499, -7.63]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.2, 0.670587995941, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 61, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Frame 3466312.png 合成 1", "cl": "png", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100, 25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 25, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 200, "h": 50, "ip": 0, "op": 60, "st": 0, "bm": 0}], "markers": [], "props": {}}