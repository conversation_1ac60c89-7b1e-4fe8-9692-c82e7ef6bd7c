package com.snails.module.course.ui

import androidx.fragment.app.viewModels
import androidx.navigation.findNavController
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.network.repository.info.course.CourseDetailsInfo
import com.snails.base.network.repository.storage.UserStorage
import com.snails.module.base.BaseStateFragment
import com.snails.module.course.databinding.FragmentSwitchCourseBinding
import com.snails.module.course.dialog.CourseLockedDialog
import com.snails.module.course.viewmodel.CourseViewModel
import com.snails.module.course.widget.CourseListView

/**
 * @Description 课程切换
 * <AUTHOR>
 * @CreateTime 2024年10月22日 10:39:12
 */
class SwitchCourseFragment : BaseStateFragment<FragmentSwitchCourseBinding>() {

    private val courseViewModel: CourseViewModel by viewModels()

    override fun createViewModel() = courseViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initClick() {
        binding.commonTitleView.setBackClickListener {
            val pop = it.findNavController().popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
        binding.courseListView.listener = object : CourseListView.ItemClickListener {
            override fun itemClick(data: CourseDetailsInfo) {
                if (data.courseStatus == "LOCKED") {
                    CourseLockedDialog().show(childFragmentManager, "")
                } else {
                    data.courseId?.let { UserStorage.me.setStudyCourseId(it) }
                    requireActivity().finish()
                }
            }
        }
    }

    override fun initData() {
        courseViewModel.courseStage = arguments?.getInt("courseStage") //from 学阶切换
        courseViewModel.courseType = arguments?.getString("courseType") //from 学阶切换
        if (courseViewModel.courseStage == null || courseViewModel.courseType == null) {
            courseViewModel.courseStage = activity?.intent?.getIntExtra("courseStage", -1) //from 学习主页
            courseViewModel.courseType = activity?.intent?.getStringExtra("courseType") //from 学习主页
        }
        courseViewModel.getCourseList()
    }

    override fun initObserve() {
        super.initObserve()
        courseViewModel.coursesLiveData.observe(viewLifecycleOwner) {
            binding.courseListView.setCourseList(it)
        }
    }

    override fun onRetry() {
        super.onRetry()
        courseViewModel.getCourseList()
    }
}