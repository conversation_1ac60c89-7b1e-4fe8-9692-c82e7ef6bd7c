package com.snails.module.course.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.course.CatalogInfo
import com.snails.module.course.viewbinder.SwitchPhaseViewBinder

/**
 * @Description 拓展页 UI
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */
class SwitchPhaseTabView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val listAdapter = MultiTypeAdapter()

    private val showTabList = mutableListOf<CatalogInfo>()

    var itemClickListener: ItemClickListener? = null

    @SuppressLint("NotifyDataSetChanged")
    private var switchPhaseViewBinder = SwitchPhaseViewBinder {
        itemClickListener?.itemClick(it)
        showTabList.forEach { info ->
            info.selected = it.courseType == info.courseType
        }
        listAdapter.items = showTabList
        listAdapter.notifyDataSetChanged()
    }

    init {
        initView()
    }

    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(CatalogInfo::class.java, switchPhaseViewBinder)
        }
        adapter = listAdapter
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setTabList(tabList: List<CatalogInfo>) {
        showTabList.clear()
        showTabList.addAll(tabList)
        listAdapter.items = tabList
        switchPhaseViewBinder.itemCount(tabList.size)
        listAdapter.notifyDataSetChanged()
    }

    interface ItemClickListener {
        fun itemClick(data: CatalogInfo)
    }
}