package com.snails.module.course.ui

import androidx.fragment.app.viewModels
import androidx.navigation.Navigation
import coil3.load
import com.blankj.utilcode.util.GsonUtils
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.network.repository.info.course.ResourceInfo
import com.snails.module.base.BaseStateFragment
import com.snails.module.course.databinding.FragmentCourseIntroductionBinding
import com.snails.module.course.viewmodel.CourseViewModel

/**
 * @Description 课程介绍
 * <AUTHOR>
 * @CreateTime 2025年02月05日 14:22:50
 */
class CourseIntroductionFragment : BaseStateFragment<FragmentCourseIntroductionBinding>() {

    private val courseViewModel: CourseViewModel by viewModels()

    override fun createViewModel() = courseViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initData() {
        super.initData()
        courseViewModel.contentId = getStringExtra("id")
        courseViewModel.getCourseIntroduction()
    }

    override fun initObserve() {
        super.initObserve()
        courseViewModel.contentDetailsInfoLiveData.observe(viewLifecycleOwner) { info ->
            if (info.resourceType == "PIC" && info.type == "SQUARE_COURSE_DESCRIPTION") {
                info.content?.let {
                    val resourceInfo = GsonUtils.fromJson(it, ResourceInfo::class.java)
                    binding.ivPic.load(resourceInfo.descriptionImageFileId)
                }
            }
        }
    }

    override fun initClick() {
        super.initClick()
        binding.commonTitleView.setBackClickListener {
            val pop = Navigation.findNavController(it).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
    }

    override fun onRetry() {
        super.onRetry()
        courseViewModel.getCourseIntroduction()
    }

}