package com.snails.module.course.ui.homework

import android.annotation.SuppressLint
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.viewModels
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.network.repository.info.teacher.HomeworkListDetailsInfo
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.addBackPop
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.BaseStateFragment
import com.snails.module.base.bean.StateType
import com.snails.module.course.R
import com.snails.module.course.databinding.FragmentMyHomeworkListBinding
import com.snails.module.course.viewmodel.CourseViewModel

/**
 * @Description 学生端-我的作业清单
 * <AUTHOR>
 * @CreateTime 2025年02月24日 14:33:29
 */
class MyHomeworkListFragment : BaseStateFragment<FragmentMyHomeworkListBinding>() {

    private val courseViewModel: CourseViewModel by viewModels()

    override fun createViewModel() = courseViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initData() {
        super.initData()
        courseViewModel.courseId = getStringExtra("courseId")

        courseViewModel.getMyHomeworkListItem(StateType.PAGE)
    }

    override fun initObserve() {
        super.initObserve()
        courseViewModel.homeworkDetailsLiveData.observe(viewLifecycleOwner) {
            initUI(it)
            it.items?.let { it1 -> binding.homeworkListView.setData(it1, true) }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun initUI(info: HomeworkListDetailsInfo) {
        binding.tvCourseInfo.text =
            "${info.courseTypeName}${info.courseStage}阶·第${info.courseNum}课"
        binding.tvCourseName.text = info.courseName ?: ""
        if (info.reportUrl.isNullOrEmpty() || info.score.isNullOrEmpty()) {
            binding.clyBottomContainer.gone()
        } else {
            binding.clyBottomContainer.apply {
                visible()
                singleClick {
                    info.reportUrl?.let {
                        HRouter.navigation(it)
                    }
                }
            }
        }
        info.score ?: return
        when (info.score) {
            "SSS" -> {
                binding.ivScore.setBackgroundResource(R.drawable.svg_score_sss)
            }

            "S" -> {
                binding.ivScore.setBackgroundResource(R.drawable.svg_score_s)
            }

            "A" -> {
                binding.ivScore.setBackgroundResource(R.drawable.svg_score_a)
            }

            "B" -> {
                binding.ivScore.setBackgroundResource(R.drawable.svg_score_b)
            }

            else -> {

            }
        }
    }

    override fun initClick() {
        super.initClick()

        binding.commonTitleView.setBackClickListener {
            it.addBackPop(requireActivity())
        }

        interceptBack()
    }

    /**
     * 拦截返回事件
     */
    private fun interceptBack() {
        requireActivity().onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    binding.commonTitleView.addBackPop(requireActivity())
                }
            }
        )
    }

    override fun onRetry() {
        super.onRetry()
        courseViewModel.getMyHomeworkListItem(stateType = StateType.PAGE)
    }
}