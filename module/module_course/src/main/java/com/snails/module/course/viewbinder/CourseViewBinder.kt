package com.snails.module.course.viewbinder

import android.annotation.SuppressLint
import android.graphics.Color
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.course.CourseDetailsInfo
import com.snails.base.network.repository.storage.UserStorage
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.invisible
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.course.R
import com.snails.module.course.databinding.ItemSwitchCourseLayoutBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:49:09
 */
class CourseViewBinder(private val itemClick: (CourseDetailsInfo) -> Unit) :
    ViewBindingDelegate<CourseDetailsInfo, ItemSwitchCourseLayoutBinding>() {

    private val studyCourseId = UserStorage.me.getStudyingCourseId()

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ItemSwitchCourseLayoutBinding>, item: CourseDetailsInfo
    ) {
        holder.binding.apply {
            if (item.courseCover.isNullOrEmpty()) {
                ivCourseCover.invisible()
            } else {
                ivCourseCover.visible()
                item.courseCover?.let {
                    ivCourseCover.load(
                        it,
                        placeholder = R.drawable.svg_w88_h88_placeholder,
                        error = R.drawable.svg_w88_h88_error
                    )
                }
            }
            tvCourseIndex.text = "第${item.courseNum}课"
            tvCourseName.text = item.courseName ?: ""
            tvCourseVocabulary.text = "词汇·${item.courseWordCount ?: "0"}"
            tvCoursePhrase.text = "短语·${item.coursePhraseCount ?: "0"}"
            if (item.courseStatus == "LOCKED") {
                vBg.setBackgroundColor(Color.TRANSPARENT)
                tvCourseIndex.setBackgroundResource(R.drawable.shape_course_index_gray)
                tvCourseIndex.setTextColor(
                    tvCourseIndex.context.resources.getColor(
                        R.color.text_describe, null
                    )
                )
                tvStudyCourse.gone()
                ivLock.visible()
                ivCourseCoverMask.visible()
                ivLockWhite.visible()
            } else {
                ivLock.gone()
                ivCourseCoverMask.gone()
                ivLockWhite.gone()
                if (item.courseId == studyCourseId) {
                    tvStudyCourse.visible()
                    tvCourseIndex.setBackgroundResource(R.drawable.shape_course_index)
                    tvCourseIndex.setTextColor(
                        tvCourseIndex.context.resources.getColor(
                            R.color.surface_primary_button_press, null
                        )
                    )
                    vBg.setBackgroundResource(R.drawable.shape_phase_complete_radius_12)
                } else {
                    tvStudyCourse.gone()
                    tvCourseIndex.setBackgroundResource(R.drawable.shape_course_index_gray)
                    vBg.setBackgroundColor(Color.TRANSPARENT)
                    tvCourseIndex.setTextColor(
                        tvCourseIndex.context.resources.getColor(
                            R.color.text_describe, null
                        )
                    )
                }
            }
        }
        holder.itemView.singleClick {
            itemClick.invoke(item)
        }
    }
}