package com.snails.module.course.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.course.PhaseInfo
import com.snails.module.course.R
import com.snails.module.course.viewbinder.PhaseViewBinder

/**
 * @Description 阶段列表数据
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */
class PhaseListView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val listAdapter = MultiTypeAdapter()
    var listener: ItemClickListener? = null

    init {
        initView()
    }

    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(PhaseInfo::class.java, PhaseViewBinder() {
                listener?.itemClick(it)
            })
        }
        adapter = listAdapter
        addItemDecoration(ItemDecoration())
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setPhaseList(tabList: List<PhaseInfo>) {
        listAdapter.items = tabList
        listAdapter.notifyDataSetChanged()
    }

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State
        ) {
            super.getItemOffsets(outRect, view, parent, state)

            outRect.top = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_12)
        }
    }

    interface ItemClickListener {
        fun itemClick(data: PhaseInfo)
    }
}