package com.snails.module.course.ui.activation

import android.graphics.Bitmap
import android.os.Build
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ImageUtils
import com.blankj.utilcode.util.ToastUtils
import com.gyf.immersionbar.ImmersionBar
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.snails.base.image_loader.load
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.BaseStateFragment
import com.snails.module.course.R
import com.snails.module.course.databinding.FragmentActivationResultBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * @Description 课程激活结果页
 * <AUTHOR>
 * @CreateTime 2024年09月09日 09:32:33
 */
class ActivationResultFragment : BaseStateFragment<FragmentActivationResultBinding>() {

    override fun initView() {
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
        getStringExtra("data")?.let { info ->
            binding.ivScanCode.load(info)
        }
    }

    override fun initClick() {
        binding.ivScanCode.setOnLongClickListener { //权限请求，
            context?.let { ctx ->
                val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    Permission.READ_MEDIA_IMAGES
                } else {
                    Permission.READ_EXTERNAL_STORAGE
                }
                XXPermissions.with(ctx).permission(permissions)
                    .request { _, allGranted ->
                        if (allGranted) {
                            savePicToAlbum()
                        }
                    }
            }
            true
        }

        binding.tvClose.singleClick {
            requireActivity().finish()
        }
    }

    /**
     * 保存图片到相册
     */
    private fun savePicToAlbum() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val view2Bitmap = ImageUtils.view2Bitmap(binding.ivScanCode)
                ImageUtils.save2Album(view2Bitmap, Bitmap.CompressFormat.PNG)
                // 在主线程中显示保存成功的 Toast
                withContext(Dispatchers.Main) {
                    ToastUtils.showShort(getString(R.string.str_save_succeed))
                }
            } catch (e: Exception) {
                // 在主线程中显示保存失败的 Toast
                withContext(Dispatchers.Main) {
                    ToastUtils.showShort(getString(R.string.str_save_failed))
                }
            }
        }
    }
}