package com.snails.module.course.viewbinder

import android.annotation.SuppressLint
import android.widget.TextView
import com.snails.base.network.repository.info.course.PhaseInfo
import com.snails.base.network.repository.storage.UserStorage
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.course.R
import com.snails.module.course.databinding.ItemPhaseLayoutBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:49:09
 */
class PhaseViewBinder(private val itemClick: (PhaseInfo) -> Unit) :
    ViewBindingDelegate<PhaseInfo, ItemPhaseLayoutBinding>() {

    private var info = UserStorage.me.getCurrentStudyInfo()

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ItemPhaseLayoutBinding>,
        item: PhaseInfo
    ) {
        holder.binding.apply {
            // 设置文本信息
            phaseLevel.text = "${item.courseStage}阶"
            tvCourseCount.text = item.courseCount?.toString() ?: "0"
            tvGameCount.text = item.gameCount?.toString() ?: "0"
            tvCourseVocabulary.text = "词汇·${item.wordCount ?: "0"}"
            tvCoursePhrase.text = "短语·${item.phraseCount ?: "0"}"
            // 根据状态处理UI
            if (item.status == "LOCKED") {
                handleLockedState()
            } else {
                handleUnlockedState(item)
            }
        }
        holder.itemView.singleClick {
            itemClick.invoke(item)
        }
    }

    private fun ItemPhaseLayoutBinding.handleLockedState() {
        ivLock.visible()
        tvStudy.gone()
        vBg.setBackgroundResource(R.drawable.shape_phase_locked)
        phaseLevel.setBackgroundResource(R.drawable.shape_phase_level_two)
        setTextColorForViews(
            phaseLevel to R.color.text_describe,
            tvCourseCount to R.color.text_body,
            tvGameCount to R.color.text_body,
            tvCourseVocabulary to R.color.text_describe,
            tvCoursePhrase to R.color.text_describe,
            tvCourseCountTxt to R.color.text_body,
            tvGameCountTxt to R.color.text_body
        )
    }

    private fun ItemPhaseLayoutBinding.handleUnlockedState(item: PhaseInfo) {
        ivLock.gone()
        phaseLevel.setBackgroundResource(R.drawable.shape_phase_level_one)
        phaseLevel.setTextColor(phaseLevel.context.resources.getColor(R.color.text_link, null))

        if (info == "${item.courseType}${item.courseStage}") {
            handleInProgressState()
        } else {
            handleCompletedState()
        }
    }

    private fun ItemPhaseLayoutBinding.handleInProgressState() {
        tvStudy.visible()
        vBg.setBackgroundResource(R.drawable.shape_phase_in_progress)
        setTextColorForViews(
            tvGameCount to R.color.text_on_primary_button,
            tvCourseVocabulary to R.color.text_on_primary_button,
            tvCoursePhrase to R.color.text_on_primary_button,
            tvCourseCount to R.color.text_on_primary_button,
            tvCourseCountTxt to R.color.text_on_primary_button,
            tvGameCountTxt to R.color.text_on_primary_button
        )
    }

    private fun ItemPhaseLayoutBinding.handleCompletedState() {
        tvStudy.gone()
        vBg.setBackgroundResource(R.drawable.shape_phase_complete)
        setTextColorForViews(
            tvGameCount to R.color.text_body,
            tvCourseVocabulary to R.color.text_describe,
            tvCoursePhrase to R.color.text_describe,
            tvCourseCount to R.color.text_body,
            tvCourseCountTxt to R.color.text_body,
            tvGameCountTxt to R.color.text_body
        )
    }

    private fun setTextColorForViews(vararg viewsToColors: Pair<TextView, Int>) {
        viewsToColors.forEach { (view, colorRes) ->
            view.setTextColor(view.context.resources.getColor(colorRes, null))
        }
    }
}