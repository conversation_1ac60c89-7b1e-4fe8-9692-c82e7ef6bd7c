package com.snails.module.course.viewbinder

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.navigation.findNavController
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.course.HomeworkItemInfo
import com.snails.base.utils.ext.compoundDrawable
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.safeNavigate
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.toCustomFormat
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.course.R
import com.snails.module.course.databinding.ItemMyHomeworkBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年02月24日 13:19:59
 */
class MyHomeworkViewBinder(private val itemClick: (HomeworkItemInfo) -> Unit) :
    ViewBindingDelegate<HomeworkItemInfo, ItemMyHomeworkBinding>() {

    private var itemSize = 0

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ItemMyHomeworkBinding>,
        item: HomeworkItemInfo
    ) {
        holder.binding.apply {
            ivCourseIcon.load(
                item.cover,
                placeholder = R.drawable.svg_w88_h88_placeholder,
                error = R.drawable.svg_w88_h88_error
            )
            tvCourseInfo.text = "${item.courseTypeName}${item.courseStage}阶·第${item.courseNum}课"
            if (item.status == "CORRECTED") {
                tvHomeworkState.text = "已批改"
                tvHomeworkState.compoundDrawable(top = R.drawable.svg_right)
            } else {
                tvHomeworkState.text = "待批改"
                tvHomeworkState.compoundDrawable(top = R.drawable.svg_waiting)
            }

            tvCourseName.text = "${item.courseName}"
            tvCommitTime.text = "提交时间：${item.submitTime?.toCustomFormat() ?: ""}"
            if (item.newCorrecting) {
                ivNewReview.visible()
            } else {
                ivNewReview.gone()
            }
            if (itemSize == holder.layoutPosition) {
                vLine.gone()
            } else {
                vLine.visible()
            }
        }
        holder.itemView.singleClick {
            it.findNavController()
                .safeNavigate(
                    R.id.action_myHomeworkFragment_to_myHomeworkListFragment,
                    Bundle().apply {
                        putString("courseId", item.courseId)
                    }
                )
            if (item.newCorrecting) {
                itemClick.invoke(item)
            }
        }
    }

    fun setItemSize(size: Int) {
        this.itemSize = size - 1
    }
}