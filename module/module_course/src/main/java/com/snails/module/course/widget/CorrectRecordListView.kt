package com.snails.module.course.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.LottieAnimationView
import com.snails.base.audio_player.AudioPlayManager
import com.snails.base.audio_player.interfaces.DefaultPlayStateListener
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.course.CorrectRecordItemInfo
import com.snails.module.course.R
import com.snails.module.course.viewbinder.CorrectRecordItemViewBinder

/**
 * @Description 批改记录列表
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */
class CorrectRecordListView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val listAdapter = MultiTypeAdapter()
    var listener: ItemClickListener? = null
    private var currentPlayView: LottieAnimationView? = null

    private val playStateListener = object : DefaultPlayStateListener() {
        override fun onPlay() {
            currentPlayView?.playAnimation()

        }

        override fun onStop() {
            currentPlayView?.apply {
                pauseAnimation()
                frame = 0
            }
        }
    }

    init {
        initView()
    }

    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(CorrectRecordItemInfo::class.java, CorrectRecordItemViewBinder(
                audioItemClick = {
                    currentPlayView = it
                },
                videoItemClick = { data ->
                    listener?.videoItemClick(data)
                }
            ))
        }
        adapter = listAdapter
        addItemDecoration(ItemDecoration())
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setListData(tabList: List<CorrectRecordItemInfo>) {
        listAdapter.items = tabList
        listAdapter.notifyDataSetChanged()
    }

    fun addPlayState() {
        AudioPlayManager.getInstance().addPlayStateListener(playStateListener)
    }

    fun removePlayState() {
        AudioPlayManager.getInstance().removePlayStateListener(playStateListener)
    }

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect.top = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_12)

            kotlin.runCatching {
                val position = parent.getChildAdapterPosition(view)
                val itemCount = parent.adapter?.itemCount ?: 0
                // 判断是否为最后一个数据项
                if (position == itemCount - 1) {
                    outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_30)
                }
            }
        }
    }

    interface ItemClickListener {
        fun videoItemClick(data: String)
    }
}