package com.snails.module.course.ui

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.navigation.Navigation
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.network.repository.info.course.CatalogInfo
import com.snails.base.network.repository.info.course.PhaseInfo
import com.snails.base.network.repository.storage.UserStorage
import com.snails.module.base.BaseStateFragment
import com.snails.module.course.R
import com.snails.module.course.databinding.FragmentSwitchPhaseBinding
import com.snails.module.course.dialog.PhaseLockedDialog
import com.snails.module.course.viewmodel.CourseViewModel
import com.snails.module.course.widget.PhaseListView
import com.snails.module.course.widget.SwitchPhaseTabView

/**
 * @Description 学阶切换
 * <AUTHOR>
 * @CreateTime 2024年10月22日 10:39:12
 */
class SwitchPhaseFragment : BaseStateFragment<FragmentSwitchPhaseBinding>() {

    private val courseViewModel: CourseViewModel by viewModels()

    override fun createViewModel() = courseViewModel



    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initClick() {
        binding.commonTitleView.setBackClickListener {
            val pop = Navigation.findNavController(it).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }

        binding.switchPhaseTabView.itemClickListener =
            object : SwitchPhaseTabView.ItemClickListener {
                override fun itemClick(data: CatalogInfo) {
                    courseViewModel.currentSelectCatalogInfo = data
                    data.stages?.let { list ->
                        binding.phaseListView.setPhaseList(list)
                    }
                }
            }

        binding.phaseListView.listener =
            object : PhaseListView.ItemClickListener {
                override fun itemClick(data: PhaseInfo) {
                    if (data.status == "LOCKED") {
                        PhaseLockedDialog().show(childFragmentManager, "")
                    } else {
                        Navigation.findNavController(binding.phaseListView)
                            .navigate(R.id.action_switchPhaseFragment_to_switchCourseFragment,
                                Bundle().apply {
                                    data.courseStage?.let { putInt("courseStage", it) }
                                    data.courseType?.let { putString("courseType", it) }
                                }
                            )
                    }
                }
            }
    }

    override fun initData() {
        val catalogLiveDataList = courseViewModel.catalogLiveData.value
        if (catalogLiveDataList == null) {
            courseViewModel.getPhaseList()
        } else {
            binding.switchPhaseTabView.setTabList(catalogLiveDataList)
            courseViewModel.currentSelectCatalogInfo?.stages?.let {
                binding.phaseListView.setPhaseList(it)
            }
        }
    }

    override fun initObserve() {
        super.initObserve()

        courseViewModel.catalogLiveData.observe(viewLifecycleOwner) {
            binding.switchPhaseTabView.setTabList(it)
            val info = UserStorage.me.getCurrentStudyInfo()
            if (info != null) {
                val data = it.firstOrNull { info.contains(it.courseType ?: "") }
                data?.stages?.let { it1 -> binding.phaseListView.setPhaseList(it1) }
            } else {
                it[0].stages?.let { list ->
                    binding.phaseListView.setPhaseList(list)
                }
            }
        }
    }
}