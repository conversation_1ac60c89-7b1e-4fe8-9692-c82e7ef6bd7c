package com.snails.module.course.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.ToastUtils
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.downloadx.download
import com.snails.base.network.repository.info.course.CatalogInfo
import com.snails.base.network.repository.info.course.ContentDetailsInfo
import com.snails.base.network.repository.info.course.CourseDetailsInfo
import com.snails.base.network.repository.info.course.HomeworkDetailsInfo
import com.snails.base.network.repository.info.course.HomeworkListInfo
import com.snails.base.network.repository.info.teacher.HomeworkListDetailsInfo
import com.snails.base.network.repository.storage.UserStorage
import com.snails.base.utils.ext.extractMediaFilesFromObject
import com.snails.module.base.BaseViewModel
import com.snails.module.base.bean.StateType
import com.snails.module.base.utils.SingleLiveEventLiveData

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年09月09日 15:21:12
 */
class CourseViewModel : BaseViewModel() {

    // 激活成功
    val activationLiveData: MutableLiveData<String> = SingleLiveEventLiveData()

    val catalogLiveData: MutableLiveData<List<CatalogInfo>> = SingleLiveEventLiveData()

    val coursesLiveData: MutableLiveData<List<CourseDetailsInfo>> = SingleLiveEventLiveData()

    val homeworkListLiveData: MutableLiveData<HomeworkListInfo?> = SingleLiveEventLiveData()
    val correctRecordDetailsInfoLiveData: MutableLiveData<HomeworkDetailsInfo> =
        SingleLiveEventLiveData()

    val contentDetailsInfoLiveData: MutableLiveData<ContentDetailsInfo> = SingleLiveEventLiveData()

    val homeworkDetailsLiveData: MutableLiveData<HomeworkListDetailsInfo> =
        SingleLiveEventLiveData()

    var currentSelectCatalogInfo: CatalogInfo? = null //当前选中的

    var courseStage: Int? = null
    var courseType: String? = null
    var contentId: String? = null
    var courseId: String? = null
    var groupingId: String? = null

    /**
     * 课程激活
     * @param code 激活码
     */
    fun activationCourse(code: String) {
        request(
            request = {
                SnailRepository.me.activate(code)
            },
            success = {
                activationLiveData.value = it
            },
            failed = { a ->
                ToastUtils.showShort(a)
            }
        )
    }

    fun getPhaseList() {
        request(request = {
            SnailRepository.me.getCourseCatalog()
        }, success = { data ->
            val info = UserStorage.me.getCurrentStudyInfo()
            data?.catalogs?.let {
                it.forEach { bean ->
                    bean.selected = (info?.contains("${bean.courseType}") == true)
                    if (bean.selected == true) {
                        currentSelectCatalogInfo = bean
                    }
                }
                catalogLiveData.value = it
            }
        })
    }

    fun getCourseList() {
        val stage = courseStage ?: return
        if (stage == -1) {
            return
        }
        val type = courseType ?: return
        request(
            request = {
                SnailRepository.me.getCourseList(stage, type)
            },
            success = { data ->
                data?.courses?.let {
                    coursesLiveData.value = it
                }
            }
        )
    }

    /**
     * 学生端-获取我的作业列表
     */
    fun getMyHomeworkList(stateType: StateType? = StateType.NONE) {
        val stage = courseStage ?: return
        if (stage == -1) {
            return
        }
        val type = courseType ?: return
        request(
            stateType = stateType,
            request = {
                SnailRepository.me.getMyHomeworkList(stage, type)
            },
            success = { data ->
                homeworkListLiveData.value = data
            }
        )
    }

    /**
     * 学生端-获取我的作业清单项
     */
    fun getMyHomeworkListItem(stateType: StateType = StateType.NONE) {
        val cId = courseId ?: return
        request(
            stateType = stateType,
            request = {
                SnailRepository.me.getMyHomeworkListItem(cId)
            },
            success = { data ->
                data?.let {
                    homeworkDetailsLiveData.value = it
                }
            }
        )
    }

    /**
     * 学生端-获取我的作业详情
     */
    fun getHomeworkDetails(stateType: StateType = StateType.NONE) {
        val gid = groupingId ?: return
        request(
            stateType = stateType,
            request = {
                SnailRepository.me.getHomeworkDetails(gid)
            },
            success = { data ->
                data?.let {
                    val result = handleData(it)
                    correctRecordDetailsInfoLiveData.postValue(result)
                    viewModelScope.download(result.extractMediaFilesFromObject(onlyMp3 = true))
                }
            }
        )
    }

    fun getCourseIntroduction() {
        val cId = contentId ?: return
        request(
            stateType = StateType.DIALOG,
            request = {
                SnailRepository.me.getCourseIntroduction(cId)
            },
            success = { data ->
                data?.let {
                    contentDetailsInfoLiveData.postValue(it)
                }
            }
        )
    }

    fun markRead(courseId: String) {
        requestAndDownloadResources(
            stateType = StateType.NONE,
            request = {
                SnailRepository.me.markRead(courseId)
            }
        )
    }

    private fun handleData(info: HomeworkDetailsInfo): HomeworkDetailsInfo {
        info.items?.forEach {
            it.courseworkType = info.courseworkType
            it.groupingSize = info.groupingSize
        }
        return info
    }
}