package com.snails.module.course.viewbinder

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.navigation.Navigation
import com.blankj.utilcode.util.ColorUtils
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.teacher.HomeworkListItemInfo
import com.snails.base.utils.ext.safeNavigate
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.course.R
import com.snails.module.course.databinding.ItemMyHomeworkListLayoutBinding
import androidx.navigation.findNavController

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年01月10日 15:34:36
 */
class MyHomeworkListItemViewBinder :
    ViewBindingDelegate<HomeworkListItemInfo, ItemMyHomeworkListLayoutBinding>() {
    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ItemMyHomeworkListLayoutBinding>,
        item: HomeworkListItemInfo
    ) {
        holder.binding.apply {
            sivPic.load(
                item.itemCover,
                error = R.drawable.svg_w88_h88_error,
                placeholder = R.drawable.svg_w88_h88_placeholder
            )
            tvClassInfo.text = "第${item.itemIndex}环节"
            tvTitleName.text = item.itemName ?: ""
            //PENDING:待批改（属于作业）,CORRECTING:已点评（属于作业）,  SCORED:已打分（属于作业）
            when (item.itemStatus) {
                "PENDING" -> {
                    tvEvaluateNum.text = "待点评"
                    tvEvaluateNum.setBackgroundResource(R.drawable.shape_spbd_25r_tb3_lr6_bg)
                    tvEvaluateNum.setTextColor(ColorUtils.getColor(R.color.text_on_primary_button))
                }

                else -> {
                    tvEvaluateNum.text =
                        "${item.textCorrectingCount + item.audioCorrectingCount}条点评"
                    tvEvaluateNum.setBackgroundResource(R.drawable.shape_ss_25r_tb3_lr6_bg)
                    tvEvaluateNum.setTextColor(ColorUtils.getColor(R.color.text_success))
                }
            }
        }
        holder.itemView.singleClick {
            it.findNavController()
                .safeNavigate(
                    R.id.action_myHomeworkListFragment_to_myHomeworkEvaluateFragment,
                    Bundle().apply {
                        putString("groupingId", item.groupingId)
                    }
                )
        }
    }
}