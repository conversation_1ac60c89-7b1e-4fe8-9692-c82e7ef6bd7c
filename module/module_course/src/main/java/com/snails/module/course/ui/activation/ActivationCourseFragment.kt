package com.snails.module.course.ui.activation

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.navigation.findNavController
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.utils.ext.safeNavigate
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.BaseVBFragment
import com.snails.module.base.utils.SnailTextWatcher
import com.snails.module.course.R
import com.snails.module.course.databinding.FragmentActivationCourseBinding
import com.snails.module.course.viewmodel.CourseViewModel

/**
 * @Description 课程激活
 * <AUTHOR>
 * @CreateTime 2024年09月09日 09:32:33
 */
class ActivationCourseFragment : BaseVBFragment<FragmentActivationCourseBinding>() {

    private val courseViewModel: CourseViewModel by viewModels()

    override fun initView() {
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(false).init()
        binding.etActivationCode.addTextChangedListener(object : SnailTextWatcher() {
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                // 输入框不为空时显示删除按钮，否则隐藏
                if (s.isNullOrEmpty()) {
                    binding.btnActivationCourse.isEnabled = false
                } else {
                    binding.btnActivationCourse.isEnabled = true
                }
            }
        })
    }

    override fun initObserve() {
        courseViewModel.activationLiveData.observe(viewLifecycleOwner) { info ->
            //激活成功，跳转成功页
            binding.btnActivationCourse.findNavController()
                .safeNavigate(
                    R.id.action_activationCourseFragment_to_activationResultFragment,
                    args = Bundle().apply {
                        putString("data", info)
                    }
                )
        }
    }

    override fun initClick() {
        binding.btnActivationCourse.singleClick {
            val code = binding.etActivationCode.text.toString().trim()
            courseViewModel.activationCourse(code)
        }
        binding.ivBack.singleClick {
            requireActivity().finish()
        }
    }
}