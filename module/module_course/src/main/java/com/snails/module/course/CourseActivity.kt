package com.snails.module.course

import android.content.Intent
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.snails.base.audio.AudioPlayerManager
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.module.base.BaseStateActivity
import com.snails.module.course.databinding.ActivityCourseBinding
import com.therouter.router.Route

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年09月09日 09:23:01
 */
@Route(path = RouterPath.COURSE_HOME)
class CourseActivity : BaseStateActivity<ActivityCourseBinding>() {

    override fun initData() {
        checkRouter(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.let {
            checkRouter(it)
        }
    }

    private fun checkRouter(intent: Intent) {
        AudioPlayerManager.release()
        HRouter.getOriginalPath(intent)?.let { targetFragmentPath ->
            dealRouter(targetFragmentPath)
        }
    }

    private fun dealRouter(fragmentPath: String?) {
        val navController =
            binding.navHostFragment.getFragment<NavHostFragment>().findNavController()
        val navGraph = navController.navInflater.inflate(R.navigation.course_navigation)
        val startDestinationId = when (fragmentPath) {
            RouterPath.SWITCH_PHASE -> {
                R.id.switchPhaseFragment
            }

            RouterPath.COURSE_ACTIVATION -> {
                R.id.activationCourseFragment
            }

            RouterPath.SWITCH_COURSE -> {
                R.id.switchCourseFragment
            }

            RouterPath.HOMEWORK_CORRECT -> {
                AudioPlayerManager.release()
                R.id.myHomeworkFragment
            }

            RouterPath.COURSE_INTRODUCTION -> {
                R.id.courseIntroductionFragment
            }

            else -> {
                R.id.activationCourseFragment
            }
        }
        navGraph.setStartDestination(startDestinationId)
        navController.graph = navGraph
    }
}