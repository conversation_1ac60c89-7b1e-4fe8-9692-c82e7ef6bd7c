package com.snails.module.course.viewbinder

import android.view.ViewGroup
import com.blankj.utilcode.util.ScreenUtils
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.course.CatalogInfo
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.course.R
import com.snails.module.course.databinding.ItemSwitchPhaseLayoutBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:49:09
 */
class SwitchPhaseViewBinder(private val clickItem: (CatalogInfo) -> Unit) :
    ViewBindingDelegate<CatalogInfo, ItemSwitchPhaseLayoutBinding>() {

    private var itemWidth = 0

    fun itemCount(size: Int) {
        itemWidth = if (size >= 4) {
            ScreenUtils.getScreenWidth() / 4
        } else {
            ScreenUtils.getScreenWidth() / size
        }
    }

    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ItemSwitchPhaseLayoutBinding>,
        item: CatalogInfo
    ) {
        holder.binding.apply {
            val layoutParams = flyItemContainer.layoutParams
            layoutParams.width = itemWidth
            layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT
            flyItemContainer.layoutParams = layoutParams
            if (item.status != "LOCKED") {
                item.icon?.let {
                    sivTabIcon.load(it)
                }
            } else {
                sivTabIcon.setBackgroundResource(R.drawable.svg_phase_locked)
            }
            tvTabName.text = item.courseTypeName ?: ""

            if (item.selected == true) {
                ivItemBg.visible()
            } else {
                ivItemBg.gone()
            }
            flyItemContainer.singleClick {
                clickItem.invoke(item)
            }
        }
    }
}