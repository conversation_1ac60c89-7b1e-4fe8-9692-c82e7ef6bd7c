package com.snails.module.course.viewbinder

import android.annotation.SuppressLint
import com.airbnb.lottie.LottieAnimationView
import com.snails.base.network.repository.info.teacher.HomeworkDetailsItemInfo
import com.snails.common.widget.StudentChatItemView
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.course.databinding.ItemMyHomeworkEvaluateBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月27日 19:27:03
 */
class MyHomeworkEvaluateItemViewBinder(
    private val clickAudio: (HomeworkDetailsItemInfo, LottieAnimationView) -> Unit,
    private val lookLargeImage: (String?) -> Unit
) : ViewBindingDelegate<HomeworkDetailsItemInfo, ItemMyHomeworkEvaluateBinding>() {


    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ItemMyHomeworkEvaluateBinding>,
        item: HomeworkDetailsItemInfo
    ) {
        holder.binding.apply {
            chatItemView.setData(item, object : StudentChatItemView.ChatListener {
                override fun clickAudio(
                    info: HomeworkDetailsItemInfo, laBa: LottieAnimationView
                ) {
                    clickAudio.invoke(info, laBa)
                }

                override fun longClick(info: HomeworkDetailsItemInfo) {
                }

                override fun lookLargeImage(imageUrl: String?) {
                    lookLargeImage.invoke(imageUrl)
                }
            })
        }
    }
}