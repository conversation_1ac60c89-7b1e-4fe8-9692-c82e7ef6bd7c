package com.snails.module.course.viewbinder

import android.annotation.SuppressLint
import com.airbnb.lottie.LottieAnimationView
import com.snails.base.audio_player.AudioPlayManager
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.course.CorrectRecordItemInfo
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.invisible
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.course.databinding.ItemCorrectRecordBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:49:09
 */
class CorrectRecordItemViewBinder(
    private val audioItemClick: (LottieAnimationView) -> Unit,
    private val videoItemClick: (String) -> Unit
) : ViewBindingDelegate<CorrectRecordItemInfo, ItemCorrectRecordBinding>() {

    private var currentPlayView: LottieAnimationView? = null

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ItemCorrectRecordBinding>,
        item: CorrectRecordItemInfo
    ) {
        holder.binding.apply {
            if (item.messageType == "TEACHER_CORRECTING") {
                clyStudentContainer.gone()
                item.avatar?.let { sivTeacherHead.load(it) }

                sivTeacherHead.visible()
                sivStudentHead.invisible()

                when (item.resourceType) {
                    "AUDIO" -> {
                        tvTeacherContent.gone()
                        clyTeacherContainer.visible()
                        ivTeacherTVideoPlay.gone()
                        ivTeacherVideo.gone()
                        tvTeacherAudioDuration.text = "${item.audioLength ?: 0}″"
                    }

                    "TEXT" -> {
                        tvTeacherContent.visible()
                        clyTeacherContainer.visible()
                        ivTeacherTVideoPlay.gone()
                        ivTeacherVideo.gone()
                        tvTeacherContent.text = item.resource ?: ""
                        tvTeacherAudioDuration.text = "${item.audioLength ?: 0}″"
                    }

                    "VIDEO" -> {
                        clyTeacherContainer.gone()

                        ivTeacherTVideoPlay.visible()
                        ivTeacherVideo.visible()
                        item.videoCover?.let {
                            ivTeacherVideo.load(it)
                        }
                    }
                }
            } else {
                sivStudentHead.visible()
                item.avatar?.let { sivStudentHead.load(it) }

                clyTeacherContainer.gone()
                sivTeacherHead.invisible()

                when (item.resourceType) {
                    "AUDIO" -> {
                        tvStudentContent.gone()
                        clyStudentContainer.visible()
                        ivStudentVideoPlay.gone()
                        ivStudentVideo.gone()
                        tvStudentAudioDuration.text = "${item.audioLength ?: 0}″"
                    }

                    "TEXT" -> {
                        tvStudentContent.visible()
                        clyStudentContainer.visible()
                        ivStudentVideoPlay.gone()
                        ivStudentVideo.gone()
                        tvStudentContent.text = item.resource ?: ""
                        tvStudentAudioDuration.text = "${item.audioLength ?: 0}″"
                    }

                    "VIDEO" -> {
                        clyStudentContainer.gone()

                        ivStudentVideoPlay.visible()
                        ivStudentVideo.visible()
                        item.videoCover?.let {
                            ivStudentVideo.load(it)
                        }
                    }
                }
            }
            ivTeacherTrumpet.singleClick {
                if (item.resourceType == "AUDIO") {
                    item.resource?.let { it1 ->
                        if (currentPlayView == ivTeacherTrumpet && ivTeacherTrumpet.isAnimating) {
                            AudioPlayManager.getInstance().stop()
                            ivTeacherTrumpet.pauseAnimation()
                        } else {
                            currentPlayView?.pauseAnimation()
                            currentPlayView = ivTeacherTrumpet
                            audioItemClick.invoke(ivTeacherTrumpet)
                            AudioPlayManager.getInstance().play(it1)
                        }
                    }
                }
            }
            ivStudentTrumpet.singleClick {
                if (item.resourceType == "AUDIO") {
                    item.resource?.let { it1 ->
                        if (currentPlayView == ivStudentTrumpet && ivStudentTrumpet.isAnimating) {
                            AudioPlayManager.getInstance().stop()
                            ivStudentTrumpet.pauseAnimation()
                        } else {
                            currentPlayView?.pauseAnimation()
                            currentPlayView = ivStudentTrumpet
                            audioItemClick.invoke(ivStudentTrumpet)
                            AudioPlayManager.getInstance().play(it1)
                        }
                    }
                }
            }
            ivStudentVideo.singleClick {
                item.resource?.let { it1 ->
                    AudioPlayManager.getInstance().stop()
                    currentPlayView?.pauseAnimation()
                    videoItemClick.invoke(it1)
                }
            }
            ivTeacherVideo.singleClick {
                item.resource?.let { it1 ->
                    videoItemClick.invoke(it1)
                    AudioPlayManager.getInstance().stop()
                    currentPlayView?.pauseAnimation()
                }
            }
        }
    }
}