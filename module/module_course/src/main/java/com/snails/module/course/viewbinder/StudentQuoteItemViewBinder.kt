package com.snails.module.course.viewbinder

import android.annotation.SuppressLint
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.teacher.QuoteContentInfo
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.course.databinding.ItemQuoteItemBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月27日 19:27:03
 */
class StudentQuoteItemViewBinder(private val click: (QuoteContentInfo) -> Unit) :
    ViewBindingDelegate<QuoteContentInfo, ItemQuoteItemBinding>() {

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ItemQuoteItemBinding>,
        item: QuoteContentInfo
    ) {
        holder.binding.apply {
            sivRightQuotePic.load(item.originResource)
            tvRightQuoteName.text = "${item.name ?: ""}:"
            sivRightQuoteTxt.text = "${item.index}/${item.groupingSize ?: 0} ${item.originText}"
            tvRightQuoteDuration.text = showAudioLength(item.audioLength)
        }
        holder.itemView.singleClick {
            click.invoke(item)
        }
    }

    private fun showAudioLength(audioLength: Int?): String {
        if (audioLength == null || audioLength <= 0) {
            return "1″"
        }
        return "$audioLength″"
    }
}