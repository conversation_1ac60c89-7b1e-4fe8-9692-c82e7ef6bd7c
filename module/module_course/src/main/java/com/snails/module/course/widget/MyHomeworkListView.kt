package com.snails.module.course.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.course.HomeworkItemInfo
import com.snails.module.course.viewbinder.MyHomeworkViewBinder

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年02月24日 11:54:59
 */
class MyHomeworkListView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    var clickListener: ItemClickListener? = null
    private val myHomeworkViewBinder = MyHomeworkViewBinder(
        itemClick = { item ->
            clickListener?.itemClick(item)
        }
    )
    private val listAdapter = MultiTypeAdapter()

    init {
        initView()
    }

    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(HomeworkItemInfo::class.java, myHomeworkViewBinder)
        }
        adapter = listAdapter
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setDataList(tabList: List<HomeworkItemInfo>) {
        listAdapter.items = tabList
        myHomeworkViewBinder.setItemSize(tabList.size)
        listAdapter.notifyDataSetChanged()
    }


    interface ItemClickListener {
        fun itemClick(data: HomeworkItemInfo)
    }
}