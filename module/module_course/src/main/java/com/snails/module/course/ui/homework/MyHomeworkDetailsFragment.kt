package com.snails.module.course.ui.homework

import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.viewModels
import com.snails.base.audio_player.AudioPlayManager
import com.snails.base.network.repository.info.ISame
import com.snails.base.network.repository.info.course.HomeworkDetailsInfo
import com.snails.base.network.repository.info.teacher.HomeworkDetailsItemInfo
import com.snails.base.network.repository.info.teacher.toQuoteContentInfo
import com.snails.base.utils.ext.addBackPop
import com.snails.module.base.BaseStateFragment
import com.snails.module.base.bean.StateType
import com.snails.module.base.dialog.LookLargeImageDialog
import com.snails.module.course.databinding.FragmentMyHomeworkDetailsBinding
import com.snails.module.course.viewmodel.CourseViewModel
import com.snails.module.course.widget.MyHomeworkEvaluateListView

/**
 * @Description 学生端-我的作业详情
 * <AUTHOR>
 * @CreateTime 2025年02月24日 16:28:22
 */
class MyHomeworkDetailsFragment : BaseStateFragment<FragmentMyHomeworkDetailsBinding>() {

    private val courseViewModel: CourseViewModel by viewModels()

    override fun createViewModel() = courseViewModel

    override fun initData() {
        super.initData()
        courseViewModel.groupingId = getStringExtra("groupingId")
        courseViewModel.getHomeworkDetails(StateType.PAGE)
    }

    override fun initObserve() {
        super.initObserve()
        courseViewModel.correctRecordDetailsInfoLiveData.observe(viewLifecycleOwner) {
            initUiInfo(it)
        }
        binding.evaluateListView.addPlayState()
    }

    override fun initClick() {
        super.initClick()

        binding.commonTitleView.setBackClickListener {
            it.addBackPop(requireActivity())
        }

        binding.evaluateListView.listener = object : MyHomeworkEvaluateListView.ItemClickListener {
            override fun lookLargeImage(info: String?) {
                LookLargeImageDialog(info).show(childFragmentManager, "")
            }
        }

        interceptBack()
    }

    private fun initUiInfo(info: HomeworkDetailsInfo) {
        binding.apply {
            commonTitleView.setTitle(info.teacherName ?: "")
            commonTitleView.setSubTitle("${info.courseTypeName}${info.courseStage}阶·第${info.courseNum}课·${info.courseworkTypeName}")
            info.items?.let {
                evaluateListView.setListData(handleQuoteData(it))
            }
        }
    }

    private fun handleQuoteData(list: List<HomeworkDetailsItemInfo>): List<ISame> {
        val resultList = mutableListOf<ISame>()
        list.forEach { data ->
            resultList.add(data)
            if (!data.refCourseworkId.isNullOrEmpty()) {
                list.firstOrNull { it.id == data.refCourseworkId }?.let {
                    resultList.add(it.toQuoteContentInfo("TEACHER"))
                }
            }
        }
        return resultList
    }

    /**
     * 拦截返回事件
     */
    private fun interceptBack() {
        requireActivity().onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    binding.commonTitleView.addBackPop(requireActivity())
                }
            }
        )
    }

    override fun onRetry() {
        super.onRetry()
        courseViewModel.getHomeworkDetails(StateType.PAGE)
    }

    override fun onDestroy() {
        binding.evaluateListView.removePlayState()
        AudioPlayManager.getInstance().release()
        super.onDestroy()
    }
}