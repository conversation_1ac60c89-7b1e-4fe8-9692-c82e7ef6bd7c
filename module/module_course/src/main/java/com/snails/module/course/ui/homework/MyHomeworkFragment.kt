package com.snails.module.course.ui.homework

import androidx.fragment.app.viewModels
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.network.repository.info.course.HomeworkItemInfo
import com.snails.base.utils.ext.addBackPop
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.visible
import com.snails.module.base.BaseStateFragment
import com.snails.module.base.bean.StateType
import com.snails.module.course.databinding.FragmentMyHomeworkBinding
import com.snails.module.course.viewmodel.CourseViewModel
import com.snails.module.course.widget.MyHomeworkListView

/**
 * @Description 学生端-我的作业
 * <AUTHOR>
 * @CreateTime 2025年02月24日 11:53:16
 */
class MyHomeworkFragment : BaseStateFragment<FragmentMyHomeworkBinding>() {

    private val courseViewModel: CourseViewModel by viewModels()

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun createViewModel() = courseViewModel

    override fun initData() {
        super.initData()
        courseViewModel.courseStage = getStringExtra("courseStage")?.toInt()
        courseViewModel.courseType = getStringExtra("courseType")

        courseViewModel.getMyHomeworkList(StateType.PAGE)
    }

    override fun initObserve() {
        super.initObserve()
        courseViewModel.homeworkListLiveData.observe(viewLifecycleOwner) {
            if (it?.items == null || it.items?.isEmpty() == true) {
                binding.llyEmpty.visible()
                binding.myHomeworkListView.gone()
            } else {
                it.items?.let { list ->
                    binding.llyEmpty.gone()
                    binding.myHomeworkListView.visible()
                    binding.myHomeworkListView.setDataList(list)
                }
            }
        }
    }

    override fun initClick() {
        super.initClick()
        binding.commonTitleView.setBackClickListener {
            it.addBackPop(requireActivity())
        }
        binding.myHomeworkListView.clickListener = object : MyHomeworkListView.ItemClickListener {
            override fun itemClick(data: HomeworkItemInfo) {
                data.courseId?.let { courseViewModel.markRead(it) }
            }
        }
    }

    override fun onRetry() {
        super.onRetry()
        courseViewModel.getMyHomeworkList(stateType = StateType.PAGE)
    }
}