package com.snails.module.course.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.LottieAnimationView
import com.snails.base.audio_player.AudioPlayManager
import com.snails.base.audio_player.interfaces.DefaultPlayStateListener
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.ISame
import com.snails.base.network.repository.info.teacher.HomeworkDetailsItemInfo
import com.snails.base.network.repository.info.teacher.QuoteContentInfo
import com.snails.module.course.R
import com.snails.module.course.viewbinder.MyHomeworkEvaluateItemViewBinder
import com.snails.module.course.viewbinder.StudentQuoteItemViewBinder

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月27日 16:26:40
 */
class MyHomeworkEvaluateListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val paddingTop = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_12)
    private val paddingBottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_20)
    private val paddingQuoteBottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8)
    private val listAdapter = MultiTypeAdapter()
    private var currentPlayView: LottieAnimationView? = null
    private var evaluateItemViewBinder: MyHomeworkEvaluateItemViewBinder? = null
    var listener: ItemClickListener? = null

    private val playStateListener = object : DefaultPlayStateListener() {
        override fun onPlay() {
            currentPlayView?.playAnimation()
        }

        override fun onStop() {
            currentPlayView?.apply {
                pauseAnimation()
                frame = 0
            }
        }
    }

    init {
        initView()
    }

    private fun initView() {
        evaluateItemViewBinder = MyHomeworkEvaluateItemViewBinder(
            clickAudio = { info, view ->
                currentPlayView = view
                AudioPlayManager.getInstance().play(info.resource)
            },
            lookLargeImage = {
                listener?.lookLargeImage(it)
            }
        )
        evaluateItemViewBinder?.let {
            // 列表项
            listAdapter.apply {
                register(HomeworkDetailsItemInfo::class.java, it)
            }
        }
        // 列表项
        listAdapter.apply {
            //引用评论
            register(QuoteContentInfo::class.java, StudentQuoteItemViewBinder {
                clickQuote(it)
            })
        }
        adapter = listAdapter
        addItemDecoration(ItemDecoration())
    }

    private fun clickQuote(quote: QuoteContentInfo) {
        var position = -1
        run loop@{
            listAdapter.items.forEachIndexed { index, data ->
                if (data is HomeworkDetailsItemInfo) {
                    if (data.id == quote.id) {
                        position = index
                        return@loop
                    }
                }
            }
        }
        if (position != -1) {
            smoothScrollToPosition(position)
        }
    }

    /**
     * 设置数据
     */
    @SuppressLint("NotifyDataSetChanged")
    fun setListData(tabList: List<ISame>) {
        listAdapter.items = tabList
        listAdapter.notifyDataSetChanged()
    }

    fun addPlayState() {
        AudioPlayManager.getInstance().addPlayStateListener(playStateListener)
    }

    fun removePlayState() {
        AudioPlayManager.getInstance().removePlayStateListener(playStateListener)
    }

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State
        ) {
            super.getItemOffsets(outRect, view, parent, state)

            kotlin.runCatching {
                val position = parent.getChildAdapterPosition(view)
                // 判断是否为最后一个数据项
                if (position == 0) {
                    outRect.top = paddingTop
                    outRect.bottom = paddingBottom
                } else {
                    val index = position + 1
                    if (index <= listAdapter.items.size - 1) {
                        if (listAdapter.items[index] is QuoteContentInfo) {
                            outRect.bottom = paddingQuoteBottom
                        } else {
                            outRect.bottom = paddingBottom
                        }
                    } else {
                        outRect.bottom = paddingBottom
                    }
                }
            }
        }
    }

    interface ItemClickListener {
        fun lookLargeImage(info: String?)
    }
}