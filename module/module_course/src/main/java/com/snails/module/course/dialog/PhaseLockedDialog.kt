package com.snails.module.course.dialog

import com.snails.base.dialog.BaseDialog
import com.snails.base.utils.ext.singleClick
import com.snails.module.course.databinding.DialogPhaseLockedBinding

/**
 * @Description 阶段弹窗
 * <AUTHOR>
 * @CreateTime 2024年08月16日 10:10:24
 */
class PhaseLockedDialog : BaseDialog<DialogPhaseLockedBinding>() {

    override fun initView() {
    }

    override fun initClick() {
        binding.ivClose.singleClick {
            dismiss()
        }

        binding.tvAgree.singleClick {
            dismiss()
        }
    }
}