<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/course_navigation"
    app:startDestination="@id/activationCourseFragment">

    <fragment
        android:id="@+id/activationCourseFragment"
        android:name="com.snails.module.course.ui.activation.ActivationCourseFragment"
        android:label="ActivationCourseFragment"
        tools:layout="@layout/fragment_activation_course">
        <action
            android:id="@+id/action_activationCourseFragment_to_activationResultFragment"
            app:destination="@id/activationResultFragment" />
    </fragment>

    <fragment
        android:id="@+id/activationResultFragment"
        android:name="com.snails.module.course.ui.activation.ActivationResultFragment"
        android:label="ActivationResultFragment"
        tools:layout="@layout/fragment_activation_result" />

    <fragment
        android:id="@+id/switchPhaseFragment"
        android:name="com.snails.module.course.ui.SwitchPhaseFragment"
        android:label="SwitchPhaseFragment"
        tools:layout="@layout/fragment_switch_phase">
        <action
            android:id="@+id/action_switchPhaseFragment_to_switchCourseFragment"
            app:destination="@id/switchCourseFragment" />
    </fragment>

    <fragment
        android:id="@+id/switchCourseFragment"
        android:name="com.snails.module.course.ui.SwitchCourseFragment"
        android:label="SwitchCourseFragment"
        tools:layout="@layout/fragment_switch_course">
        <argument
            android:name="courseStage"
            app:argType="integer" />
        <argument
            android:name="courseType"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/courseIntroductionFragment"
        android:name="com.snails.module.course.ui.CourseIntroductionFragment"
        android:label="CourseIntroductionFragment" />

    <fragment
        android:id="@+id/myHomeworkFragment"
        android:name="com.snails.module.course.ui.homework.MyHomeworkFragment"
        android:label="MyHomeworkFragment"
        tools:layout="@layout/fragment_my_homework">
        <action
            android:id="@+id/action_myHomeworkFragment_to_myHomeworkListFragment"
            app:destination="@id/myHomeworkListFragment" />
    </fragment>

    <fragment
        android:id="@+id/myHomeworkListFragment"
        android:name="com.snails.module.course.ui.homework.MyHomeworkListFragment"
        android:label="MyHomeworkListFragment"
        tools:layout="@layout/fragment_my_homework_list" >
        <action
            android:id="@+id/action_myHomeworkListFragment_to_myHomeworkEvaluateFragment"
            app:destination="@id/myHomeworkEvaluateFragment" />
    </fragment>

    <fragment
        android:id="@+id/myHomeworkEvaluateFragment"
        android:name="com.snails.module.course.ui.homework.MyHomeworkDetailsFragment"
        android:label="MyHomeworkEvaluateFragment"
        tools:layout="@layout/fragment_my_homework_details" />

</navigation>