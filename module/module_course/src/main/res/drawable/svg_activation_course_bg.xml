<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="375dp"
    android:height="273dp"
    android:viewportWidth="375"
    android:viewportHeight="273">
  <group>
    <clip-path
        android:pathData="M0,0h375v273h-375z"/>
    <path
        android:pathData="M0,0h375v273h-375z"
        android:fillColor="#34ACFE"/>
    <path
        android:pathData="M246.62,44C342.64,44 366.11,98.6 436,98.6V290.97L-260.18,307C-287.21,243.21 -325.05,115.64 -260.18,115.64C-179.09,115.64 -150.29,44 -84.67,44C-19.05,44 -5.18,98.6 60.97,98.6C127.12,98.6 150.59,44 246.62,44Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="70"
            android:startY="44"
            android:endX="69.97"
            android:endY="131.67"
            android:type="linear">
          <item android:offset="0" android:color="#FF29A6FB"/>
          <item android:offset="1" android:color="#FF34ACFE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M106.5,144C16.5,144 -5.5,198.5 -71,198.5V390.5L581.5,406.5C606.83,342.83 642.3,215.5 581.5,215.5C505.5,215.5 478.5,144 417,144C355.5,144 342.5,198.5 280.5,198.5C218.5,198.5 196.5,144 106.5,144Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="272.04"
            android:startY="144"
            android:endX="272.07"
            android:endY="231.5"
            android:type="linear">
          <item android:offset="0" android:color="#FF29A6FB"/>
          <item android:offset="1" android:color="#FF34ACFE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M56.01,186.84L76.74,186.16C77.99,186.21 79.04,187.02 79.42,188.23C79.63,188.9 79.59,189.6 79.35,190.21L82.88,201.43C83.15,202.29 82.67,203.21 81.82,203.48C80.96,203.75 80.05,203.27 79.77,202.4L76.68,192.59L61.03,202.78C60.59,203.1 60.11,203.35 59.6,203.51C59.08,203.67 58.55,203.75 58,203.73L37.27,204.41C36.02,204.36 34.97,203.54 34.59,202.34C34.21,201.14 34.61,199.87 35.6,199.11L35.7,199.04L52.98,187.79C53.85,187.14 54.92,186.8 56.01,186.84ZM71.95,198.89C73.76,197.71 76.18,198.6 76.84,200.67L76.84,200.67L77.51,202.81C77.51,202.82 77.52,202.83 77.52,202.84C77.52,202.85 77.53,202.86 77.53,202.87L80.41,212.01C81.07,214.11 80.25,216.41 78.42,217.6L67.86,224.48C67.42,224.8 66.94,225.05 66.43,225.21C65.92,225.37 65.38,225.45 64.84,225.43L52.11,225.85C49.92,225.92 47.94,224.51 47.27,222.4L43.71,211.08C43.06,209.01 44.54,206.9 46.7,206.83L51.56,206.68L59.43,206.39C60.12,206.37 60.8,206.15 61.38,205.78L67.88,201.56L67.87,201.56L71.95,198.89Z"
        android:fillColor="#44B3FF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M112.5,180L263.5,180A8.5,8.5 0,0 1,272 188.5L272,188.5A8.5,8.5 0,0 1,263.5 197L112.5,197A8.5,8.5 0,0 1,104 188.5L104,188.5A8.5,8.5 0,0 1,112.5 180z"
        android:fillColor="#2A9CEB"/>
    <path
        android:pathData="M94.16,66.21C95.06,66.27 95.78,65.43 95.54,64.56L87.63,35.91C87.54,35.59 87.33,35.31 87.01,35.18C85.2,34.41 81.36,33.84 76.93,35.06C73.1,36.12 69.79,38.12 67.88,39.53C67.11,40.1 66.83,41.09 67.09,42.01L74.85,70.12C75.1,71.03 76.25,71.39 77.03,70.87C79.2,69.41 82.73,67.45 85.66,66.64C88.23,65.93 91.95,66.04 94.16,66.21ZM99.99,64.6C101.82,63.32 104.94,61.31 107.52,60.6C110.44,59.79 114.48,59.66 117.09,59.8C118.03,59.85 118.83,58.94 118.58,58.04L110.81,29.93C110.56,29.01 109.81,28.31 108.86,28.22C106.49,27.98 102.63,27.96 98.79,29.02C94.36,30.24 91.36,32.71 90.2,34.3C90.01,34.57 89.97,34.92 90.05,35.24L97.97,63.89C98.21,64.76 99.26,65.11 99.99,64.6Z"
        android:fillColor="#44B3FF"/>
    <path
        android:pathData="M133.8,80.6L243.04,80.6A15.26,15.26 0,0 1,258.3 95.86L258.3,176.24A15.26,15.26 0,0 1,243.04 191.5L133.8,191.5A15.26,15.26 0,0 1,118.54 176.24L118.54,95.86A15.26,15.26 0,0 1,133.8 80.6z"
        android:fillColor="#7BD7FF"/>
    <path
        android:pathData="M143.14,94.13L233.7,94.13A10.17,10.17 0,0 1,243.87 104.3L243.87,166A10.17,10.17 0,0 1,233.7 176.18L143.14,176.18A10.17,10.17 0,0 1,132.97 166L132.97,104.3A10.17,10.17 0,0 1,143.14 94.13z"
        android:fillColor="#1869A0"/>
    <path
        android:pathData="M117.67,167.16L258.27,167.16A8.14,8.14 0,0 1,266.41 175.3L266.41,183.36A8.14,8.14 0,0 1,258.27 191.5L117.67,191.5A8.14,8.14 0,0 1,109.53 183.36L109.53,175.3A8.14,8.14 0,0 1,117.67 167.16z"
        android:fillColor="#7BD7FF"/>
    <path
        android:pathData="M121.84,177.26H194.07M179.32,183.87H244.95"
        android:strokeWidth="3.05233"
        android:fillColor="#00000000"
        android:strokeColor="#56B7F9"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M142.23,151.04V157.65C142.23,158.85 143.19,159.83 144.39,159.86L232.44,161.72C233.67,161.75 234.69,160.75 234.69,159.51V151.19C234.69,150.21 234.06,149.35 233.11,149.1C222.3,146.31 201.25,144.56 188.13,150.58C187.55,150.84 186.9,150.87 186.31,150.65C170.38,144.62 151.9,146.4 143.65,149.04C142.78,149.32 142.23,150.14 142.23,151.04Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M137.34,162.02C137.34,160.28 138.68,158.83 140.42,158.71L188.21,155.34L236.01,158.71C237.75,158.83 239.09,160.28 239.09,162.02V164.51C239.09,166.43 237.46,167.95 235.55,167.81L188.21,164.47L140.88,167.81C138.97,167.95 137.34,166.43 137.34,164.51V162.02Z"
        android:fillColor="#FFD735"/>
    <path
        android:pathData="M181.08,163.15C181.24,162.07 182.17,161.28 183.26,161.28H194.15C195.24,161.28 196.16,162.07 196.33,163.15L196.63,165.1C196.84,166.43 195.8,167.64 194.45,167.64H182.96C181.61,167.64 180.57,166.43 180.78,165.1L181.08,163.15Z"
        android:fillColor="#FFD735"/>
    <path
        android:pathData="M147.12,152.47C159.65,149.34 172.07,150.03 180.88,152.47M193.11,152.47C201.91,149.54 220.7,148.17 231.27,152.47"
        android:strokeWidth="2.20822"
        android:fillColor="#00000000"
        android:strokeColor="#E9E9E9"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M192.24,136.72C191.02,140.09 187.32,141.83 183.97,140.61C183.04,140.27 182.21,139.72 181.53,139.01C172.91,129.99 165.16,124.46 171.07,118.75C176.52,113.55 181.55,120.33 184.24,123.14C191.98,108.04 198.92,98.07 205.5,103.5C213.61,110.15 200.06,115.71 192.24,136.72V136.72Z"
        android:fillColor="#27E96B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M99.28,102.33C99.42,101.26 100.67,100.74 101.53,101.39L103.84,103.13C104.03,103.27 104.26,103.36 104.5,103.4L107.37,103.78C108.44,103.92 108.96,105.17 108.31,106.03L106.57,108.34C106.42,108.53 106.33,108.76 106.3,109L105.92,111.87C105.77,112.94 104.53,113.46 103.67,112.81L101.36,111.07C101.16,110.92 100.93,110.83 100.69,110.8L97.83,110.42C96.76,110.27 96.24,109.03 96.89,108.17L98.63,105.86C98.77,105.66 98.86,105.44 98.9,105.19L99.28,102.33Z"
        android:fillColor="#7BD7FF"/>
    <path
        android:pathData="M271.51,159.43C271.9,160.36 272.97,160.81 273.91,160.43C274.84,160.04 275.29,158.97 274.91,158.03L274.21,156.34L275.91,155.64C276.85,155.25 277.3,154.18 276.91,153.24C276.52,152.31 275.45,151.86 274.52,152.24L272.82,152.94L272.12,151.24C271.74,150.3 270.67,149.86 269.73,150.24C268.79,150.63 268.34,151.7 268.73,152.64L269.42,154.33L267.73,155.03C266.79,155.41 266.34,156.49 266.72,157.42C267.11,158.36 268.18,158.81 269.12,158.43L270.82,157.73L271.51,159.43Z"
        android:fillColor="#FFD735"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M263.31,86.19C268.15,77.62 274.63,79.69 275.61,82.7C276.59,85.7 274.18,88.61 270.94,87.65C267.71,86.68 267.56,75.05 277.36,75.76"
        android:strokeAlpha="0.2"
        android:strokeWidth="3"
        android:fillColor="#00000000"
        android:strokeColor="#EAF6FF"
        android:fillAlpha="0.2"
        android:strokeLineCap="round"/>
  </group>
</vector>
