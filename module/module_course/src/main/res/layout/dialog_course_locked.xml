<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/base_sw_dp_311"
    android:layout_height="@dimen/base_sw_dp_198"
    android:layout_gravity="center">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/base_sw_dp_311"
        android:layout_height="@dimen/base_sw_dp_198"
        android:background="@drawable/com_dialog_white_20_shape"
        android:paddingBottom="@dimen/base_sw_dp_20">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_16"
            android:text="@string/str_course_lock"
            android:textColor="@color/text_headline"
            android:textSize="@dimen/headline_h4"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTipsDesc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/base_sw_dp_20"
            android:layout_marginTop="@dimen/base_sw_dp_8"
            android:text="@string/str_course_lock_desc"
            android:textColor="@color/text_body"
            android:textSize="@dimen/text_body_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvAgree"
            android:layout_width="0dp"
            android:layout_height="@dimen/base_sw_dp_48"
            android:layout_marginHorizontal="@dimen/base_sw_dp_55"
            android:background="@drawable/com_btn_pb_bg_shape"
            android:elevation="@dimen/base_sw_dp_0"
            android:gravity="center"
            android:text="@string/str_know"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/button_h1"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>