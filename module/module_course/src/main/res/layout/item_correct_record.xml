<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/base_sw_dp_16">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivTeacherHead"
        android:layout_width="@dimen/base_sw_dp_38"
        android:layout_height="@dimen/base_sw_dp_38"
        android:scaleType="centerCrop"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/CircleStyle"
        tools:background="#b5b5b5"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/clyTeacherContainer"
        android:layout_width="@dimen/base_sw_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_8"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/sivStudentHead"
        app:layout_constraintStart_toEndOf="@+id/sivTeacherHead"
        app:layout_constraintTop_toTopOf="@+id/sivTeacherHead"
        tools:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_correct_record_item_bg"
            android:minWidth="@dimen/base_sw_dp_120"
            android:padding="@dimen/base_sw_dp_12"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="RtlSymmetry">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTeacherContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_headline"
                android:textSize="@dimen/text_body_small"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="文字文字文字文字文字文字文字文字文字文字文字文字"
                tools:visibility="visible" />

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/ivTeacherTrumpet"
                android:layout_width="@dimen/base_sw_dp_24"
                android:layout_height="@dimen/base_sw_dp_24"
                android:layout_marginTop="@dimen/base_sw_dp_4"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTeacherContent"
                app:layout_goneMarginTop="@dimen/base_sw_dp_0"
                app:lottie_autoPlay="false"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/lottie_radio" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTeacherAudioDuration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/base_sw_dp_8"
                android:textColor="@color/text_headline"
                android:textSize="@dimen/text_body_small"
                app:layout_constraintBottom_toBottomOf="@+id/ivTeacherTrumpet"
                app:layout_constraintStart_toEndOf="@+id/ivTeacherTrumpet"
                app:layout_constraintTop_toTopOf="@+id/ivTeacherTrumpet"
                tools:text="18" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivTeacherVideo"
        android:layout_width="@dimen/base_sw_dp_247"
        android:layout_height="@dimen/base_sw_dp_139"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:scaleType="centerCrop"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@+id/sivTeacherHead"
        app:layout_constraintTop_toTopOf="@+id/sivTeacherHead"
        app:shapeAppearance="@style/Rounded12Style"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivTeacherTVideoPlay"
        android:layout_width="@dimen/base_sw_dp_32"
        android:layout_height="@dimen/base_sw_dp_32"
        android:background="@drawable/svg_wb_play"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivTeacherVideo"
        app:layout_constraintEnd_toEndOf="@+id/ivTeacherVideo"
        app:layout_constraintStart_toStartOf="@+id/ivTeacherVideo"
        app:layout_constraintTop_toTopOf="@+id/ivTeacherVideo"
        tools:visibility="visible" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivStudentHead"
        android:layout_width="@dimen/base_sw_dp_38"
        android:layout_height="@dimen/base_sw_dp_38"
        android:scaleType="centerCrop"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/CircleStyle"
        tools:background="#b5b5b5"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/clyStudentContainer"
        android:layout_width="@dimen/base_sw_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_8"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/sivStudentHead"
        app:layout_constraintStart_toEndOf="@+id/sivTeacherHead"
        app:layout_constraintTop_toTopOf="@+id/sivStudentHead"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:background="@drawable/shape_correct_record_student_item_bg"
            android:minWidth="@dimen/base_sw_dp_120"
            android:padding="@dimen/base_sw_dp_12"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="RtlSymmetry">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvStudentContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_on_primary_button"
                android:textSize="@dimen/text_body_small"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="文字文字文字文字文字文字文字文字文字文字文字文字文字文字"
                tools:visibility="gone" />

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/ivStudentTrumpet"
                android:layout_width="@dimen/base_sw_dp_24"
                android:layout_height="@dimen/base_sw_dp_24"
                android:layout_marginTop="@dimen/base_sw_dp_4"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvStudentContent"
                app:layout_goneMarginTop="@dimen/base_sw_dp_0"
                app:lottie_autoPlay="false"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/lottie_radio_white" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvStudentAudioDuration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/base_sw_dp_8"
                android:textColor="@color/text_on_primary_button"
                android:textSize="@dimen/text_body_small"
                app:layout_constraintBottom_toBottomOf="@+id/ivStudentTrumpet"
                app:layout_constraintEnd_toStartOf="@+id/ivStudentTrumpet"
                app:layout_constraintTop_toTopOf="@+id/ivStudentTrumpet"
                tools:text="18" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivStudentVideo"
        android:layout_width="@dimen/base_sw_dp_247"
        android:layout_height="@dimen/base_sw_dp_139"
        android:layout_marginEnd="@dimen/base_sw_dp_8"
        android:scaleType="centerCrop"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/sivStudentHead"
        app:layout_constraintTop_toTopOf="@+id/sivTeacherHead"
        app:shapeAppearance="@style/Rounded12Style"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivStudentVideoPlay"
        android:layout_width="@dimen/base_sw_dp_32"
        android:layout_height="@dimen/base_sw_dp_32"
        android:background="@drawable/svg_wb_play"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivStudentVideo"
        app:layout_constraintEnd_toEndOf="@+id/ivStudentVideo"
        app:layout_constraintStart_toStartOf="@+id/ivStudentVideo"
        app:layout_constraintTop_toTopOf="@+id/ivStudentVideo"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>