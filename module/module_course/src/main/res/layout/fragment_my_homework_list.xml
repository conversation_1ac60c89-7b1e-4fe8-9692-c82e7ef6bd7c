<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_157"
        android:background="@drawable/svg_homework_list_top_bg"
        app:layout_constraintTop_toTopOf="parent" />

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_44"
        app:commTitle="@string/str_my_homework_list"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vStudentInfoBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_85"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:background="@drawable/shape_sbw_59r_bg"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCourseInfo"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_28"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintBottom_toTopOf="@+id/tvCourseName"
        app:layout_constraintEnd_toStartOf="@+id/ivScore"
        app:layout_constraintStart_toStartOf="@+id/vStudentInfoBg"
        app:layout_constraintTop_toTopOf="@+id/vStudentInfoBg"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="听力1阶 · 第2课" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCourseName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_28"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h3"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/vStudentInfoBg"
        app:layout_constraintEnd_toStartOf="@+id/ivScore"
        app:layout_constraintStart_toStartOf="@+id/vStudentInfoBg"
        app:layout_constraintTop_toBottomOf="@+id/tvCourseInfo"
        tools:text="who is it" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivScore"
        android:layout_width="@dimen/base_sw_dp_68"
        android:layout_height="@dimen/base_sw_dp_68"
        android:layout_marginEnd="@dimen/base_sw_dp_17"
        app:layout_constraintBottom_toBottomOf="@+id/vStudentInfoBg"
        app:layout_constraintEnd_toEndOf="@+id/vStudentInfoBg"
        app:layout_constraintTop_toTopOf="@+id/vStudentInfoBg" />

    <com.snails.module.course.widget.MyHomeworkListListView
        android:id="@+id/homeworkListView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_0"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@+id/clyBottomContainer"
        app:layout_constraintTop_toBottomOf="@+id/vStudentInfoBg" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clyBottomContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_background_white"
        android:paddingBottom="@dimen/base_sw_dp_36"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible">

        <View
            android:id="@+id/view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_48"
            android:layout_marginHorizontal="@dimen/base_sw_dp_20"
            android:layout_marginTop="@dimen/base_sw_dp_16"
            android:background="@drawable/com_btn_state_selector_bg"
            android:enabled="false"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLookStudyReport"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/svg_record_list"
            android:drawablePadding="@dimen/base_sw_dp_4"
            android:gravity="center"
            android:text="查看学习报告"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/button_h1"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/view"
            app:layout_constraintEnd_toEndOf="@+id/view"
            app:layout_constraintStart_toStartOf="@+id/view"
            app:layout_constraintTop_toTopOf="@+id/view"
            tools:ignore="HardcodedText" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
