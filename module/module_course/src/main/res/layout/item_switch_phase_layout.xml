<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/flyItemContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivItemBg"
        android:layout_width="@dimen/base_sw_dp_142"
        android:layout_height="@dimen/base_sw_dp_94"
        android:layout_gravity="center"
        android:background="@drawable/svg_selected_bg"
        android:visibility="gone"
        tools:visibility="visible" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:gravity="center"
        android:orientation="vertical">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/sivTabIcon"
            android:layout_width="@dimen/base_sw_dp_40"
            android:layout_height="@dimen/base_sw_dp_40"
            app:shapeAppearance="@style/CircleStyle" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTabName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_6"
            android:textColor="@color/text_body"
            android:textSize="@dimen/button_h2"
            android:textStyle="bold" />
    </LinearLayout>
</FrameLayout>