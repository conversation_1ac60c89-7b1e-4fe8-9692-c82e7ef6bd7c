<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/vHeadBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/surface_brand"
        app:layout_constraintBottom_toBottomOf="@+id/switchPhaseTabView"
        app:layout_constraintTop_toTopOf="parent" />

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_44"
        app:commTitle="@string/str_switch_phase"
        app:layout_constraintTop_toTopOf="parent" />

    <com.snails.module.course.widget.SwitchPhaseTabView
        android:id="@+id/switchPhaseTabView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_94"
        android:background="#00000000"
        android:clipChildren="false"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView"
        tools:listitem="@layout/item_switch_phase_layout" />

    <com.snails.module.course.widget.PhaseListView
        android:id="@+id/phaseListView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/surface_background_white"
        android:paddingHorizontal="@dimen/base_sw_dp_12"
        android:paddingTop="@dimen/base_sw_dp_8"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/switchPhaseTabView" />

</androidx.constraintlayout.widget.ConstraintLayout>