<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_44"
        app:commTitle="@string/str_switch_course"
        app:layout_constraintTop_toTopOf="parent" />

    <com.snails.module.course.widget.CourseListView
        android:id="@+id/courseListView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingHorizontal="@dimen/base_sw_dp_12"
        android:paddingTop="@dimen/base_sw_dp_8"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView" />

</androidx.constraintlayout.widget.ConstraintLayout>