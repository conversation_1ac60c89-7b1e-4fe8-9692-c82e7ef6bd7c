<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clyContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/vTopSpace"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_44"
        android:background="@color/surface_background_white"
        app:layout_constraintTop_toTopOf="parent" />

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_44"
        app:commTitleBg="@color/surface_background_white"
        app:layout_constraintTop_toBottomOf="@+id/vTopSpace" />

    <com.snails.module.course.widget.MyHomeworkEvaluateListView
        android:id="@+id/evaluateListView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_weight="1"
        android:scrollbars="none"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView"
        tools:itemCount="1" />

</androidx.constraintlayout.widget.ConstraintLayout>
