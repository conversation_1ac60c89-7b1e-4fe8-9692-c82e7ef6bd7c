<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_sw_dp_96">

    <View
        android:id="@+id/vBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@drawable/shape_phase_complete_radius_12" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivCourseCover"
        android:layout_width="@dimen/base_sw_dp_72"
        android:layout_height="@dimen/base_sw_dp_72"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="@+id/vBg"
        app:layout_constraintStart_toStartOf="@+id/vBg"
        app:layout_constraintTop_toTopOf="@+id/vBg"
        app:shapeAppearance="@style/Rounded8Style" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivCourseCoverMask"
        android:layout_width="@dimen/base_sw_dp_72"
        android:layout_height="@dimen/base_sw_dp_72"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:background="@color/surface_mask2"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/vBg"
        app:layout_constraintStart_toStartOf="@+id/vBg"
        app:layout_constraintTop_toTopOf="@+id/vBg"
        app:shapeAppearance="@style/Rounded8Style"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLockWhite"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:background="@drawable/svg_locked_24_24_white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivCourseCoverMask"
        app:layout_constraintEnd_toEndOf="@+id/ivCourseCoverMask"
        app:layout_constraintStart_toStartOf="@+id/ivCourseCoverMask"
        app:layout_constraintTop_toTopOf="@+id/ivCourseCoverMask"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvCourseIndex"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:background="@drawable/shape_course_index"
        android:textColor="@color/text_link"
        android:textSize="@dimen/text_body_footnote"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/ivCourseCover"
        app:layout_constraintTop_toTopOf="@+id/ivCourseCover"
        tools:text="第1课" />

    <TextView
        android:id="@+id/tvCourseName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_6"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/button_h1"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/ivCourseCover"
        app:layout_constraintTop_toBottomOf="@+id/tvCourseIndex"
        tools:text="20" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCourseVocabulary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:gravity="center"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintStart_toEndOf="@+id/ivCourseCover"
        app:layout_constraintTop_toBottomOf="@+id/tvCourseName"
        tools:text="词汇·300" />

    <View
        android:id="@+id/vCourseLine"
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:background="@color/border_divider"
        app:layout_constraintBottom_toBottomOf="@+id/tvCourseVocabulary"
        app:layout_constraintStart_toEndOf="@+id/tvCourseVocabulary"
        app:layout_constraintTop_toTopOf="@+id/tvCourseVocabulary" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCoursePhrase"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:gravity="center"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintStart_toEndOf="@+id/vCourseLine"
        app:layout_constraintTop_toBottomOf="@+id/tvCourseName"
        tools:text="词汇·300" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStudyCourse"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_12"
        android:background="@drawable/shape_current_study_course"
        android:text="@string/str_current_study"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/button_h3"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLock"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_marginEnd="@dimen/base_sw_dp_12"
        android:background="@drawable/svg_locked_24_24_gray"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>