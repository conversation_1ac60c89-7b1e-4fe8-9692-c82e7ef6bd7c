<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/vHead"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_44"
        android:background="@color/surface_background_white"
        app:layout_constraintTop_toTopOf="parent" />

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:commShowBackBtn="true"
        app:commTitle="@string/str_course_details_info"
        app:commTitleBg="@color/surface_background_white"
        app:layout_constraintTop_toBottomOf="@+id/vHead" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivPic"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true" />
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>