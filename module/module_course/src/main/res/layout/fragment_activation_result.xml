<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/vBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_300"
        android:background="@drawable/svg_blue_bg"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivSucceed"
        android:layout_width="@dimen/base_sw_dp_261"
        android:layout_height="@dimen/base_sw_dp_200"
        android:layout_marginTop="@dimen/base_sw_dp_40"
        android:background="@drawable/ic_activation_succeed"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vWBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:background="@drawable/shape_sbw_24r_bg"
        app:layout_constraintBottom_toBottomOf="@+id/vBgThree"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivSucceed" />

    <View
        android:id="@+id/vScanCodeBg"
        android:layout_width="@dimen/base_sw_dp_172"
        android:layout_height="@dimen/base_sw_dp_172"
        android:layout_marginTop="@dimen/base_sw_dp_28"
        android:background="@drawable/shape_wechat_scan_code_bg"
        app:layout_constraintEnd_toEndOf="@+id/vWBg"
        app:layout_constraintStart_toStartOf="@+id/vWBg"
        app:layout_constraintTop_toTopOf="@+id/vWBg" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivScanCode"
        android:layout_width="@dimen/base_sw_dp_142"
        android:layout_height="@dimen/base_sw_dp_142"
        android:background="@drawable/svg_scan_code"
        app:layout_constraintBottom_toBottomOf="@+id/vScanCodeBg"
        app:layout_constraintEnd_toEndOf="@+id/vScanCodeBg"
        app:layout_constraintStart_toStartOf="@+id/vScanCodeBg"
        app:layout_constraintTop_toTopOf="@+id/vScanCodeBg" />

    <View
        android:id="@+id/vLine"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_14"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:background="@drawable/svg_line"
        app:layout_constraintEnd_toEndOf="@+id/vWBg"
        app:layout_constraintStart_toStartOf="@+id/vWBg"
        app:layout_constraintTop_toBottomOf="@+id/vScanCodeBg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:drawableStart="@drawable/svg_preparation_blue"
        android:text="课前准备"
        android:textColor="@color/surface_primary_button"
        android:textSize="@dimen/headline_h3"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vLine"
        tools:ignore="HardcodedText" />

    <View
        android:id="@+id/vBgOne"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:background="@drawable/shape_sbd_8r_bg"
        app:layout_constraintEnd_toEndOf="@+id/vWBg"
        app:layout_constraintStart_toStartOf="@+id/vWBg"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />


    <View
        android:id="@+id/vBgTwo"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:background="@drawable/shape_sbd_8r_bg"
        app:layout_constraintEnd_toEndOf="@+id/vWBg"
        app:layout_constraintStart_toStartOf="@+id/vWBg"
        app:layout_constraintTop_toBottomOf="@+id/vBgOne" />

    <View
        android:id="@+id/vBgThree"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_20"
        app:layout_constraintEnd_toEndOf="@+id/vWBg"
        app:layout_constraintStart_toStartOf="@+id/vWBg"
        app:layout_constraintTop_toBottomOf="@+id/vBgTwo" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStepOne"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:drawableStart="@drawable/svg_step_one"
        android:drawablePadding="@dimen/base_sw_dp_8"
        android:text="@string/str_step_one_tips"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintBottom_toBottomOf="@+id/vBgOne"
        app:layout_constraintStart_toStartOf="@+id/vBgOne"
        app:layout_constraintTop_toTopOf="@+id/vBgOne" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStepTwo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:drawableStart="@drawable/svg_step_two"
        android:drawablePadding="@dimen/base_sw_dp_8"
        android:text="@string/str_step_two_tips"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintBottom_toBottomOf="@+id/vBgTwo"
        app:layout_constraintStart_toStartOf="@+id/vBgTwo"
        app:layout_constraintTop_toTopOf="@+id/vBgTwo" />

    <View
        android:id="@+id/vBottomBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_72"
        android:background="@color/surface_background_white"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvClose"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:background="@drawable/com_btn_stroke_pb_bg_shape"
        android:elevation="@dimen/base_sw_dp_0"
        android:gravity="center"
        android:text="关闭"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/vBottomBg"
        app:layout_constraintEnd_toEndOf="@+id/vBottomBg"
        app:layout_constraintStart_toStartOf="@+id/vBottomBg"
        app:layout_constraintTop_toTopOf="@+id/vBottomBg"
        tools:ignore="HardcodedText" />


</androidx.constraintlayout.widget.ConstraintLayout>