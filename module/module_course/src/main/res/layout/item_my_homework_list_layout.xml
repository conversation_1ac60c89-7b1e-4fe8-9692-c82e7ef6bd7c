<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/base_sw_dp_16"
    android:background="@drawable/shape_sbw_16r_bg">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivPic"
        android:layout_width="@dimen/base_sw_dp_58"
        android:layout_height="@dimen/base_sw_dp_58"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/Rounded12Style"
        tools:background="#00fff0" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvClassInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_4"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintStart_toEndOf="@+id/sivPic"
        app:layout_constraintTop_toTopOf="@+id/sivPic"
        tools:text="第6环节" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitleName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_4"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h4"
        app:layout_constraintStart_toEndOf="@+id/sivPic"
        app:layout_constraintTop_toBottomOf="@+id/tvClassInfo"
        tools:text="词汇测验" />

    <View
        android:id="@+id/vSpace"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_12"
        app:layout_constraintTop_toBottomOf="@+id/sivPic" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvEvaluateNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_4"
        android:gravity="center"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintBottom_toBottomOf="@+id/sivPic"
        app:layout_constraintEnd_toStartOf="@+id/ivArrowRight"
        app:layout_constraintTop_toTopOf="@+id/sivPic"
        tools:background="@drawable/shape_ss_25r_tb3_lr6_bg"
        tools:text="2条点评"
        tools:textColor="@color/text_on_primary_button" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrowRight"
        android:layout_width="@dimen/base_sw_dp_16"
        android:layout_height="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_12"
        android:background="@drawable/svg_arrow_right"
        app:layout_constraintBottom_toBottomOf="@+id/tvEvaluateNum"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvEvaluateNum" />
</androidx.constraintlayout.widget.ConstraintLayout>
