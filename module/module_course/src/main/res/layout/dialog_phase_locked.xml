<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/base_sw_dp_311"
    android:layout_height="@dimen/base_sw_dp_419"
    android:layout_gravity="center">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/base_sw_dp_311"
        android:layout_height="@dimen/base_sw_dp_419"
        android:background="@drawable/com_dialog_white_20_shape"
        android:paddingBottom="@dimen/base_sw_dp_20">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_16"
            android:text="@string/str_phase_lock"
            android:textColor="@color/text_headline"
            android:textSize="@dimen/headline_h4"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivClose"
            android:layout_width="@dimen/base_sw_dp_24"
            android:layout_height="@dimen/base_sw_dp_24"
            android:layout_marginTop="@dimen/base_sw_dp_17"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            android:background="@drawable/svg_close"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTipsDesc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/base_sw_dp_20"
            android:layout_marginTop="@dimen/base_sw_dp_8"
            android:text="@string/str_tips_desc"
            android:textColor="@color/text_body"
            android:textSize="@dimen/text_body_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clyOne"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/base_sw_dp_20"
            android:layout_marginTop="@dimen/base_sw_dp_12"
            android:background="@drawable/shape_phase_complete_radius_12"
            android:paddingBottom="@dimen/base_sw_dp_8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTipsDesc">

            <TextView
                android:id="@+id/tvOne"
                android:layout_width="@dimen/base_sw_dp_20"
                android:layout_height="@dimen/base_sw_dp_20"
                android:layout_marginStart="@dimen/base_sw_dp_12"
                android:layout_marginTop="@dimen/base_sw_dp_8"
                android:background="@drawable/shape_dialog_locked_index"
                android:gravity="center"
                android:text="1"
                android:textColor="@color/text_on_primary_button"
                android:textSize="@dimen/button_h2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tvOneTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/base_sw_dp_6"
                android:text="@string/str_change_phase"
                android:textColor="@color/text_link"
                android:textSize="@dimen/button_h2"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/tvOne"
                app:layout_constraintStart_toEndOf="@+id/tvOne"
                app:layout_constraintTop_toTopOf="@+id/tvOne" />

            <TextView
                android:id="@+id/tvOneContent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/base_sw_dp_12"
                android:layout_marginTop="@dimen/base_sw_dp_8"
                android:layout_marginEnd="@dimen/base_sw_dp_12"
                android:text="@string/str_phase_lock_tips_one"
                android:textColor="@color/text_link"
                android:textSize="@dimen/button_h2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvOne" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clyTwo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/base_sw_dp_20"
            android:layout_marginTop="@dimen/base_sw_dp_12"
            android:background="@drawable/shape_phase_complete_radius_12"
            android:paddingBottom="@dimen/base_sw_dp_8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/clyOne">

            <TextView
                android:id="@+id/tvTwo"
                android:layout_width="@dimen/base_sw_dp_20"
                android:layout_height="@dimen/base_sw_dp_20"
                android:layout_marginStart="@dimen/base_sw_dp_12"
                android:layout_marginTop="@dimen/base_sw_dp_8"
                android:background="@drawable/shape_dialog_locked_index"
                android:gravity="center"
                android:text="2"
                android:textColor="@color/text_on_primary_button"
                android:textSize="@dimen/button_h2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tvTwoTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/base_sw_dp_6"
                android:text="@string/str_study_open_lock"
                android:textColor="@color/text_link"
                android:textSize="@dimen/button_h2"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/tvTwo"
                app:layout_constraintStart_toEndOf="@+id/tvTwo"
                app:layout_constraintTop_toTopOf="@+id/tvTwo" />

            <TextView
                android:id="@+id/tvTwoContent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/base_sw_dp_12"
                android:layout_marginTop="@dimen/base_sw_dp_8"
                android:layout_marginEnd="@dimen/base_sw_dp_12"
                android:text="@string/str_phase_lock_tips_two"
                android:textColor="@color/text_link"
                android:textSize="@dimen/button_h2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTwo" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvAgree"
            android:layout_width="0dp"
            android:layout_height="@dimen/base_sw_dp_48"
            android:layout_marginHorizontal="@dimen/base_sw_dp_55"
            android:background="@drawable/com_btn_pb_bg_shape"
            android:elevation="@dimen/base_sw_dp_0"
            android:gravity="center"
            android:text="@string/str_know"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/button_h1"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>