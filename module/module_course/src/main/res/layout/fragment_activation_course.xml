<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_273"
        android:background="@drawable/svg_activation_course_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivBack"
        android:layout_width="@dimen/base_sw_dp_44"
        android:layout_height="@dimen/base_sw_dp_44"
        android:layout_marginStart="@dimen/base_sw_dp_10"
        android:layout_marginTop="@dimen/base_sw_dp_40"
        android:background="@drawable/svg_common_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/base_sw_dp_221"
        android:background="@drawable/shape_activation_course" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_32"
        android:layout_marginTop="@dimen/base_sw_dp_245"
        android:text="@string/str_course_activation"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h2"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vInputActivationCodeArea"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginStart="@dimen/base_sw_dp_32"
        android:layout_marginTop="@dimen/base_sw_dp_24"
        android:layout_marginEnd="@dimen/base_sw_dp_40"
        android:background="@drawable/com_btn_sb_bg_shape"
        android:enabled="false"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etActivationCode"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:background="@null"
        android:gravity="center_vertical"
        android:hint="@string/str_input_activation_code_tips"
        android:maxLength="100"
        android:maxLines="1"
        android:textColor="@color/text_headline"
        android:textColorHint="@color/text_disable"
        android:textCursorDrawable="@drawable/color_cursor"
        android:textSize="@dimen/headline_h4"
        app:layout_constraintBottom_toBottomOf="@+id/vInputActivationCodeArea"
        app:layout_constraintEnd_toEndOf="@+id/vInputActivationCodeArea"
        app:layout_constraintStart_toStartOf="@+id/vInputActivationCodeArea"
        app:layout_constraintTop_toTopOf="@+id/vInputActivationCodeArea" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnActivationCourse"
        android:layout_width="@dimen/base_sw_dp_295"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginTop="@dimen/base_sw_dp_40"
        android:background="@drawable/com_btn_pb_bg_shape"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/str_activation_right_now"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/button_h1"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etActivationCode" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:text="@string/str_activation_sms_tips"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnActivationCourse" />

</androidx.constraintlayout.widget.ConstraintLayout>