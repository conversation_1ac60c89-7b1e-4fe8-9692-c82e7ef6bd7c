<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_sw_dp_86">

    <View
        android:id="@+id/vBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@drawable/shape_phase_complete" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/phaseLevel"
        android:layout_width="@dimen/base_sw_dp_62"
        android:layout_height="@dimen/base_sw_dp_62"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:gravity="center"
        android:textColor="@color/text_link"
        android:textSize="@dimen/headline_h3"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/vBg"
        app:layout_constraintStart_toStartOf="@+id/vBg"
        app:layout_constraintTop_toTopOf="@+id/vBg"
        tools:text="1阶" />

    <TextView
        android:id="@+id/tvCourseCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_7"
        android:textColor="@color/text_body"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/phaseLevel"
        app:layout_constraintTop_toTopOf="@+id/phaseLevel"
        tools:text="20" />

    <TextView
        android:id="@+id/tvCourseCountTxt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_7"
        android:layout_marginBottom="@dimen/base_sw_dp_2"
        android:text="@string/str_course"
        android:textColor="@color/text_body"
        android:textSize="@dimen/button_h3"
        app:layout_constraintBottom_toBottomOf="@+id/tvCourseCount"
        app:layout_constraintStart_toEndOf="@+id/tvCourseCount" />

    <TextView
        android:id="@+id/tvGameCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_7"
        android:textColor="@color/text_body"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/tvCourseCountTxt"
        app:layout_constraintTop_toTopOf="@+id/phaseLevel"
        tools:text="20" />

    <TextView
        android:id="@+id/tvGameCountTxt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_7"
        android:layout_marginBottom="@dimen/base_sw_dp_2"
        android:text="@string/str_step"
        android:textColor="@color/text_body"
        android:textSize="@dimen/button_h3"
        app:layout_constraintBottom_toBottomOf="@+id/tvGameCount"
        app:layout_constraintStart_toEndOf="@+id/tvGameCount" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCourseVocabulary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:gravity="center"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintStart_toEndOf="@+id/phaseLevel"
        app:layout_constraintTop_toBottomOf="@+id/tvCourseCount"
        tools:text="词汇·300" />

    <View
        android:id="@+id/vCourseLine"
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:background="@color/border_divider"
        app:layout_constraintBottom_toBottomOf="@+id/tvCourseVocabulary"
        app:layout_constraintStart_toEndOf="@+id/tvCourseVocabulary"
        app:layout_constraintTop_toTopOf="@+id/tvCourseVocabulary" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCoursePhrase"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:gravity="center"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintStart_toEndOf="@+id/vCourseLine"
        app:layout_constraintTop_toBottomOf="@+id/tvCourseCount"
        tools:text="词汇·300" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStudy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_12"
        android:background="@drawable/shape_current_study"
        android:text="@string/str_current_study"
        android:textColor="@color/text_link"
        android:textSize="@dimen/button_h3"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLock"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_marginEnd="@dimen/base_sw_dp_12"
        android:background="@drawable/svg_locked_24_24_gray"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>