<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_44"
        android:layout_marginTop="@dimen/base_sw_dp_44"
        app:commTitle="@string/str_my_homework"
        app:layout_constraintTop_toTopOf="parent" />

    <com.snails.module.course.widget.MyHomeworkListView
        android:id="@+id/myHomeworkListView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_0"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:orientation="vertical"
        android:visibility="gone"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView" />

    <LinearLayout
        android:id="@+id/llyEmpty"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_0"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="@dimen/base_sw_dp_160"
            android:layout_height="@dimen/base_sw_dp_128"
            android:background="@drawable/svg_common_no_data" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="还没有提交的作业哦～"
            android:textColor="@color/text_body"
            android:textSize="@dimen/text_body_medium"
            tools:ignore="HardcodedText" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>