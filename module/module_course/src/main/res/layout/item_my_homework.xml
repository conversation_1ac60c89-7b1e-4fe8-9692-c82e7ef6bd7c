<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivTimeIcon"
            android:layout_width="@dimen/base_sw_dp_16"
            android:layout_height="@dimen/base_sw_dp_16"
            android:background="@drawable/svg_gray_circle_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/vLine"
            android:layout_width="@dimen/base_sw_dp_2"
            android:layout_height="0dp"
            android:background="@color/border_divider"
            app:layout_constraintBottom_toBottomOf="@+id/vSpaceBottom"
            app:layout_constraintEnd_toEndOf="@+id/ivTimeIcon"
            app:layout_constraintStart_toStartOf="@+id/ivTimeIcon"
            app:layout_constraintTop_toBottomOf="@+id/ivTimeIcon" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCommitTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_8"
            android:textColor="@color/text_describe"
            android:textSize="@dimen/text_body_footnote"
            app:layout_constraintBottom_toBottomOf="@+id/ivTimeIcon"
            app:layout_constraintStart_toEndOf="@+id/ivTimeIcon"
            app:layout_constraintTop_toTopOf="@+id/ivTimeIcon"
            tools:text="提交时间：2024-08-11 12:00" />

        <View
            android:id="@+id/vBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/base_sw_dp_8"
            android:layout_marginTop="@dimen/base_sw_dp_8"
            android:background="@drawable/shape_sbw_12r_bg"
            app:layout_constraintBottom_toBottomOf="@+id/vSpace"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivTimeIcon"
            app:layout_constraintTop_toBottomOf="@+id/tvCommitTime" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivCourseIcon"
            android:layout_width="@dimen/base_sw_dp_68"
            android:layout_height="@dimen/base_sw_dp_68"
            android:layout_margin="@dimen/base_sw_dp_8"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="@+id/vBg"
            app:layout_constraintTop_toTopOf="@+id/vBg" />

        <View
            android:id="@+id/vSpace"
            android:layout_width="@dimen/base_sw_dp_1"
            android:layout_height="@dimen/base_sw_dp_1"
            android:layout_marginTop="@dimen/base_sw_dp_7"
            app:layout_constraintStart_toStartOf="@+id/ivCourseIcon"
            app:layout_constraintTop_toBottomOf="@+id/ivCourseIcon" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvHomeworkState"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/base_sw_dp_12"
            android:drawablePadding="@dimen/base_sw_dp_6"
            android:textColor="@color/text_describe"
            android:textSize="@dimen/text_body_footnote"
            app:layout_constraintBottom_toBottomOf="@+id/ivCourseIcon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivCourseIcon"
            tools:drawableTop="@drawable/svg_right"
            tools:text="待批改" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCourseInfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_8"
            android:textColor="@color/text_body"
            android:textSize="@dimen/text_body_footnote"
            app:layout_constraintBottom_toTopOf="@+id/tvCourseName"
            app:layout_constraintStart_toEndOf="@+id/ivCourseIcon"
            app:layout_constraintTop_toTopOf="@+id/ivCourseIcon"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="听力1阶·第1课" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCourseName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/base_sw_dp_8"
            android:layout_marginTop="@dimen/base_sw_dp_4"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/text_headline"
            android:textSize="@dimen/headline_h4"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/ivCourseIcon"
            app:layout_constraintEnd_toStartOf="@+id/tvHomeworkState"
            app:layout_constraintStart_toEndOf="@+id/ivCourseIcon"
            app:layout_constraintTop_toBottomOf="@+id/tvCourseInfo"
            tools:text="who is it  who is it who is it who is it who is it who is it " />

        <View
            android:id="@+id/vSpaceBottom"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/base_sw_dp_24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vBg" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivNewReview"
        android:layout_width="@dimen/base_sw_dp_62"
        android:layout_height="@dimen/base_sw_dp_30"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_11"
        android:background="@drawable/svg_new_review"
        android:visibility="gone"
        tools:visibility="visible" />
</RelativeLayout>