<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clyRightQuoteContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_46"
        android:background="@drawable/shape_sbdk_12r_bg"
        android:maxWidth="@dimen/base_sw_dp_182"
        android:minWidth="@dimen/base_sw_dp_140"
        android:padding="@dimen/base_sw_dp_8">

        <!--右边 评论 引用 人名-->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvRightQuoteName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_describe"
            android:textSize="@dimen/text_body_small"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="何一:" />
        <!--右边 评论 引用 图片-->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/sivRightQuotePic"
            android:layout_width="@dimen/base_sw_dp_48"
            android:layout_height="@dimen/base_sw_dp_48"
            android:layout_marginTop="@dimen/base_sw_dp_2"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvRightQuoteName"
            app:shapeAppearance="@style/Rounded4Style" />
        <!--右边 评论 引用 文字-->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/sivRightQuoteTxt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_8"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_body"
            android:textSize="@dimen/text_body_small"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/sivRightQuotePic"
            app:layout_constraintTop_toTopOf="@+id/sivRightQuotePic"
            tools:text="1/6 who is it？o is it？ it？o is it it？o is it" />
        <!--右边 评论 引用 语音-->
        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottieRightQuoteLaBa"
            android:layout_width="@dimen/base_sw_dp_18"
            android:layout_height="@dimen/base_sw_dp_18"
            android:layout_marginStart="@dimen/base_sw_dp_8"
            android:layout_marginTop="@dimen/base_sw_dp_8"
            app:layout_constraintStart_toEndOf="@+id/sivRightQuotePic"
            app:layout_constraintTop_toBottomOf="@+id/sivRightQuoteTxt"
            app:lottie_autoPlay="false"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/lottie_radio_gray"
            tools:visibility="visible" />
        <!--右边 评论 引用 语音时长-->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvRightQuoteDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_8"
            android:textColor="@color/text_describe"
            android:textSize="@dimen/text_body_small"
            app:layout_constraintBottom_toBottomOf="@+id/lottieRightQuoteLaBa"
            app:layout_constraintStart_toEndOf="@+id/lottieRightQuoteLaBa"
            app:layout_constraintTop_toTopOf="@+id/lottieRightQuoteLaBa"
            tools:text="18″" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>