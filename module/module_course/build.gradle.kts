plugins {
    alias(libs.plugins.common.gradle)
    alias(libs.plugins.kotlin.ksp)
}

dependencies {
    implementation(libs.immersionbar)
    implementation(libs.permissions)

    implementation(project(":common:common_widget"))
    implementation(project(":module:module_base"))

    implementation(project(":base:base_audio"))
    implementation(project(":base:base_audio_player"))

    implementation(project(":base:base_video_player"))
    implementation(libs.bundles.coil)
    ksp(libs.router.ksp)
}
