package com.snails.module.webview

import android.annotation.SuppressLint
import android.content.Intent
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.router.RouterPath
import com.snails.base.share_ui.BottomShareDialog
import com.snails.module.base.BaseVBActivity
import com.snails.module.webview.databinding.ActivityWebviewBinding
import com.therouter.router.Route
import java.net.URLDecoder

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月19日 15:58:04
 */
@Route(path = RouterPath.WEB_HOME)
class WebViewActivity : BaseVBActivity<ActivityWebviewBinding>() {

    private var shareUrl: String? = null
    private var shareTitle: String? = null
    private var shareDesc: String? = null
    private var shareIconUrl: String? = null

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun initView() {
        binding.webView.settings.apply {
            this.javaScriptEnabled = true
        }
        binding.webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                //防止在站外打开，返回false，意味着请求过程里，不管有多少次的跳转请求（即新的请求地址），均交给webView自己处理
                return false
            }
        }
    }

    override fun initClick() {
        binding.commonTitleView.setBackClickListener {
            finish()
        }
    }

    override fun initData() {
        checkRouter(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.let {
            checkRouter(it)
        }
    }

    private fun checkRouter(intent: Intent) {
        //分享snail-router://com.snail.reading/web?url="xxxxxx"&title="xxx"&shareUrl="xxxxx"&shareTitle="xxxx"&shareDesc="xxxx"&shareIconUrl="xxxx"
        intent.getStringExtra("url")?.let { url ->
            binding.webView.loadUrl(URLDecoder.decode(url, "utf-8"))
        }
        intent.getStringExtra("title")?.let { title ->
            binding.commonTitleView.setTitle(URLDecoder.decode(title, "utf-8"))
        }
        shareUrl = intent.getStringExtra("shareUrl")
        shareTitle = intent.getStringExtra("shareTitle")
        shareDesc = intent.getStringExtra("shareDesc")
        shareIconUrl = intent.getStringExtra("shareIconUrl")
        if (!shareUrl.isNullOrEmpty()) {
            binding.commonTitleView.apply {
                setRightIcon(R.drawable.svg_share_bw)
                showRightIcon(true)
                setRightIconClickListener {
                    BottomShareDialog(
                        shareUrl = shareUrl,
                        shareTitle = shareTitle,
                        shareDesc = shareDesc,
                        shareIconUrl = shareIconUrl,
                    ).show(supportFragmentManager, "")
                }
            }
        }
    }
}