<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/svg_video_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivBack"
        android:layout_width="@dimen/base_sw_dp_32"
        android:layout_height="@dimen/base_sw_dp_32"
        android:layout_marginStart="@dimen/base_sw_dp_20"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:background="@drawable/svg_blue_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAlbumName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:textColor="@color/text_body"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/ivBack"
        app:layout_constraintEnd_toStartOf="@+id/videoListView"
        app:layout_constraintStart_toEndOf="@+id/ivBack"
        app:layout_constraintTop_toTopOf="@+id/ivBack"
        tools:text="小猪佩奇" />

    <com.snails.module.video.widget.VideoListView
        android:id="@+id/videoListView"
        android:layout_width="@dimen/base_sw_dp_159"
        android:layout_height="match_parent"
        android:background="@drawable/shape_video_list_bg"
        android:paddingStart="@dimen/base_sw_dp_20"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintEnd_toEndOf="parent"
        tools:ignore="RtlSymmetry" />

    <View
        android:id="@+id/vTvBg"
        android:layout_width="@dimen/base_sw_dp_518"
        android:layout_height="@dimen/base_sw_dp_296"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:background="@drawable/svg_video_tv_bg"
        app:layout_constraintEnd_toStartOf="@+id/videoListView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvAlbumName" />


    <View
        android:id="@+id/vVideoWhiteBg"
        android:layout_width="@dimen/base_sw_dp_447"
        android:layout_height="@dimen/base_sw_dp_255"
        android:background="@drawable/shape_video_white_bg"
        app:layout_constraintBottom_toBottomOf="@+id/vTvBg"
        app:layout_constraintEnd_toEndOf="@+id/vTvBg"
        app:layout_constraintStart_toStartOf="@+id/vTvBg"
        app:layout_constraintTop_toTopOf="@+id/vTvBg" />

    <com.snails.module.video.widget.VideoPlayerContainer
        android:id="@+id/videoPlayerContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="@+id/vTvBg"
        app:layout_constraintEnd_toEndOf="@+id/vTvBg"
        app:layout_constraintStart_toStartOf="@+id/vTvBg"
        app:layout_constraintTop_toTopOf="@+id/vTvBg"
        tools:background="#f0f"
        tools:layout_height="@dimen/base_sw_dp_247"
        tools:layout_width="@dimen/base_sw_dp_439" />

</androidx.constraintlayout.widget.ConstraintLayout>