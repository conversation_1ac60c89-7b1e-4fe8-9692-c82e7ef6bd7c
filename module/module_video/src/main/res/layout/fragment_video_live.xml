<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clyContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.snails.base.video_player.control.EmptyControlVideo
        android:id="@+id/gsyVideoPlayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <View
        android:id="@+id/vClick"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rlyLiveInfoBg"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/base_sw_dp_50"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_56"
        android:layout_marginEnd="@dimen/base_sw_dp_100"
        android:background="@drawable/shape_live_info_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/sivLiveHead"
            android:layout_width="@dimen/base_sw_dp_36"
            android:layout_height="@dimen/base_sw_dp_36"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/CircleStyle"
            tools:background="#00ff00" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLiveTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_6"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/button_h2"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@+id/sivLiveHead"
            app:layout_constraintTop_toTopOf="@+id/sivLiveHead"
            tools:ignore="RtlSymmetry"
            tools:text="陪跑营陪陪跑营陪" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLiveWatchCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_6"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/button_h3"
            app:layout_constraintStart_toEndOf="@+id/sivLiveHead"
            app:layout_constraintTop_toBottomOf="@+id/tvLiveTitle"
            tools:ignore="RtlSymmetry"
            tools:text="3333观看" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_marginTop="@dimen/base_sw_dp_56"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:background="@drawable/svg_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvReport"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_20"
        android:background="@drawable/shape_sbw_12r_tb3_lr8_bg"
        android:text="举报"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/text_body_footnote"
        app:layout_constraintBottom_toBottomOf="@+id/ivClose"
        app:layout_constraintEnd_toStartOf="@+id/ivClose"
        app:layout_constraintTop_toTopOf="@+id/ivClose"
        tools:ignore="HardcodedText" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clyEtContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.snails.module.video.widget.LiveChatListView
            android:id="@+id/liveChatListView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_268"
            android:layout_marginHorizontal="@dimen/base_sw_dp_16"
            android:layout_marginBottom="@dimen/base_sw_dp_16"
            android:fadingEdgeLength="@dimen/base_sw_dp_134"
            android:requiresFadingEdge="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/llyContent"
            app:stackFromEnd="true" />

        <LinearLayout
            android:id="@+id/llyContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/base_sw_dp_16"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/etContent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/base_sw_dp_8"
                android:layout_weight="1"
                android:background="@drawable/com_btn_mask2_bg_shape_30"
                android:gravity="center_vertical"
                android:hint="@string/str_say_something"
                android:imeOptions="actionSend"
                android:minHeight="@dimen/base_sw_dp_48"
                android:paddingHorizontal="@dimen/base_sw_dp_12"
                android:singleLine="true"
                android:textColor="@color/text_on_primary_button"
                android:textColorHint="@color/text_on_primary_button"
                android:textSize="@dimen/button_h2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tvSend"
                app:layout_constraintStart_toStartOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvSend"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/base_sw_dp_48"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/base_sw_dp_12"
                android:gravity="center"
                android:text="@string/str_send"
                android:textColor="@color/text_disable"
                android:textSize="@dimen/button_h2"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/etContent" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>