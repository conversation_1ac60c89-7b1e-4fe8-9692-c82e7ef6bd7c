<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/flyContainer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_live_chat_item_bg"
        android:orientation="horizontal"
        tools:ignore="UselessParent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/sivLiveHead"
            android:layout_width="@dimen/base_sw_dp_20"
            android:layout_height="@dimen/base_sw_dp_20"
            app:shapeAppearance="@style/CircleStyle"
            tools:background="#00ff00" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvNameAndChatContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_4"
            android:paddingEnd="@dimen/base_sw_dp_12"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/text_body_small"
            tools:ignore="RtlSymmetry"
            tools:text="黎明时分，太阳" />
    </LinearLayout>
</FrameLayout>