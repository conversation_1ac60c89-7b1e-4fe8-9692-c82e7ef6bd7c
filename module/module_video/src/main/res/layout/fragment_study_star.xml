<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clyContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.snails.base.video_player.control.StudyStarControlVideo
        android:id="@+id/gsyVideoPlayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPlay"
        android:layout_width="@dimen/base_sw_dp_72"
        android:layout_height="@dimen/base_sw_dp_72"
        android:layout_gravity="center_vertical"
        android:background="@drawable/svg_video_play"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_marginTop="@dimen/base_sw_dp_56"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:background="@drawable/svg_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivStudyStarHead"
        android:layout_width="@dimen/base_sw_dp_40"
        android:layout_height="@dimen/base_sw_dp_40"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginBottom="@dimen/base_sw_dp_57"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:shapeAppearance="@style/CircleStyle"
        tools:background="#00ff00" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStudyStarName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_6"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingEnd="@dimen/base_sw_dp_6"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/button_h2"
        app:layout_constraintStart_toEndOf="@+id/sivStudyStarHead"
        app:layout_constraintTop_toTopOf="@+id/sivStudyStarHead"
        tools:ignore="RtlSymmetry"
        tools:text="陪跑营陪" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStudyStarAge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_6"
        android:paddingEnd="@dimen/base_sw_dp_6"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/button_h3"
        app:layout_constraintStart_toEndOf="@+id/sivStudyStarHead"
        app:layout_constraintTop_toBottomOf="@+id/tvStudyStarName"
        tools:ignore="RtlSymmetry"
        tools:text="3333观看" />




</androidx.constraintlayout.widget.ConstraintLayout>