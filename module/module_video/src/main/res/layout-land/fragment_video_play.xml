<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clyContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.snails.base.video_player.control.NormalControlVideo
        android:id="@+id/gsyVideoPlayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPlay"
        android:layout_width="@dimen/base_sw_dp_72"
        android:layout_height="@dimen/base_sw_dp_72"
        android:layout_gravity="center_vertical"
        android:background="@drawable/svg_video_play"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_marginStart="@dimen/base_sw_dp_30"
        android:layout_marginTop="@dimen/base_sw_dp_24"
        android:background="@drawable/svg_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDownload"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_marginTop="@dimen/base_sw_dp_24"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:background="@drawable/svg_download"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>