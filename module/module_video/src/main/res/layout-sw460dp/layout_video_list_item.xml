<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/flyContainer"
    android:layout_width="@dimen/base_sw_dp_161"
    android:layout_height="@dimen/base_sw_dp_128">

    <View
        android:id="@+id/vChooseBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_video_list_item_choose_bg" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivVideoPic"
        android:layout_width="@dimen/base_sw_dp_141"
        android:layout_height="@dimen/base_sw_dp_80"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="@+id/vChooseBg"
        app:layout_constraintStart_toStartOf="@+id/vChooseBg"
        app:layout_constraintTop_toTopOf="@+id/vChooseBg"
        app:shapeAppearance="@style/Rounded8Style" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVideoName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_4"
        android:ellipsize="marquee"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:maxLines="1"
        android:scrollHorizontally="true"
        android:singleLine="true"
        app:layout_constraintTop_toBottomOf="@+id/ivVideoPic" />
</androidx.constraintlayout.widget.ConstraintLayout>