package com.snails.module.video.ui

import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.ToastUtils
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.shuyu.gsyvideoplayer.listener.GSYSampleCallBack
import com.shuyu.gsyvideoplayer.utils.GSYVideoType
import com.snails.base.image_loader.load
import com.snails.base.network.repository.storage.TeacherStorage
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.utils.SaveUtils
import com.snails.base.video_player.control.NormalControlVideo
import com.snails.module.base.BaseStateFragment
import com.snails.module.base.dialog.SimpleDownloadDialog
import com.snails.module.video.databinding.FragmentVideoPlayBinding

/**
 * @Description 仅仅播放一个链接的视频
 * <AUTHOR>
 * @CreateTime 2024年10月29日 14:31:55
 */
class VideoPlayFragment : BaseStateFragment<FragmentVideoPlayBinding>() {

    private var videoCover: String? = null //视频封面
    private var playUrl: String? = null //视频播放地址
    private var canDownload: Boolean = false //是否可以下载
    private var landscape: Boolean = false

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).hideBar(BarHide.FLAG_HIDE_STATUS_BAR).init()
    }

    override fun initClick() {
        super.initClick()
        binding.ivClose.singleClick {
            binding.gsyVideoPlayer.onVideoPause()
            val pop = it.findNavController().popBackStack()
            if (!pop) {
                binding.gsyVideoPlayer.onVideoPause()
                requireActivity().finish()
            }
        }
        binding.gsyVideoPlayer.apply {
            setVideoAllCallBack(object : GSYSampleCallBack() {
                override fun onAutoComplete(url: String?, vararg objects: Any?) {
                    //播放完了，objects[0]是title，object[1]是当前所处播放器（全屏或非全屏）
                    super.onAutoComplete(url, *objects)
                    //设置封面
                    binding.gsyVideoPlayer.getPlayCoverView()?.load(videoCover)
                    //显示封面UI布局
                    binding.gsyVideoPlayer.showCoverUi()
                }
            })
            normalControlListener = object : NormalControlVideo.NormalControlListener {
                override fun changeOrientation(orientation: Int) {
                    if (orientation == 1) {
                        activity?.intent?.putExtra("landscape", true)
                        activity?.let { it1 -> ScreenUtils.setLandscape(it1) }
                    } else {
                        activity?.intent?.putExtra("landscape", false)
                        activity?.let { it1 -> ScreenUtils.setPortrait(it1) }
                    }
                }

                override fun clickVideoSpeed() {
                    val videoPlaySpeed = TeacherStorage.me.getVideoPlaySpeed()
                    val newSpeed = when (videoPlaySpeed) {
                        1.0f -> {
                            1.5f
                        }

                        1.5f -> {
                            2.0f
                        }

                        else -> {
                            1.0f
                        }
                    }
                    TeacherStorage.me.setVideoPlaySpeed(newSpeed)
                    binding.gsyVideoPlayer.showSpeed(newSpeed)
                    binding.gsyVideoPlayer.speed = newSpeed
                }
            }
        }

        binding.ivDownload.singleClick {
            requestPermission {
                playUrl?.let { url ->
                    SimpleDownloadDialog(url, lifecycleScope = lifecycleScope) { filePath ->
                        context?.let { ctx ->
                            SaveUtils.saveVideoToAlbum(ctx, filePath)
                            ToastUtils.showLong("下载成功")
                        }
                    }.showNow(childFragmentManager, "")
                }
            }
        }
    }

    override fun initData() {
        super.initData()
        landscape = getBooleanExtra("landscape")
        videoCover = getStringExtra("videoCover")
        playUrl = getStringExtra("playUrl")
        canDownload = getBooleanExtra("canDownload")
        changeOrientation()
    }

    private fun changeOrientation() {
        playUrl ?: return
        if (landscape) {
            if (!ScreenUtils.isLandscape()) {
                activity?.let { it1 -> ScreenUtils.setLandscape(it1) }
            }
            playVideo(GSYVideoType.SCREEN_TYPE_FULL)
        } else {
            if (!ScreenUtils.isPortrait()) {
                activity?.let { it1 -> ScreenUtils.setPortrait(it1) }
            }
            playVideo(GSYVideoType.SCREEN_TYPE_DEFAULT)
        }
    }

    private fun playVideo(type: Int) {
        binding.gsyVideoPlayer.setUp(playUrl, true, "")
        GSYVideoType.setShowType(type)
        binding.gsyVideoPlayer.isLooping = false
        val videoPlaySpeed = TeacherStorage.me.getVideoPlaySpeed()
        binding.gsyVideoPlayer.speed = videoPlaySpeed
        binding.gsyVideoPlayer.showSpeed(videoPlaySpeed)
        binding.gsyVideoPlayer.startPlayLogic()
    }

    override fun onResume() {
        super.onResume()
        binding.gsyVideoPlayer.onVideoResume(false)
    }

    override fun onPause() {
        super.onPause()
        binding.gsyVideoPlayer.onVideoPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.gsyVideoPlayer.release()
    }

    /**
     * 请求权限
     */
    private fun requestPermission(callback: () -> Unit) {
        context?.let { ctx ->
            XXPermissions.with(ctx)
                .permission(
                    Permission.WRITE_EXTERNAL_STORAGE,
                )
                .request { _, allGranted ->
                    if (allGranted) {
                        callback.invoke()
                    }
                }
        }
    }
}