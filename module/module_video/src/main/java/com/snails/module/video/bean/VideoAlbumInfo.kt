package com.snails.module.video.bean

import com.snails.base.network.repository.info.ISame

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月21日 16:30:51
 */
data class VideoAlbumInfo(
    val albumCover: String? = null,
    var albumId: String? = null,
    val albumName: String? = null,
    val albumSize: String? = null,
    val description: String? = null
) : ISame() {
    override fun isSame(data: ISame): Boolean {
        if (data !is VideoAlbumInfo) {
            return false
        }
        if (albumCover != data.albumCover) {
            return false
        }
        if (albumId != data.albumId) {
            return false
        }
        if (albumName != data.albumName) {
            return false
        }
        if (albumSize != data.albumSize) {
            return false
        }
        if (description != data.description) {
            return false
        }
        return true
    }
}
