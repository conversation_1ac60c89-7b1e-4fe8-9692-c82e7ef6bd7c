package com.snails.module.video.viewbinder

import android.annotation.SuppressLint
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.SpanUtils
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.course.PopCommentInfo
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.video.databinding.LayoutLiveChatListItemBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月19日 14:11:27
 */
class LiveChatListViewBinder :
    ViewBindingDelegate<PopCommentInfo, LayoutLiveChatListItemBinding>() {


    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<LayoutLiveChatListItemBinding>,
        item: PopCommentInfo
    ) {
        holder.binding.apply {
            item.avatar?.let { url ->
                sivLiveHead.load(url)
            }
            SpanUtils.with(tvNameAndChatContent)
                .append("${item.nickName ?: ""} ").setBold()
                .append(item.comment ?: "")
                .create()
        }
        holder.binding.flyContainer.singleClick {
            KeyboardUtils.hideSoftInput(it)
        }
    }
}