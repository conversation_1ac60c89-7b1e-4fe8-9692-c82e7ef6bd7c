package com.snails.module.video.viewbinder

import android.annotation.SuppressLint
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.expand.AlbumItemInfo
import com.snails.base.utils.ext.invisible
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.video.databinding.LayoutVideoListItemBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月19日 14:11:27
 */
class VideoItemViewBinder(private val itemClick: (AlbumItemInfo, Int) -> Unit) :
    ViewBindingDelegate<AlbumItemInfo, LayoutVideoListItemBinding>() {

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<LayoutVideoListItemBinding>,
        item: AlbumItemInfo
    ) {
        holder.binding.apply {
            if (item.isPlaying) {
                vChooseBg.visible()
                tvVideoName.isSelected = true
            } else {
                vChooseBg.invisible()
                tvVideoName.isSelected = false
            }
            item.itemCover?.let {
                ivVideoPic.load(it)
            }
            tvVideoName.text = item.itemName ?: ""
        }

        holder.itemView.singleClick {
            itemClick.invoke(item, holder.layoutPosition)
        }
    }
}