package com.snails.module.video.widget

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import androidx.core.animation.addListener
import com.blankj.utilcode.util.ScreenUtils
import com.snails.base.network.repository.info.expand.AlbumItemInfo
import com.snails.base.network.repository.info.expand.VideoQualityInfo
import com.snails.base.utils.ext.addRadius
import com.snails.base.utils.ext.isPortraitPadMode
import com.snails.base.video_player.bean.ClarityInfo
import com.snails.base.video_player.control.VideoControlVideo
import com.snails.module.video.R

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月22日 09:48:24
 */
class VideoPlayerContainer @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    @Volatile
    private var runningScaleAnimation: Boolean = false //缩放动画是否正在运行
    private var screenHeight = ScreenUtils.getScreenHeight()
    private var screenWidth = ScreenUtils.getScreenWidth()
    private var gsyVideoPlayer: VideoControlVideo? = null

    private var videoSmallScreenWidth = 0 // 视频小屏时播放宽度
    private var videoSmallScreenHeight = 0 //视频小屏时播放高度
    private var transY = 0F //X 方向上的移动
    private var transX = 0F //Y 方向上的移动
    private var isFullScreenState = false //当前视频是否是全屏状态
    private var currentPlayState = 0 //当前播放状态

    var playListener: PlayListener? = null

    private var handler: Handler? = null

    init {
        handler = Handler(Looper.getMainLooper())
        // 使用 LayoutInflater 加载布局
        val layout = R.layout.layout_video_player_container
        val view = LayoutInflater.from(context).inflate(layout, this, true)
        gsyVideoPlayer = view.findViewById(R.id.gsyVideoPlayer)

        gsyVideoPlayer?.videoListener = object : VideoControlVideo.VideoListener {
            override fun screenClick(needFull: Boolean) {
                if (needFull) {
                    toFullScreenMode()
                } else {
                    toSmallScreenMode()
                }
            }

            override fun changePlayState(playing: Boolean) {
                if (playing) {
                    resumeVideo()
                } else {
                    pauseVideo()
                }
            }

            override fun chooseClarity(info: ClarityInfo) {
                playListener?.changeClarity(info)
            }

            override fun playState(state: Int) {
                currentPlayState = state
                runCatching {
                    playListener?.playState(currentPlayState)
                }
            }

            override fun resetTimer(isFull: Boolean) {
                autoFullScreen(isFull)
            }

        }
        initSizeInfo()
        toSmallScreenMode()
    }


    fun playVideo(info: AlbumItemInfo) {
        val url = info.videoQualityList?.firstOrNull()?.resource ?: info.itemResource ?: return
        setClarityList(info.videoQualityList)
        gsyVideoPlayer?.apply {
            setUp(url, true, "")
            startPlayLogic()
        }
    }

    /**
     * 自动全屏
     * @param fullScreen 是否要进行全屏
     */
    fun autoFullScreen(fullScreen: Boolean) {
        handler?.removeCallbacksAndMessages(null)
        if (!fullScreen || isFullScreenState) { //是否需要全屏，是否已经是全屏
            return
        }
        handler?.postDelayed({
            if (currentPlayState == 2) {
                toFullScreenMode()
            } else {
                //否则进入下一个轮询
                autoFullScreen(true)
            }
        }, 5000)
    }

    fun playVideo(url: String?) {
        url ?: return
        gsyVideoPlayer?.apply {
            setUp(url, true, "")
            startPlayLogic()
        }
    }

    /**
     * 设置清晰度
     */
    fun setClarityList(videoQualityList: List<VideoQualityInfo>?) {
        val list = videoQualityList ?: return
        val clarityList = convertVideoQuality(list)
        gsyVideoPlayer?.setClarityData(clarityList)
    }

    fun convertVideoQuality(videoQualityList: List<VideoQualityInfo>): List<ClarityInfo> {
        val list = mutableListOf<ClarityInfo>()
        videoQualityList.forEachIndexed { index, videoQualityInfo ->
            list.add(
                ClarityInfo(
                    qualityName = videoQualityInfo.qualityName,
                    resource = videoQualityInfo.resource,
                    choose = index == 0
                )
            )
        }
        return list
    }

    fun pauseVideo() {
        gsyVideoPlayer?.onVideoPause()
    }

    fun resumeVideo() {
        gsyVideoPlayer?.onVideoResume(false)
    }

    fun release() {
        gsyVideoPlayer?.release()
    }

    fun clean() {
        playListener = null
        gsyVideoPlayer?.videoListener = null
        gsyVideoPlayer = null
        handler?.removeCallbacksAndMessages(null)
        handler = null
    }

    fun getPlayer() = gsyVideoPlayer

    /**
     * 初始化尺寸相关的信息
     */
    private fun initSizeInfo() {
        transY = 0F
        transX = 0F
        //设置屏幕宽高
        if (screenHeight > screenWidth) {
            screenHeight = screenWidth.apply { //交换两个变量的值
                screenWidth = screenHeight
            }
        }
        if (context.isPortraitPadMode()) {
            //竖屏模式下，pad横屏大小
            videoSmallScreenWidth = resources.getDimension(R.dimen.base_sw_dp_550).toInt()
            videoSmallScreenHeight = resources.getDimension(R.dimen.base_sw_dp_310).toInt()
            val videoListViewWidth = resources.getDimension(R.dimen.base_sw_dp_201)
            transY =
                screenHeight / 2f - videoSmallScreenHeight / 2 + resources.getDimension(R.dimen.base_sw_dp_48) / 2
            transX = (screenWidth - videoListViewWidth - videoSmallScreenWidth) / 2
        } else {
            //竖屏模式下，手机横屏大小
            //设置视频播放器小屏宽高
            videoSmallScreenWidth = resources.getDimension(R.dimen.base_sw_dp_437).toInt()
            videoSmallScreenHeight = resources.getDimension(R.dimen.base_sw_dp_246).toInt()
            //左边视频列表宽度
            val videoListViewWidth = resources.getDimension(R.dimen.base_sw_dp_159)
            transY = resources.getDimension(R.dimen.base_sw_dp_81)
            transX = (screenWidth - videoListViewWidth - videoSmallScreenWidth) / 2
        }
    }

    /**
     * 全屏模式
     */
    private fun toFullScreenMode() {
        if (runningScaleAnimation) {
            return
        }
        val player = gsyVideoPlayer ?: return
        val valueAnimatorWidth = ValueAnimator.ofInt(videoSmallScreenWidth, screenWidth).apply {
            addUpdateListener { animation ->
                val layoutParams = player.layoutParams
                layoutParams.width = animation.animatedValue as Int
                player.layoutParams = layoutParams
            }
        }
        val valueAnimatorHeight = ValueAnimator.ofInt(videoSmallScreenHeight, screenHeight).apply {
            addUpdateListener { animation ->
                val layoutParams = player.layoutParams
                layoutParams.height = animation.animatedValue as Int
                player.layoutParams = layoutParams
            }
        }
        val translationX = ObjectAnimator.ofFloat(player, "translationX", transX, 0f)
        val translationY = ObjectAnimator.ofFloat(player, "translationY", transY, 0f)
        AnimatorSet().apply {
            playTogether(valueAnimatorWidth, valueAnimatorHeight, translationX, translationY)
            duration = 150
            interpolator = LinearInterpolator()
            addListener(
                onStart = {
                    runningScaleAnimation = true
                }, onEnd = {
                    isFullScreenState = true
                    runningScaleAnimation = false
                    gsyVideoPlayer?.addRadius(R.dimen.base_sw_dp_0)
                    autoFullScreen(false) //关闭定时器
                    gsyVideoPlayer?.setScreenState(true)
                }
            )
            start()
        }
    }

    /**
     * 小屏模式
     */
    private fun toSmallScreenMode() {
        if (runningScaleAnimation) {
            return
        }
        val player = gsyVideoPlayer ?: return

        val valueAnimatorWidth = ValueAnimator.ofInt(screenWidth, videoSmallScreenWidth).apply {
            addUpdateListener { animation ->
                val layoutParams = player.layoutParams
                layoutParams.width = animation.animatedValue as Int
                player.layoutParams = layoutParams
            }
        }
        val valueAnimatorHeight = ValueAnimator.ofInt(screenHeight, videoSmallScreenHeight).apply {
            addUpdateListener { animation ->
                val layoutParams = player.layoutParams
                layoutParams.height = animation.animatedValue as Int
                player.layoutParams = layoutParams
            }
        }

        val translationX = ObjectAnimator.ofFloat(player, "translationX", 0f, transX)
        val translationY = ObjectAnimator.ofFloat(player, "translationY", 0f, transY)
        AnimatorSet().apply {
            playTogether(valueAnimatorWidth, valueAnimatorHeight, translationX, translationY)
            duration = 150
            interpolator = LinearInterpolator()
            addListener(
                onStart = {
                    gsyVideoPlayer?.addRadius(R.dimen.base_sw_dp_15)
                }, onEnd = {
                    isFullScreenState = false
                    runningScaleAnimation = false
                    autoFullScreen(true) //开启定时器
                    gsyVideoPlayer?.setScreenState(false)
                }
            )
            start()
        }
    }

    interface PlayListener {
        fun changeClarity(info: ClarityInfo)
        fun playState(state: Int)
    }
}