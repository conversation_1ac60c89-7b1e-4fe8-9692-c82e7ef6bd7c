package com.snails.module.video.ui

import android.annotation.SuppressLint
import androidx.fragment.app.viewModels
import androidx.navigation.Navigation
import com.blankj.utilcode.util.GsonUtils
import com.gyf.immersionbar.ImmersionBar
import com.shuyu.gsyvideoplayer.utils.GSYVideoType
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.course.ContentDetailsInfo
import com.snails.base.network.repository.info.video.StudyStarDetailsInfo
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.BaseStateFragment
import com.snails.module.video.databinding.FragmentStudyStarBinding
import com.snails.module.video.viewmodel.VideoViewModel

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年10月29日 14:31:55
 */
class StudyStarFragment : BaseStateFragment<FragmentStudyStarBinding>() {

    private val videoViewModel: VideoViewModel by viewModels()

    override fun createViewModel() = videoViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(false).init()
    }

    override fun initClick() {
        super.initClick()
        binding.ivClose.singleClick {
            binding.gsyVideoPlayer.onVideoPause()
            val pop = Navigation.findNavController(it).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
    }

    override fun initData() {
        super.initData()
        activity?.intent?.getStringExtra("id")?.let {
            videoViewModel.queryContentDetails(it)
        }
    }

    override fun initObserve() {
        super.initObserve()
        videoViewModel.contentDetailsLiveData.observe(viewLifecycleOwner) {
            intViewInfo(it)
            binding.gsyVideoPlayer.setUp(it.resource, true, "")
            GSYVideoType.setShowType(GSYVideoType.SCREEN_TYPE_FULL)
            binding.gsyVideoPlayer.isLooping = true
            binding.gsyVideoPlayer.startPlayLogic()

        }
    }

    @SuppressLint("SetTextI18n")
    private fun intViewInfo(data: ContentDetailsInfo) {
        if (data.type == "SQUARE_LEARNING_STAR") {
            data.content?.let {
                val liveDetailsInfo = GsonUtils.fromJson(it, StudyStarDetailsInfo::class.java)
                binding.tvStudyStarName.text = liveDetailsInfo.nickName ?: ""
                binding.tvStudyStarAge.text = liveDetailsInfo.description ?: ""
                liveDetailsInfo.avatar?.let { url ->
                    binding.sivStudyStarHead.load(url)
                }
            }
        }
    }

    override fun onRetry() {
        super.onRetry()
        activity?.intent?.getStringExtra("id")?.let {
            videoViewModel.queryContentDetails(it)
        }
    }

    override fun onResume() {
        super.onResume()
        binding.gsyVideoPlayer.onVideoResume(false)
    }

    override fun onPause() {
        super.onPause()
        binding.gsyVideoPlayer.onVideoPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.gsyVideoPlayer.release()
    }
}