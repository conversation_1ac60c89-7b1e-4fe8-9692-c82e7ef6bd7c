package com.snails.module.video.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.ISame
import com.snails.base.network.repository.info.expand.AlbumItemInfo
import com.snails.module.video.R
import com.snails.module.video.bean.VideoAlbumInfo
import com.snails.module.video.viewbinder.VideoCountViewBinder
import com.snails.module.video.viewbinder.VideoItemViewBinder

/**
 * @Description 视频列表
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */
class VideoListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    var videoListListener: VideoListListener? = null
    private val listAdapter = MultiTypeAdapter()

    init {
        initView()
    }

    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(VideoAlbumInfo::class.java, VideoCountViewBinder())
            register(AlbumItemInfo::class.java, VideoItemViewBinder { info, pos ->
                videoListListener?.itemClick(info, pos)
                setChooseVideoItem(info)
            })
        }
        addItemDecoration(ItemDecoration())
        adapter = listAdapter
    }

    @Suppress("UNCHECKED_CAST")
    fun setData(new: List<ISame>) {
        val old = listAdapter.items as List<ISame>
        val diffResult: DiffUtil.DiffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition].javaClass == new[newItemPosition].javaClass
            }

            override fun getOldListSize(): Int {
                return old.size
            }

            override fun getNewListSize(): Int {
                return new.size
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition].isSame(new[newItemPosition])
            }
        })
        listAdapter.items = new
        diffResult.dispatchUpdatesTo(listAdapter)
    }

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State,
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect.top = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_4)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setChooseVideoItem(chooseItem: AlbumItemInfo) {
        listAdapter.items.forEach { info ->
            if (info is AlbumItemInfo) {
                info.isPlaying = (info.itemId == chooseItem.itemId)
            }
        }
        adapter?.notifyDataSetChanged()
    }

    interface VideoListListener {
        fun itemClick(data: AlbumItemInfo, pos: Int)
    }
}