package com.snails.module.video

import android.content.Intent
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.snails.base.audio.AudioPlayerManager
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.module.base.BaseStateActivity
import com.snails.module.video.databinding.ActivityVideoBinding
import com.therouter.router.Route

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年09月09日 09:23:01
 */
@Route(path = RouterPath.VIDEO_BASE)
class VideoActivity : BaseStateActivity<ActivityVideoBinding>() {

    override fun initData() {
        checkRouter(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.let {
            checkRouter(it)
        }
    }

    private fun checkRouter(intent: Intent) {
        AudioPlayerManager.release()
        HRouter.getOriginalPath(intent)?.let { targetFragmentPath ->
            dealRouter(targetFragmentPath)
        }
    }

    private fun dealRouter(fragmentPath: String?) {
        val navController =
            binding.navHostFragment.getFragment<NavHostFragment>().findNavController()
        val navGraph = navController.navInflater.inflate(R.navigation.video_navigation)
        val startDestinationId = when (fragmentPath) {
            RouterPath.VIDEO_HOME -> {
                R.id.videoHomeFragment
            }

            RouterPath.VIDEO_LIVE -> {
                R.id.videoLiveFragment
            }

            RouterPath.VIDEO_PLAY -> {
                R.id.videoPlayFragment
            }

            RouterPath.STUDY_STAR -> {
                R.id.studyStarFragment
            }

            else -> {
                R.id.videoHomeFragment
            }
        }
        navGraph.setStartDestination(startDestinationId)
        navController.graph = navGraph
    }
}