package com.snails.module.video.ui

import android.os.Handler
import android.os.Looper
import androidx.fragment.app.viewModels
import androidx.navigation.findNavController
import com.blankj.utilcode.util.ScreenUtils
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.shuyu.gsyvideoplayer.cache.CacheFactory
import com.shuyu.gsyvideoplayer.cache.ProxyCacheManager
import com.shuyu.gsyvideoplayer.listener.GSYSampleCallBack
import com.shuyu.gsyvideoplayer.player.PlayerFactory
import com.shuyu.gsyvideoplayer.utils.GSYVideoType
import com.snails.base.audio.AudioPlayerManager
import com.snails.base.network.repository.info.expand.AlbumItemInfo
import com.snails.base.utils.ext.addRadius
import com.snails.base.utils.ext.singleClick
import com.snails.base.video_player.bean.ClarityInfo
import com.snails.module.base.BaseStateFragment
import com.snails.module.base.dialog.RetryDialog
import com.snails.module.video.R
import com.snails.module.video.databinding.FragmentVideoHomeBinding
import com.snails.module.video.viewmodel.VideoViewModel
import com.snails.module.video.widget.VideoListView.VideoListListener
import com.snails.module.video.widget.VideoPlayerContainer
import tv.danmaku.ijk.media.exo2.Exo2PlayerManager

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年10月28日 10:27:39
 */
class VideoHomeFragment : BaseStateFragment<FragmentVideoHomeBinding>() {

    private val videoViewModel: VideoViewModel by viewModels()

    override fun createViewModel() = videoViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        activity?.let {
            if (ScreenUtils.isPortrait()) {
                ScreenUtils.setLandscape(it)
                ScreenUtils.setFullScreen(it)
            }
        }
        ImmersionBar.with(this).hideBar(BarHide.FLAG_HIDE_STATUS_BAR).init()
    }

    override fun initData() {
        super.initData()
        AudioPlayerManager.release()
        PlayerFactory.setPlayManager(Exo2PlayerManager::class.java)
        CacheFactory.setCacheManager(ProxyCacheManager::class.java)
        GSYVideoType.setShowType(GSYVideoType.SCREEN_TYPE_DEFAULT)
        videoViewModel.albumId = activity?.intent?.getStringExtra("id")
        videoViewModel.getAlbumList()
    }

    override fun initView() {
        super.initView()
        binding.videoPlayerContainer.addRadius(R.dimen.base_sw_dp_15)
        binding.videoPlayerContainer.getPlayer()?.apply {
            setVideoAllCallBack(object : GSYSampleCallBack() {
                override fun onAutoComplete(url: String?, vararg objects: Any?) {
                    //播放完了，objects[0]是title，object[1]是当前所处播放器（全屏或非全屏）
                    super.onAutoComplete(url, *objects)
                    videoViewModel.getPlayNextRes()?.let {
                        binding.videoListView.setChooseVideoItem(it)
                        binding.videoPlayerContainer.playVideo(it)
                        videoViewModel.historyRecord(
                            it.itemIndex,
                            videoViewModel.albumId,
                            "ALBUM_PAGE"
                        )
                        scrollToPosition()
                    }
                }
            })
        }
    }

    override fun initObserve() {
        super.initObserve()
        videoViewModel.apply {
            videoAlbumLiveData.observe(viewLifecycleOwner) {
                binding.tvAlbumName.text = it.albumName ?: ""
            }
            videoListLiveData.observe(viewLifecycleOwner) {
                binding.videoListView.setData(it)
            }
            currentPlayVideoLiveData.observe(viewLifecycleOwner) {
                binding.videoPlayerContainer.playVideo(it)
                scrollToPosition(300)
            }
        }
    }

    override fun initClick() {
        super.initClick()
        binding.ivBack.singleClick {
            val pop = it.findNavController().popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
        binding.videoListView.videoListListener = object : VideoListListener {
            override fun itemClick(data: AlbumItemInfo, pos: Int) {
                videoViewModel.currentPlayVideoInfo = data
                binding.videoPlayerContainer.playVideo(data)
                videoViewModel.historyRecord(data.itemIndex, videoViewModel.albumId, "ALBUM_PAGE")
                videoViewModel.currentPlayPos = pos + 1
                scrollToPosition()
                binding.videoPlayerContainer.autoFullScreen(true)
            }
        }

        binding.videoPlayerContainer.playListener = object : VideoPlayerContainer.PlayListener {
            override fun changeClarity(info: ClarityInfo) {
                binding.videoPlayerContainer.playVideo(info.resource)
            }

            override fun playState(state: Int) {
                //错误状态
                if (state == 7) {
                    runCatching {
                        RetryDialog {
                            //重试
                            videoViewModel.currentPlayVideoInfo?.let {
                                binding.videoPlayerContainer.playVideo(it)
                            }
                        }.show(childFragmentManager, "")
                    }
                }
            }

        }
    }

    override fun onRetry() {
        super.onRetry()
        videoViewModel.getAlbumList()
    }

    override fun onPause() {
        binding.videoPlayerContainer.pauseVideo()
        super.onPause()
    }

    override fun onDestroy() {
        binding.videoPlayerContainer.apply {
            getPlayer()?.setVideoAllCallBack(null)
            release()
            clean()
        }
        super.onDestroy()
    }

    /**
     * 滚动到播放列表
     */
    private fun scrollToPosition(delayMillis: Long = 0) {
        Handler(Looper.getMainLooper()).postDelayed({
            if (activity?.isFinishing != true) {
                runCatching {
                    binding.videoListView.smoothScrollToPosition(videoViewModel.currentPlayPos)
                }
            }
        }, delayMillis)
    }
}