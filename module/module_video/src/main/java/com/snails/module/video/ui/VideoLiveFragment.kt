package com.snails.module.video.ui

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.Rect
import android.os.Bundle
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.viewModels
import androidx.navigation.Navigation
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.gyf.immersionbar.ImmersionBar
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.utils.GSYVideoType
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.course.ContentDetailsInfo
import com.snails.base.network.repository.info.course.LiveDetailsInfo
import com.snails.base.network.repository.info.course.PopCommentInfo
import com.snails.base.network.repository.storage.UserStorage
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.BaseStateFragment
import com.snails.module.base.utils.SnailTextWatcher
import com.snails.module.video.R
import com.snails.module.video.databinding.FragmentVideoLiveBinding
import com.snails.module.video.viewmodel.VideoViewModel

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年10月28日 09:13:10
 */
class VideoLiveFragment : BaseStateFragment<FragmentVideoLiveBinding>() {

    private val videoViewModel: VideoViewModel by viewModels()

    private val userInfo = UserStorage.me.getUserInfo()

    override fun createViewModel() = videoViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(false).init()
    }

    override fun initView() {
        super.initView()
        val bottom = binding.clyEtContent.resources.getDimension(R.dimen.base_sw_dp_42).toInt()
        binding.clyContainer.viewTreeObserver.addOnGlobalLayoutListener {
            val rect = Rect()
            binding.clyContainer.getWindowVisibleDisplayFrame(rect)
            val screenHeight = binding.clyContainer.height
            val keypadHeight = screenHeight - rect.bottom

            if (keypadHeight > screenHeight * 0.15) {
                // 键盘弹出
                binding.llyContent.setBackgroundColor(Color.WHITE)
                binding.etContent.setBackgroundResource(R.drawable.com_btn_sb_bg_shape)
                binding.clyEtContent.translationY = -keypadHeight.toFloat() // 让目标View位于键盘之上
                binding.tvSend.visible()
                binding.etContent.setHintTextColor(ColorUtils.getColor(R.color.text_disable))
                binding.etContent.setTextColor(ColorUtils.getColor(R.color.text_headline))
                val layoutParams =
                    binding.clyEtContent.layoutParams as? ConstraintLayout.LayoutParams
                layoutParams?.setMargins(0, 0, 0, 0)
                binding.clyEtContent.layoutParams = layoutParams
            } else {
                // 键盘收回
                binding.llyContent.background = null
                binding.etContent.setBackgroundResource(R.drawable.com_btn_mask2_bg_shape_30)
                binding.clyEtContent.translationY = 0f // 重置位置
                binding.tvSend.gone()
                binding.etContent.setHintTextColor(ColorUtils.getColor(R.color.text_on_primary_button))
                binding.etContent.setTextColor(ColorUtils.getColor(R.color.text_on_primary_button))

                val layoutParams =
                    binding.clyEtContent.layoutParams as? ConstraintLayout.LayoutParams
                layoutParams?.setMargins(0, 0, 0, bottom)
                binding.clyEtContent.layoutParams = layoutParams

            }
        }
        // 监听输入框内容变化
        binding.etContent.addTextChangedListener(object : SnailTextWatcher() {
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (s.isNullOrEmpty()) {
                    binding.tvSend.setTextColor(ColorUtils.getColor(R.color.text_disable))
                } else {
                    binding.tvSend.setTextColor(ColorUtils.getColor(R.color.text_link))
                }
            }
        })
    }

    override fun initData() {
        super.initData()
        GSYVideoManager.releaseAllVideos()
        activity?.intent?.getStringExtra("id")?.let {
            videoViewModel.queryContentDetails(it)
        }
    }

    override fun initObserve() {
        super.initObserve()
        videoViewModel.contentDetailsLiveData.observe(viewLifecycleOwner) {
            intViewInfo(it)
            binding.gsyVideoPlayer.setUp(it.resource, true, "")
            binding.gsyVideoPlayer.isLooping = true
            GSYVideoType.setShowType(GSYVideoType.SCREEN_TYPE_FULL)
            binding.gsyVideoPlayer.startPlayLogic()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun intViewInfo(data: ContentDetailsInfo) {
        if (data.type == "SQUARE_LEARNING_SHOW") {
            data.content?.let {
                val liveDetailsInfo = GsonUtils.fromJson(it, LiveDetailsInfo::class.java)
                binding.tvLiveTitle.text = liveDetailsInfo.nickName ?: ""
                binding.tvLiveWatchCount.text = "${liveDetailsInfo.userCount ?: 0}"
                liveDetailsInfo.avatar?.let { url ->
                    binding.sivLiveHead.load(url)
                }
                liveDetailsInfo.popComments?.let { list ->
                    binding.liveChatListView.setData(list)
                }
            }
        }
    }

    override fun initClick() {
        super.initClick()
        binding.ivClose.singleClick {
            val pop = Navigation.findNavController(it).popBackStack()
            if (!pop) {
                binding.gsyVideoPlayer.onVideoPause()
                binding.gsyVideoPlayer.release()
                requireActivity().finish()
            }
        }
        binding.tvSend.singleClick {
            val content = binding.etContent.text.toString().trim()
            if (content.isNotEmpty()) {
                val pop = PopCommentInfo(
                    comment = content,
                    nickName = userInfo?.nickname ?: "",
                    avatar = userInfo?.avatar ?: ""
                )
                binding.liveChatListView.addData(pop)
                binding.etContent.text = null
            }
        }
        binding.vClick.singleClick {
            KeyboardUtils.hideSoftInput(it)
        }
        binding.tvReport.singleClick {
            HRouter.navigation(RouterPath.FEEDBACK, Bundle().apply {
                putString("type", "1")
            })
        }
    }

    override fun onRetry() {
        super.onRetry()
        activity?.intent?.getStringExtra("id")?.let {
            videoViewModel.queryContentDetails(it)
        }
    }

    override fun onResume() {
        super.onResume()
        binding.gsyVideoPlayer.onVideoResume(false)
    }

    override fun onPause() {
        super.onPause()
        binding.gsyVideoPlayer.onVideoPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.gsyVideoPlayer.release()
    }
}