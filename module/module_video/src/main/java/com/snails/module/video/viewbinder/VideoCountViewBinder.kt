package com.snails.module.video.viewbinder

import android.annotation.SuppressLint
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.video.bean.VideoAlbumInfo
import com.snails.module.video.databinding.LayoutVideoListCountBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月19日 14:11:27
 */
class VideoCountViewBinder :
    ViewBindingDelegate<VideoAlbumInfo, LayoutVideoListCountBinding>() {


    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<LayoutVideoListCountBinding>,
        item: VideoAlbumInfo
    ) {
        holder.binding.apply {
            tvCount.text = "共${item.albumSize ?: 0}集"
        }
    }
}