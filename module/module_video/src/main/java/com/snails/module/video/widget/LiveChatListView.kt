package com.snails.module.video.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.course.PopCommentInfo
import com.snails.module.video.R
import com.snails.module.video.viewbinder.LiveChatListViewBinder

/**
 * @Description 直播聊天
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */
class LiveChatListView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val listAdapter = MultiTypeAdapter()
    private val showListChat = mutableListOf<PopCommentInfo>()

    init {
        initView()
    }

    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(PopCommentInfo::class.java, LiveChatListViewBinder())
        }
        addItemDecoration(ItemDecoration())
        adapter = listAdapter
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addData(info: PopCommentInfo) {
        showListChat.add(info)
        listAdapter.items = showListChat
        listAdapter.notifyDataSetChanged()

        // 滚动到最底部
        this.post { // 当列表项足够多时，smoothScrollToPosition会导致滚动
            layoutManager?.smoothScrollToPosition(
                this,
                null,
                layoutManager!!.itemCount - 1
            )
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    @Suppress("UNCHECKED_CAST")
    fun setData(new: List<PopCommentInfo>) {
        showListChat.clear()
        showListChat.addAll(new)
        listAdapter.items = new
        listAdapter.notifyDataSetChanged()
        val old = listAdapter.items as List<PopCommentInfo>
        val diffResult: DiffUtil.DiffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition].javaClass == new[newItemPosition].javaClass
            }

            override fun getOldListSize(): Int {
                return old.size
            }

            override fun getNewListSize(): Int {
                return new.size
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition].isSame(new[newItemPosition])
            }
        })
        listAdapter.items = new
        diffResult.dispatchUpdatesTo(listAdapter)
    }

    override fun getBottomFadingEdgeStrength() = 0f

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State,
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8)
        }
    }
}