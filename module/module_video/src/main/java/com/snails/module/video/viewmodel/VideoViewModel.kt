package com.snails.module.video.viewmodel

import androidx.lifecycle.MutableLiveData
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.info.ISame
import com.snails.base.network.repository.info.course.ContentDetailsInfo
import com.snails.base.network.repository.info.expand.AlbumItemInfo
import com.snails.base.network.repository.info.expand.AlbumListInfo
import com.snails.module.base.BaseViewModel
import com.snails.module.base.bean.StateType
import com.snails.module.base.utils.SingleLiveEventLiveData
import com.snails.module.video.bean.VideoAlbumInfo

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年09月09日 15:21:12
 */
class VideoViewModel : BaseViewModel() {

    var albumId: String? = null

    //当前正在播放视频的信息
    var currentPlayVideoInfo: AlbumItemInfo? = null
    var currentPlayPos = 0 //当前正在播视频列表的下标
    val contentDetailsLiveData: MutableLiveData<ContentDetailsInfo> = SingleLiveEventLiveData()

    //当前视频专辑信息
    val videoAlbumLiveData: MutableLiveData<VideoAlbumInfo> = SingleLiveEventLiveData()

    //当前正在播放视频的信息
    val currentPlayVideoLiveData: MutableLiveData<AlbumItemInfo> = SingleLiveEventLiveData()
    val videoListLiveData: MutableLiveData<List<ISame>> = SingleLiveEventLiveData()

    fun queryContentDetails(contentId: String) {
        request(
            stateType = StateType.PAGE,
            request = {
                SnailRepository.me.queryContentDetails(contentId)
            },
            success = { data ->
                data?.let {
                    contentDetailsLiveData.value = it
                }
            }
        )
    }

    fun getAlbumList() {
        val aId = albumId ?: return
        request(
            stateType = StateType.PAGE,
            request = {
                SnailRepository.me.getAlbumList(aId)
            },
            success = { data ->
                data?.let {
                    val pair = convertData(it)
                    videoAlbumLiveData.value = pair.first
                    videoListLiveData.value = pair.second
                    currentPlayVideoInfo?.let { info ->
                        currentPlayVideoLiveData.value = info
                    }
                }
            }
        )
    }

    /**
     * 获取下一个可以播放的资源
     */
    fun getPlayNextRes(): AlbumItemInfo? {
        val list = videoListLiveData.value ?: return null
        ++currentPlayPos
        if (currentPlayPos >= list.size) {
            currentPlayPos = 1
        }
        return videoListLiveData.value?.get(currentPlayPos) as? AlbumItemInfo
    }

    /**
     * 更新拓展页点击记录：progress为空，targetId传 contentId，scene 传EXTEND_PAGE；
     * 更新播放记录：progress传列表资源序号 itemIndex，targetId传专辑 id，scene 传ALBUM_PAGE
     */
    fun historyRecord(
        progress: Int? = null,
        targetId: String? = null,
        scene: String,
    ) {
        val tId = targetId ?: return
        request(
            request = {
                SnailRepository.me.historyRecord(progress, tId, scene)
            }
        )
    }

    private fun convertData(albumListInfo: AlbumListInfo): Pair<VideoAlbumInfo, List<ISame>> {
        val list = mutableListOf<ISame>()
        val videoAlbumInfo = VideoAlbumInfo(
            albumCover = albumListInfo.albumCover,
            albumId = albumListInfo.albumId,
            albumName = albumListInfo.albumName,
            albumSize = albumListInfo.albumSize,
            description = albumListInfo.description
        )
        list.add(videoAlbumInfo)
        albumListInfo.items?.forEachIndexed { index, info ->
            val isPlay = (index == albumListInfo.currentIndex - 1)
            info.isPlaying = isPlay
            if (isPlay) {
                currentPlayPos = index + 1 //+1 因为第一个是标题
                currentPlayVideoInfo = info
            }
            list.add(info)
        }
        return Pair(videoAlbumInfo, list)
    }
}