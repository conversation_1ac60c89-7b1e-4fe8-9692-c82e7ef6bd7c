package com.snails.module.main.viewbinder

import com.snails.base.router.HRouter
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.main.bean.MenuBean
import com.snails.module.main.databinding.SquareMenuBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 13:29:47
 */
class SquareMenuViewBinder : ViewBindingDelegate<MenuBean, SquareMenuBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<SquareMenuBinding>,
        item: MenuBean
    ) {
        val size = item.items?.size
        when (size) {
            1 -> {
                holder.binding.menuItemOne.visible()
                holder.binding.menuItemTwo.gone()
                holder.binding.menuItemThree.gone()
                holder.binding.menuItemFour.gone()
                holder.binding.menuItemOne.setMenuIcon(item.items[0].cover)
                holder.binding.menuItemOne.setMenuTitle(item.items[0].title)
            }

            2 -> {
                holder.binding.menuItemOne.visible()
                holder.binding.menuItemTwo.visible()
                holder.binding.menuItemThree.gone()
                holder.binding.menuItemFour.gone()

                holder.binding.menuItemOne.setMenuIcon(item.items[0].cover)
                holder.binding.menuItemTwo.setMenuIcon(item.items[1].cover)
                holder.binding.menuItemOne.setMenuTitle(item.items[0].title)
                holder.binding.menuItemTwo.setMenuTitle(item.items[1].title)
            }

            3 -> {
                holder.binding.menuItemOne.visible()
                holder.binding.menuItemTwo.visible()
                holder.binding.menuItemThree.visible()
                holder.binding.menuItemFour.gone()

                holder.binding.menuItemOne.setMenuIcon(item.items[0].cover)
                holder.binding.menuItemTwo.setMenuIcon(item.items[1].cover)
                holder.binding.menuItemThree.setMenuIcon(item.items[2].cover)

                holder.binding.menuItemOne.setMenuTitle(item.items[0].title)
                holder.binding.menuItemTwo.setMenuTitle(item.items[1].title)
                holder.binding.menuItemThree.setMenuTitle(item.items[2].title)
            }

            4 -> {
                holder.binding.menuItemOne.visible()
                holder.binding.menuItemTwo.visible()
                holder.binding.menuItemThree.visible()
                holder.binding.menuItemFour.visible()
                holder.binding.menuItemOne.setMenuIcon(item.items[0].cover)
                holder.binding.menuItemTwo.setMenuIcon(item.items[1].cover)
                holder.binding.menuItemThree.setMenuIcon(item.items[2].cover)
                holder.binding.menuItemFour.setMenuIcon(item.items[3].cover)

                holder.binding.menuItemOne.setMenuTitle(item.items[0].title)
                holder.binding.menuItemTwo.setMenuTitle(item.items[1].title)
                holder.binding.menuItemThree.setMenuTitle(item.items[2].title)
                holder.binding.menuItemFour.setMenuTitle(item.items[3].title)
            }
        }
        holder.binding.menuItemOne.singleClick {
            item.items?.get(0)?.route?.let { it1 -> HRouter.navigation(it1) }
        }
        holder.binding.menuItemTwo.singleClick {
            item.items?.get(1)?.route?.let { it1 -> HRouter.navigation(it1) }
        }
        holder.binding.menuItemThree.singleClick {
            item.items?.get(2)?.route?.let { it1 -> HRouter.navigation(it1) }
        }
        holder.binding.menuItemFour.singleClick {
            item.items?.get(3)?.route?.let { it1 -> HRouter.navigation(it1) }
        }
    }
}