package com.snails.module.main.widget

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.ISame
import com.snails.base.network.repository.info.course.CourseDetailsInfo
import com.snails.module.main.R
import com.snails.module.main.bean.MarkingInfoBean
import com.snails.module.main.viewbinder.StudyCourseViewBinder
import com.snails.module.main.viewbinder.StudyMarkingInfoViewBinder

/**
 * @Description 学习页 列表
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */

class StudyListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val listAdapter = MultiTypeAdapter()

    var listener: ItemClickListener? = null

    init {
        initView()
    }

    private fun initView() {

        // 列表项
        listAdapter.apply {
            register(MarkingInfoBean::class.java, StudyMarkingInfoViewBinder())
            register(CourseDetailsInfo::class.java, StudyCourseViewBinder {
                listener?.changeCourse(it)
            })
        }
        adapter = listAdapter
        this.addItemDecoration(ItemDecoration())
    }


    @Suppress("UNCHECKED_CAST")
    fun setData(new: List<ISame>) {
        val old = listAdapter.items as List<ISame>
        val diffResult: DiffUtil.DiffResult = DiffUtil.calculateDiff(
            object : DiffUtil.Callback() {
                override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                    return old[oldItemPosition].javaClass == new[newItemPosition].javaClass
                }

                override fun getOldListSize(): Int {
                    return old.size
                }

                override fun getNewListSize(): Int {
                    return new.size
                }

                override fun areContentsTheSame(
                    oldItemPosition: Int,
                    newItemPosition: Int
                ): Boolean {
                    return old[oldItemPosition].isSame(new[newItemPosition])
                }
            }
        )
        listAdapter.items = new
        diffResult.dispatchUpdatesTo(listAdapter)
    }


    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State
        ) {
            super.getItemOffsets(outRect, view, parent, state)

            kotlin.runCatching {
                val position = parent.getChildAdapterPosition(view)
                val itemCount = parent.adapter?.itemCount ?: 0

                // 判断是否为最后一个数据项
                if (position == itemCount - 1) {
                    outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_60)
                } else {
                    outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_20)
                }
            }
        }
    }

    interface ItemClickListener {
        fun changeCourse(data: CourseDetailsInfo)
    }
}