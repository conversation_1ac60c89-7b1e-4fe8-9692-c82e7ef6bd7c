package com.snails.module.main.bean

import com.google.gson.annotations.SerializedName
import com.snails.base.network.repository.info.ISame
import com.snails.module.main.bean.base.PlateInfo

/**
 * @Description
 * <AUTHOR> @CreateTime 2024年11月28日 13:21:27
 */
data class AiTalkBean(
    @SerializedName("items")
    val items: List<AiTalkItemBean>? = null,
) : PlateInfo() {
    override fun isSame(data: ISame): <PERSON><PERSON><PERSON> {
        if (data !is AiTalkBean) {
            return false
        }
        if (items?.size != data.items?.size) {
            return false
        }
        items?.forEachIndexed { index, info ->
            if (data.items?.get(index)?.isSame(info) == false) {
                return false
            }
        }
        return true
    }
}