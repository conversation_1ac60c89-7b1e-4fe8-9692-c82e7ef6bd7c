package com.snails.module.main.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.LinearLayoutCompat
import com.snails.module.main.R

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月27日 15:03:46
 */
class StudyStepViewScore @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayoutCompat(context, attrs, defStyleAttr) {

    private val ivScoreOne: AppCompatImageView
    private val ivScoreTwo: AppCompatImageView
    private val ivScoreThree: AppCompatImageView

    init {
        // 使用 LayoutInflater 加载布局
        val layoutId = R.layout.study_step_view_score
        val view = LayoutInflater.from(context).inflate(layoutId, this, true)
        ivScoreOne = view.findViewById(R.id.ivScoreOne)
        ivScoreTwo = view.findViewById(R.id.ivScoreTwo)
        ivScoreThree = view.findViewById(R.id.ivScoreThree)
    }

    fun setScore(score: Int) {
        when (score) {
            0 -> {
                ivScoreOne.setBackgroundResource(R.drawable.svg_star_gray)
                ivScoreTwo.setBackgroundResource(R.drawable.svg_star_gray)
                ivScoreThree.setBackgroundResource(R.drawable.svg_star_gray)
            }

            1 -> {
                ivScoreOne.setBackgroundResource(R.drawable.svg_star_yellow)
                ivScoreTwo.setBackgroundResource(R.drawable.svg_star_gray)
                ivScoreThree.setBackgroundResource(R.drawable.svg_star_gray)
            }

            2 -> {
                ivScoreOne.setBackgroundResource(R.drawable.svg_star_yellow)
                ivScoreTwo.setBackgroundResource(R.drawable.svg_star_yellow)
                ivScoreThree.setBackgroundResource(R.drawable.svg_star_gray)
            }

            3 -> {
                ivScoreOne.setBackgroundResource(R.drawable.svg_star_yellow)
                ivScoreTwo.setBackgroundResource(R.drawable.svg_star_yellow)
                ivScoreThree.setBackgroundResource(R.drawable.svg_star_yellow)

            }
        }
    }


}