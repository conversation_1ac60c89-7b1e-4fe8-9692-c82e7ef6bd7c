package com.snails.module.main.widget

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.ISame
import com.snails.module.main.R
import com.snails.module.main.bean.AiTalkBean
import com.snails.module.main.bean.BookshelfBean
import com.snails.module.main.bean.StoryBean
import com.snails.module.main.viewbinder.ExpandAiTalkViewBinder
import com.snails.module.main.viewbinder.ExpandBookshelfViewBinder
import com.snails.module.main.viewbinder.ExpandStoryViewBinder

/**
 * @Description 拓展页 UI
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */

class ExpandListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    var itemClickListener: ItemClickListener? = null
    private val listAdapter = MultiTypeAdapter()

    init {
        initView()
    }

    private fun initView() {

        // 列表项
        listAdapter.apply {
            register(AiTalkBean::class.java, ExpandAiTalkViewBinder())
            register(BookshelfBean::class.java, ExpandBookshelfViewBinder(context) {
                itemClickListener?.itemClick(it)
            })
            register(StoryBean::class.java, ExpandStoryViewBinder(context) {
                itemClickListener?.itemClick(it)
            })
        }
        adapter = listAdapter
        this.addItemDecoration(ItemDecoration())
    }


    @Suppress("UNCHECKED_CAST")
    fun setData(new: List<ISame>) {
        val old = listAdapter.items as List<ISame>
        val diffResult: DiffUtil.DiffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition].javaClass == new[newItemPosition].javaClass
            }

            override fun getOldListSize(): Int {
                return old.size
            }

            override fun getNewListSize(): Int {
                return new.size
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition] == new[newItemPosition]
            }

        })
        listAdapter.items = new
        diffResult.dispatchUpdatesTo(listAdapter)
    }


    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State,
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            kotlin.runCatching {
                val position = parent.getChildAdapterPosition(view)
                val itemCount = parent.adapter?.itemCount ?: 0
                // 判断是否为最后一个数据项
                outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8)
            }
        }
    }

    interface ItemClickListener {
        fun itemClick(contentId: String?)
    }
}