package com.snails.module.main.viewbinder

import android.annotation.SuppressLint
import android.os.Bundle
import com.snails.base.image_loader.load
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.main.bean.MarkingInfoBean
import com.snails.module.main.databinding.StudyMarkingInfoBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 13:29:47
 */
class StudyMarkingInfoViewBinder : ViewBindingDelegate<MarkingInfoBean, StudyMarkingInfoBinding>() {
    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<StudyMarkingInfoBinding>,
        item: MarkingInfoBean
    ) {
        holder.binding.apply {
            if (item.msgCount != null && item.msgCount > 0) {
                vCirclePoint.visible()
                tvStudyMessage.text = "老师为你点评了${item.msgCount}条作业"
            } else {
                vCirclePoint.gone()
                tvStudyMessage.text = "暂无新点评"
            }
            clyContainer.singleClick {
                HRouter.navigation(RouterPath.HOMEWORK_CORRECT, Bundle().apply {
                    putString("courseStage", item.courseStage?.toString() ?: "")
                    putString("courseType", item.courseType ?: "")
                })
            }
        }
    }
}