package com.snails.module.main.bean

import com.snails.base.network.repository.info.ISame
import com.snails.module.main.bean.base.PlateInfo

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 13:14:27
 */
data class BannerBean(
    val items: List<CommonItemBean>? = null
) : PlateInfo() {
    override fun isSame(data: ISame): <PERSON><PERSON>an {
        if (data !is BannerBean) {
            return false
        }
        if (items?.size != data.items?.size) {
            return false
        }
        items?.forEachIndexed { index, info ->
            if (data.items?.get(index)?.isSame(info) == false) {
                return false
            }
        }
        return true
    }
}
