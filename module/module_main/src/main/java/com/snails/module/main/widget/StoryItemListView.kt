package com.snails.module.main.widget

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ScreenUtils
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.ISame
import com.snails.base.utils.ext.isPortraitPadMode
import com.snails.base.utils.ext.showItemSize
import com.snails.module.main.R
import com.snails.module.main.bean.CommonItemBean
import com.snails.module.main.viewbinder.ExpandStoryItemViewBinder

/**
 * @Description 拓展页 UI
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */
class StoryItemListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    var itemClickListener: ItemClickListener? = null
    private val listAdapter = MultiTypeAdapter()

    init {
        initView()
    }

    private fun initView() {
        val itemWidth = calculateItemWidth()
        // 列表项
        listAdapter.apply {
            register(CommonItemBean::class.java, ExpandStoryItemViewBinder(itemWidth) {
                itemClickListener?.itemClick(it)
            })

        }
        adapter = listAdapter
        this.addItemDecoration(ItemDecoration())
    }

    private fun calculateItemWidth(): Int {
        return (ScreenUtils.getScreenWidth() - spaceWidth()) / context.showItemSize()
    }

    @Suppress("UNCHECKED_CAST")
    fun setData(new: List<ISame>) {
        val old = listAdapter.items as List<ISame>
        val diffResult: DiffUtil.DiffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition].javaClass == new[newItemPosition].javaClass
            }

            override fun getOldListSize(): Int {
                return old.size
            }

            override fun getNewListSize(): Int {
                return new.size
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition] == new[newItemPosition]
            }

        })
        listAdapter.items = new
        diffResult.dispatchUpdatesTo(listAdapter)
    }


    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State,
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect.left = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_6)
            outRect.right = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_6)
            outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_20)
        }
    }



    private fun spaceWidth(): Int {
        if (context.isPortraitPadMode()) {
            return resources.getDimensionPixelSize(R.dimen.base_sw_dp_96)
        }
        return resources.getDimensionPixelSize(R.dimen.base_sw_dp_56)
    }

    interface ItemClickListener {
        fun itemClick(contentId: String?)
    }
}