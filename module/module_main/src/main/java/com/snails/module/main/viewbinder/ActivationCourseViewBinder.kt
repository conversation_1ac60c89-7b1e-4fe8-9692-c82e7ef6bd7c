package com.snails.module.main.viewbinder

import com.snails.base.router.HRouter
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.main.bean.ActivationCourseBean
import com.snails.module.main.databinding.ActivationCourseViewBinding

class ActivationCourseViewBinder :
    ViewBindingDelegate<ActivationCourseBean, ActivationCourseViewBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ActivationCourseViewBinding>,
        item: ActivationCourseBean
    ) {
        holder.itemView.singleClick {
            item.items?.firstOrNull()?.route?.let { it1 -> HRouter.navigation(it1) }
        }
    }
}