package com.snails.module.main.viewbinder

import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.main.bean.StudyStarBean
import com.snails.module.main.databinding.SquareStudyStarBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 13:29:47
 */
class SquareStudyStarViewBinder : ViewBindingDelegate<StudyStarBean, SquareStudyStarBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<SquareStudyStarBinding>,
        item: StudyStarBean
    ) {
        holder.binding.apply {
            tvStudyStarTitle.text = item.title
            studyStarView.setStudyStarData(item.items)
        }
    }
}