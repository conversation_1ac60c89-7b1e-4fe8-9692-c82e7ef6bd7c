package com.snails.module.main.viewbinder

import android.annotation.SuppressLint
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.course.CourseDetailsInfo
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.main.R
import com.snails.module.main.databinding.StudyCourseBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 13:29:47
 */
class StudyCourseViewBinder(private val changeCourseClick: (CourseDetailsInfo) -> Unit) :
    ViewBindingDelegate<CourseDetailsInfo, StudyCourseBinding>() {
    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<StudyCourseBinding>,
        item: CourseDetailsInfo
    ) {
        holder.binding.apply {
            tvCourseName.text = item.courseName
            tvExChangeCourse.text = "第${item.courseNum}课"
            tvCourseVocabulary.text = "词汇·${item.courseWordCount ?: "0"}"
            tvCoursePhrase.text = "短语·${item.coursePhraseCount ?: "0"}"
            item.courseCover?.let {
                sivCoursePic.load(
                    it,
                    placeholder = R.drawable.svg_w88_h88_placeholder,
                    error = R.drawable.svg_w88_h88_error
                )
            }
            item.steps?.let { studyStepView.setLessonData(it) }
            tvExChangeCourse.singleClick {
                changeCourseClick.invoke(item)
            }
        }
    }
}