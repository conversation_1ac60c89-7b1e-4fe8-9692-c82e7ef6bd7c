package com.snails.module.main.bean

import com.snails.base.network.repository.info.ISame

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月27日 13:23:35
 */
class MarkingInfoBean(
    val msgCount: Int? = null,
    val courseStage: Int? = null,
    val courseType: String? = null,
) : ISame() {
    override fun isSame(data: ISame): Boolean {
        if (data !is MarkingInfoBean) {
            return false
        }
        if (msgCount != data.msgCount) {
            return false
        }
        if (courseStage != data.courseStage) {
            return false
        }
        if (courseType != data.courseType) {
            return false
        }

        return true
    }
}