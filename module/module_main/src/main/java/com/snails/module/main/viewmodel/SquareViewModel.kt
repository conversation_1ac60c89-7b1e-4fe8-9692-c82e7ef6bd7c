package com.snails.module.main.viewmodel

import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.GsonUtils
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.info.ISame
import com.snails.base.network.repository.info.square.Region
import com.snails.module.base.BaseViewModel
import com.snails.module.base.bean.StateType
import com.snails.module.base.utils.SingleLiveEventLiveData
import com.snails.module.main.bean.ActivationCourseBean
import com.snails.module.main.bean.BannerBean
import com.snails.module.main.bean.CourseDescBean
import com.snails.module.main.bean.LiveBean
import com.snails.module.main.bean.MenuBean
import com.snails.module.main.bean.StudyStarBean

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 14:43:45
 */
class SquareViewModel : BaseViewModel() {
    // 广场列表数据
    val squareListDataLiveData: MutableLiveData<List<ISame>> = SingleLiveEventLiveData()

    fun getSquareData(stateType: StateType = StateType.NONE) {
        request(
            stateType = stateType,
            request = {
                SnailRepository.me.squareList(AppUtils.getAppVersionName())
            },
            success = {
                it?.regions?.let { list ->
                    squareListDataLiveData.value = convertSquareData(list)
                }
            }
        )
    }

    private fun convertSquareData(data: List<Region>): List<ISame> {
        val list = mutableListOf<ISame>()
        data.forEach { bean ->
            when (bean.type) {
                "SQUARE_TOP_BANNER" -> {
                    bean.content?.let {
                        kotlin.runCatching {
                            list.add(GsonUtils.fromJson(it, BannerBean::class.java))
                        }
                    }
                }

                "SQUARE_NAVIGATION" -> {
                    bean.content?.let {
                        kotlin.runCatching {
                            list.add(GsonUtils.fromJson(it, MenuBean::class.java))
                        }
                    }
                }

                "SQUARE_ACTIVATE_COURSE" -> {
                    bean.content?.let {
                        kotlin.runCatching {
                            list.add(GsonUtils.fromJson(it, ActivationCourseBean::class.java))
                        }
                    }
                }

                "SQUARE_LEARNING_SHOW" -> {
                    bean.content?.let {
                        kotlin.runCatching {
                            list.add(GsonUtils.fromJson(it, LiveBean::class.java))
                        }
                    }
                }

                "SQUARE_LEARNING_STAR" -> {
                    bean.content?.let {
                        kotlin.runCatching {
                            list.add(GsonUtils.fromJson(it, StudyStarBean::class.java))
                        }
                    }
                }

                "SQUARE_COURSE_DESCRIPTION" -> {
                    bean.content?.let {
                        kotlin.runCatching {
                            list.add(GsonUtils.fromJson(it, CourseDescBean::class.java))
                        }
                    }
                }
            }
        }
        return list
    }
}