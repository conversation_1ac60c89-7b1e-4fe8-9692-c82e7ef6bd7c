package com.snails.module.main.viewbinder

import android.content.Context
import androidx.recyclerview.widget.GridLayoutManager
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.showItemSize
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.main.bean.StoryBean
import com.snails.module.main.databinding.ExpandStoryViewBinding
import com.snails.module.main.widget.StoryItemListView.ItemClickListener

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:49:09
 */
class ExpandStoryViewBinder(
    private val context: Context,
    private val itemClick: (String?) -> Unit
) :
    ViewBindingDelegate<StoryBean, ExpandStoryViewBinding>() {

    private val itemClickListener = object : ItemClickListener {
        override fun itemClick(contentId: String?) {
            itemClick.invoke(contentId)
        }
    }

    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ExpandStoryViewBinding>, item: StoryBean
    ) {
        holder.binding.apply {
            tvTitle.text = item.title
            item.items?.let {
                storyItemListView.apply {
                    setHasFixedSize(true)
                    setData(it)
                }
            }
            storyItemListView.setLayoutManager(GridLayoutManager(context, context.showItemSize()))
            storyItemListView.itemClickListener = itemClickListener
            tvLookMore.singleClick {
                item.showMoreRoute?.let { it1 -> HRouter.navigation(it1) }
            }
            if (item.showMore == true) {
                tvLookMore.visible()
            } else {
                tvLookMore.gone()
            }
        }
    }
}