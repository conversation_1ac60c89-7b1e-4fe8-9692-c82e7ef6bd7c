package com.snails.module.main.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.snails.base.network.repository.info.course.StepInfo
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.singleClick
import com.snails.module.main.R

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月27日 15:03:46
 */
class StudyStepView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val lessonViewList: List<StudyStepItemView>
    private val lessonList: MutableList<StepInfo> = mutableListOf()

    init {
        // 使用 LayoutInflater 加载布局
        val layoutId = R.layout.study_step_view
        val view = LayoutInflater.from(context).inflate(layoutId, this, true)

        // 初始化数据
        lessonViewList = listOf(
            view.findViewById(R.id.lessonOne),
            view.findViewById(R.id.lessonTwo),
            view.findViewById(R.id.lessonThree),
            view.findViewById(R.id.lessonFour),
            view.findViewById(R.id.lessonFive),
            view.findViewById(R.id.lessonSix)
        )

        // 设置点击事件
        lessonViewList.forEachIndexed { index, itemView ->
            itemView.singleClick {
                selectItem(index)
            }
        }
    }

    fun setLessonData(list: List<StepInfo>) {
        lessonList.clear()
        lessonList.addAll(list)
        lessonList.forEachIndexed { index, lessonBean ->
            lessonViewList[index].setLessonInfo(lessonBean)
        }
    }

    private fun selectItem(index: Int) {
        val lesson = lessonList[index]
        ////"FINISHED":完成、"ACTIVE":进行中、"IN_ACTIVE":未开始
        if (lesson.stepStatus != "LOCKED") {
            lesson.route?.let {
                HRouter.navigation(it)
            }
        }
    }
}