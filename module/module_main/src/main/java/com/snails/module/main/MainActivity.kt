package com.snails.module.main

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.activity.viewModels
import androidx.media3.common.MediaMetadata
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import com.blankj.utilcode.util.ServiceUtils
import com.snails.base.audio.AudioPlayerManager
import com.snails.base.audio.interfaces.IPlayStateListener
import com.snails.base.audio.interfaces.IPlayingListener
import com.snails.base.game.GameEventListener
import com.snails.base.game.GameMethodManager
import com.snails.base.game.PreloadGameProcessService
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.expand.AlbumItemInfo
import com.snails.base.network.repository.storage.EnvironmentStorage
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.invisible
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.base.utils.utils.AnimationUtils
import com.snails.module.base.BaseVBActivity
import com.snails.module.base.utils.KeepStateNavigator
import com.snails.module.main.bean.FragmentVisibilityState
import com.snails.module.main.bean.Tab
import com.snails.module.main.bridge.UserInfoGameBridge
import com.snails.module.main.databinding.ActivityMainBinding
import com.snails.module.main.viewmodel.MainViewModel
import com.therouter.router.Route

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月20日 10:01:10
 */
@Route(path = RouterPath.MAIN_BASE)
class MainActivity : BaseVBActivity<ActivityMainBinding>() {

    private var audioPlaying = false //是否播放中
    private var currentPlayAlbumId: String? = null
    private var circleAnimation = AnimationUtils.createAnimation()
    private var userInfoGameBridge: UserInfoGameBridge? = null

    private val mainViewModel: MainViewModel by viewModels()

    private lateinit var navController: NavController

    private val playStateListener = object : IPlayStateListener {
        override fun playing() {

        }

        override fun buffering() {
            binding.lottieProgress.invisible()
            binding.lottieProgress.apply {
                visible()
                playAnimation()
            }
        }

        override fun pause() {
        }

        override fun stop() {
        }

        override fun error() {
        }
    }

    private val playingListener = object : IPlayingListener {
        override fun onIsPlayingChanged(isPlaying: Boolean) {
            audioPlaying = isPlaying
            showPlayUi(isPlaying)
        }

        override fun currentPlayMediaMetadata(mediaMetadata: MediaMetadata) {
            handleCurrentPlayData(mediaMetadata)
        }
    }

    override fun initData() {
        userInfoGameBridge = UserInfoGameBridge(this, object : GameEventListener {
            override fun gameExit() {
                Handler(Looper.getMainLooper()).postDelayed({
                    preloadInitGameProcess()
                }, 3000)
            }
        })
        userInfoGameBridge?.let {
            GameMethodManager.addGameBridgeMethod(it)
        }
        preloadInitGameProcess()
        mainViewModel.getAppConfig()
    }

    override fun initView() {
        window.statusBarColor = getColor(R.color.surface_background)

        setNavigation()
        binding.bottomNavigationView.setSelectedItem(0)
        mainViewModel.fragmentVisibilityLiveData.value = FragmentVisibilityState(Tab.SQUARE, false)
    }

    override fun initClick() {
        binding.bottomNavigationView.apply {
            setOnItemSelectedListener { index ->
                changeNavigate(index)
            }
        }
        binding.ivClose.singleClick {
            AudioPlayerManager.release()
            binding.llyPlayContainer.gone()
            circleAnimation.pauseCircleAnim()
            AudioPlayerManager.removePlayStateListener(playStateListener)
            AudioPlayerManager.removePlayingListener(playingListener)
        }
        binding.ivPlayState.singleClick {
            if (audioPlaying) {
                AudioPlayerManager.pause()
            } else {
                AudioPlayerManager.resume()
            }
        }
        binding.llyPlayContainer.singleClick {
            HRouter.navigation(RouterPath.AUDIO_HOME, Bundle().apply {
                putString("id", currentPlayAlbumId)
            })
        }
    }

    private fun setNavigation() {
        val navHostFragment =
            supportFragmentManager.findFragmentById(R.id.fragmentContainerView) as NavHostFragment
        navController = navHostFragment.navController
        val navigator = KeepStateNavigator(
            this, navHostFragment.childFragmentManager, R.id.fragmentContainerView
        )
        navController.navigatorProvider.addNavigator(navigator)
        navController.setGraph(R.navigation.main_navigation)
    }

    private fun changeNavigate(index: Int) {
        when (index) {
            0 -> {
                mainViewModel.fragmentVisibilityLiveData.value =
                    FragmentVisibilityState(Tab.SQUARE, false)
                navController.navigate(R.id.squareFragment)
            }

            1 -> {
                mainViewModel.fragmentVisibilityLiveData.value =
                    FragmentVisibilityState(Tab.STUDY, false)
                navController.navigate(R.id.studyFragment)
            }

            2 -> {
                mainViewModel.fragmentVisibilityLiveData.value =
                    FragmentVisibilityState(Tab.EXPAND, false)
                navController.navigate(R.id.expandFragment)
            }

            3 -> {
                mainViewModel.fragmentVisibilityLiveData.value =
                    FragmentVisibilityState(Tab.MINE, false)
                navController.navigate(R.id.mineFragment)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mainViewModel.fragmentVisibilityLiveData.value?.let {
            mainViewModel.fragmentVisibilityLiveData.value =
                FragmentVisibilityState(it.currentVisibilityTab, false)
        }
        checkAudioIsPlaying()
    }

    override fun onStop() {
        mainViewModel.fragmentVisibilityLiveData.value?.let {
            mainViewModel.fragmentVisibilityLiveData.value =
                FragmentVisibilityState(it.currentVisibilityTab, true)
        }
        super.onStop()
    }

    private fun checkAudioIsPlaying() {
        val currentPlayInfo = AudioPlayerManager.queryPlayingInfo()
        val mediaMetadata = currentPlayInfo.mediaMetadata
        if (mediaMetadata == null) {
            binding.llyPlayContainer.gone()
            return
        }
        audioPlaying = currentPlayInfo.isPlaying
        showPlayUi(currentPlayInfo.isPlaying)
        if (audioPlaying) {
            circleAnimation.startCircleAnim(binding.sivAlbumPic)
        }
        handleCurrentPlayData(mediaMetadata)
        AudioPlayerManager.addPlayStateListener(playStateListener)
        AudioPlayerManager.addPlayingListener(playingListener)
    }

    private fun handleCurrentPlayData(mediaMetadata: MediaMetadata) {
        (mediaMetadata.extras?.getSerializable("data") as? AlbumItemInfo)?.let { info ->
            binding.apply {
                currentPlayAlbumId = info.albumId
                llyPlayContainer.visible()
                mediaMetadata.artworkUri?.toString()?.let {
                    sivAlbumPic.load(it)
                }
                tvStoryName.text = info.itemName
                tvStoryName.isSelected = true
            }
        }
    }

    private fun showPlayUi(isPlaying: Boolean) {
        binding.ivPlayState.visible()
        binding.lottieProgress.gone()
        if (isPlaying) {
            circleAnimation.startCircleAnim(binding.sivAlbumPic)
            binding.ivPlayState.setBackgroundResource(R.drawable.svg_play_play)
        } else {
            circleAnimation.pauseCircleAnim()
            binding.ivPlayState.setBackgroundResource(R.drawable.svg_play_pause)
        }
    }

    private fun preloadInitGameProcess() {
        Looper.myQueue().addIdleHandler {
            preloadLoadGameProcess()
            if (EnvironmentStorage.me.getAutoUploadCrashLog()) {
                mainViewModel.uploadCrashLog()
            }
            false //返回false表示只执行一次
        }
    }

    private fun preloadLoadGameProcess() {
        kotlin.runCatching {
            if (!ServiceUtils.isServiceRunning(PreloadGameProcessService::class.java.name)) {
                val intent = Intent(this, PreloadGameProcessService::class.java)
                startService(intent)
            }
        }
    }

    override fun onDestroy() {
        userInfoGameBridge?.clean()
        super.onDestroy()
    }
}