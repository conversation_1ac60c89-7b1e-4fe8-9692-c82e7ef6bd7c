package com.snails.module.main.widget.tab

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity.CENTER_HORIZONTAL
import android.view.LayoutInflater
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.appcompat.widget.LinearLayoutCompat
import com.snails.module.main.R

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月20日 10:51:51
 */
class BottomNavigationItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayoutCompat(context, attrs, defStyleAttr) {

    private val ivItemPic: AppCompatImageView
    private val tvItemTxt: AppCompatTextView

    private var selectedRes: Int = 0
    private var unselectedRes: Int = 0

    init {
        val layoutId = R.layout.tab_bottom_navigation_view_item
        // 初始化布局
        val view = LayoutInflater.from(context).inflate(layoutId, this, true)
        ivItemPic = view.findViewById(R.id.ivItemPic)
        tvItemTxt = view.findViewById(R.id.tvItemTxt)

        // 读取自定义属性
        attrs?.let {
            val ta = context.obtainStyledAttributes(it, R.styleable.BottomNavigationItemView, 0, 0)
            selectedRes = ta.getResourceId(R.styleable.BottomNavigationItemView_selectedIcon, 0)
            unselectedRes = ta.getResourceId(R.styleable.BottomNavigationItemView_unselectedIcon, 0)
            val labelText = ta.getString(R.styleable.BottomNavigationItemView_labelText) ?: ""

            setLabelText(labelText)
            setItemSelected(false)
            ta.recycle()
        }

        orientation = VERTICAL
        gravity = CENTER_HORIZONTAL
    }

    // 设置标签文本
    private fun setLabelText(text: String) {
        tvItemTxt.text = text
    }

    // 设置选中状态
    fun setItemSelected(isSelected: Boolean) {
        val resId = if (isSelected) {
            selectedRes
        } else {
            unselectedRes
        }
        ivItemPic.setImageResource(resId)
        tvItemTxt.isEnabled = isSelected
    }
}