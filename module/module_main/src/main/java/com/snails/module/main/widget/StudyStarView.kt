package com.snails.module.main.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView
import com.snails.module.main.adapter.StudyStarAdapter
import com.snails.module.main.bean.CommonItemBean
import com.snails.module.main.bean.StudyStarColumnBean
import java.lang.ref.WeakReference

/**
 * @Description 学习之星横向滚动
 * <AUTHOR>
 * @CreateTime 2024年08月21日 15:45:10
 */
class StudyStarView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val studyStarAdapter = StudyStarAdapter(context)
    private var running = false //标示是否正在自动轮询
    private var autoPollTask: AutoPollTask? = null
    private var canScroll = false //标示是否可以自动轮询
    private val timeAutoPoll: Long = 16

    init {
        initView()
        autoPollTask = AutoPollTask(this)

    }

    private fun initView() {
        this.adapter = studyStarAdapter
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setStudyStarData(opusList: List<CommonItemBean>?) {
        opusList?.let { list ->
            val columnList = mutableListOf<StudyStarColumnBean>()
            for (i in list.indices step 2) {
                // 检查索引 i 和 i+1 是否在集合范围内
                if (i < list.size - 1) {
                    val firstData = list[i]
                    val secondData = list[i + 1]
                    val excitingOpusColumnInfo = StudyStarColumnBean(
                        mutableListOf<CommonItemBean>().apply {
                            this.add(firstData)
                            this.add(secondData)
                        }
                    )
                    columnList.add(excitingOpusColumnInfo)
                }
            }
            canScroll = calculationCanScroll(context, columnList.size)
            if (canScroll) {
                start()
            }
            studyStarAdapter.setContents(columnList, canScroll)
            studyStarAdapter.notifyDataSetChanged()
        }
    }

    private fun calculationCanScroll(context: Context, listSize: Int): Boolean {
        return listSize > 2
    }

    /**
     * @param currentFragmentShow 当前界面是否可见，如果不可见，停止滚动
     */
    fun updateFragmentShow(currentFragmentShow: Boolean) {
        if (!currentFragmentShow) {
            stop()
        } else {
            start()
        }
    }

    fun start() {
        if (running || !canScroll) {
            stop()
        }
        running = true
        postDelayed(autoPollTask, timeAutoPoll)
    }

    fun stop() {
        if (!running) {
            return
        }
        running = false
        removeCallbacks(autoPollTask)
    }

    internal class AutoPollTask(reference: StudyStarView?) : Runnable {

        private val mReference: WeakReference<StudyStarView> =
            WeakReference<StudyStarView>(reference)

        override fun run() {
            val recyclerView: StudyStarView? = mReference.get()
            if (recyclerView != null && recyclerView.running && recyclerView.canScroll) {
                recyclerView.scrollBy(2, 0)
                recyclerView.postDelayed(recyclerView.autoPollTask, recyclerView.timeAutoPoll)
            }
        }
    }
}