package com.snails.module.main.widget

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import android.view.ViewTreeObserver
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.builder.GSYVideoOptionBuilder
import com.shuyu.gsyvideoplayer.utils.GSYVideoType
import com.shuyu.gsyvideoplayer.video.base.GSYVideoView.CURRENT_STATE_PAUSE
import com.shuyu.gsyvideoplayer.video.base.GSYVideoView.CURRENT_STATE_PLAYING
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.ISame
import com.snails.base.video_player.control.EmptyControlVideo
import com.snails.module.main.R
import com.snails.module.main.bean.ActivationCourseBean
import com.snails.module.main.bean.BannerBean
import com.snails.module.main.bean.CourseDescBean
import com.snails.module.main.bean.LiveBean
import com.snails.module.main.bean.MenuBean
import com.snails.module.main.bean.StudyStarBean
import com.snails.module.main.viewbinder.ActivationCourseViewBinder
import com.snails.module.main.viewbinder.SquareBannerViewBinder
import com.snails.module.main.viewbinder.SquareCourseDescViewBinder
import com.snails.module.main.viewbinder.SquareLiveViewBinder
import com.snails.module.main.viewbinder.SquareMenuViewBinder
import com.snails.module.main.viewbinder.SquareStudyStarViewBinder

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 13:12:03
 */
class SquareListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val listAdapter = MultiTypeAdapter()

    private var firstVisibleItem = 0
    private var lastVisibleItem = 0
    private var visibleCount = 0
    private var firstTime = true //是否是第一次显示在屏幕上
    var playerValid = false //页面上有播放器正在播放，且是有效的播放
    var gsyVideoPlayer: EmptyControlVideo? = null

    init {
        initView()
        addOnScrollListener(object : OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                firstTime = false
                listenerScroll(recyclerView, newState)
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                listenerScroll(recyclerView, recyclerView.scrollState)
            }
        })

        if (firstTime) {
            viewTreeObserver?.addOnGlobalLayoutListener(object :
                ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    if (!firstTime) {
                        viewTreeObserver?.removeOnGlobalLayoutListener(this)
                    }
                    if (childCount > 0 && firstTime) {
                        listenerScroll(this@SquareListView, SCROLL_STATE_IDLE)
                    }
                }
            })
        }
    }


    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(BannerBean::class.java, SquareBannerViewBinder())
            register(MenuBean::class.java, SquareMenuViewBinder())
            register(ActivationCourseBean::class.java, ActivationCourseViewBinder())
            register(LiveBean::class.java, SquareLiveViewBinder(createGSYVideoOptionBuilder()))
            register(StudyStarBean::class.java, SquareStudyStarViewBinder())
            register(CourseDescBean::class.java, SquareCourseDescViewBinder())
        }
        adapter = listAdapter
        this.addItemDecoration(ItemDecoration())
    }

    @Suppress("UNCHECKED_CAST")
    fun setData(new: List<ISame>) {
        val old = listAdapter.items as List<ISame>
        val diffResult: DiffUtil.DiffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition].javaClass == new[newItemPosition].javaClass
            }

            override fun getOldListSize(): Int {
                return old.size
            }

            override fun getNewListSize(): Int {
                return new.size
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition] == new[newItemPosition]
            }

        })
        listAdapter.items = new
        diffResult.dispatchUpdatesTo(listAdapter)
    }

    private fun createGSYVideoOptionBuilder(): GSYVideoOptionBuilder {
        //静音
        GSYVideoManager.instance().isNeedMute = true
        return GSYVideoOptionBuilder().apply {
            setIsTouchWiget(false)
            setCacheWithPlay(false)
            setRotateViewAuto(true)
            setLooping(true)
            setLockLand(true)
            setPlayTag("SquareListView")
            setShowFullAnimation(true)
            setNeedLockFull(true)
        }
    }

    fun listenerScroll(recyclerView: RecyclerView, newState: Int) {
        playerValid = false
        //停止时，计算当前第一个完全可见的视频卡片。
        if (newState == SCROLL_STATE_IDLE) {
            val layoutManager = recyclerView.layoutManager
            if (layoutManager is LinearLayoutManager) {
                firstVisibleItem = layoutManager.findFirstVisibleItemPosition()
                lastVisibleItem = layoutManager.findLastVisibleItemPosition()
                if (firstVisibleItem != NO_POSITION && lastVisibleItem != NO_POSITION) {
                    visibleCount = lastVisibleItem - firstVisibleItem
                    autoPlayVideo(recyclerView)
                }
            }
        } else {
            gsyVideoPlayer?.let {
                if (it.currentState == CURRENT_STATE_PLAYING) {
                    it.onVideoPause()
                }
            }
        }
    }

    /**
     * 循环遍历 可见区域的播放器
     * 然后通过 getLocalVisibleRect(rect)方法计算出哪个播放器完全显示出来
     *
     * @param recyclerView
     */
    private fun autoPlayVideo(recyclerView: RecyclerView) {
        for (i in 0..visibleCount) {
            val child = recyclerView.getChildAt(i)
            val player = child?.findViewById<View>(R.id.gsyVideoPlayer) as? EmptyControlVideo
            if (player != null) {
                firstTime = false
                val rect = Rect()
                player.getLocalVisibleRect(rect)
                val videoHeight = player.height
                if (rect.top == 0 && rect.bottom == videoHeight) {
                    if (player == gsyVideoPlayer && (gsyVideoPlayer?.currentState == CURRENT_STATE_PAUSE)) {
                        gsyVideoPlayer?.onVideoResume(false)
                        playerValid = true
                    } else {
                        gsyVideoPlayer = player
                        GSYVideoType.setShowType(GSYVideoType.SCREEN_TYPE_FULL)
                        gsyVideoPlayer?.startPlayLogic()
                        playerValid = true
                    }
                    break
                }
            }
        }
    }

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State,
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            kotlin.runCatching {
                val position = parent.getChildAdapterPosition(view)
                val itemCount = parent.adapter?.itemCount ?: 0
                // 判断是否为最后一个数据项
                if (position == itemCount - 1) {
                    outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_60)
                } else {
                    outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_20)
                }
            }
        }
    }
}