package com.snails.module.main.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.snails.base.image_loader.load
import com.snails.base.utils.ext.singleClick
import com.snails.module.main.R

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 13:38:04
 */
class MenuItem @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val ivMenuIcon: AppCompatImageView
    private val tvMenuTitle: AppCompatTextView
    private val itemView: View

    init {
        // 使用 LayoutInflater 加载布局
        val layoutId = R.layout.square_menu_item_view
        itemView = LayoutInflater.from(context).inflate(layoutId, this, true)

        ivMenuIcon = itemView.findViewById(R.id.ivMenuIcon)
        tvMenuTitle = itemView.findViewById(R.id.tvMenuTitle)

        // 读取自定义属性（如果有）
        attrs?.let {
            val ta = context.obtainStyledAttributes(it, R.styleable.MenuItem, 0, 0)

            val itemIcon =
                ta.getResourceId(R.styleable.MenuItem_menuIcon, R.drawable.svg_common_back)
            val itemTitle = ta.getString(R.styleable.MenuItem_menuTitle) ?: ""

            setMenuIcon(itemIcon)
            setMenuTitle(itemTitle)

            ta.recycle()
        }
    }

    // 设置文字
    fun setMenuTitle(title: String?) {
        tvMenuTitle.text = title ?: ""
    }

    // 设置左边的图标
    fun setMenuIcon(id: Int) {
        ivMenuIcon.setImageResource(id)
    }

    // 设置左边的图标
    fun setMenuIcon(url: String?) {
        if (url != null) {
            ivMenuIcon.load(url)
        }
    }

    // 设置Item点击事件
    fun setMenuClickListener(listener: OnClickListener) {
        itemView.singleClick(listener)
    }
}