package com.snails.module.main.viewbinder

import android.os.Bundle
import com.snails.base.image_loader.load
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.main.R
import com.snails.module.main.bean.AiTalkBean
import com.snails.module.main.databinding.ExpandAitalkViewBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:49:09
 */
class ExpandAiTalkViewBinder : ViewBindingDelegate<AiTalkBean, ExpandAitalkViewBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ExpandAitalkViewBinding>, item: AiTalkBean
    ) {
        holder.binding.apply {
            tvAiTitle.text = item.title
            item.items?.firstOrNull()?.let { info ->
                info.cover?.let {
                    ivAiTalkPic.load(
                        it,
                        error = R.drawable.svg_w343_h137_error,
                        placeholder = R.drawable.svg_w343_h137_placeholder
                    )
                }
                tvTopicName.text = info.topicName ?: ""
                holder.itemView.singleClick {
                    info.route?.let { it1 ->
                        HRouter.navigation(it1, Bundle().apply {
                            putString("topicId", info.topicId)
                            putString("topicName", info.topicName)
                            putString("speakerName", info.speakerName)
                            putString("speakerAvatar", info.speakerAvatar)
                            putString("ringtone", info.ringtone)
                        })
                    }
                }
            }
        }
    }
}