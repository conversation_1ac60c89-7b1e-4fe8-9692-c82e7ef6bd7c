package com.snails.module.main.viewbinder

import com.snails.base.router.HRouter
import com.snails.base.utils.ext.addRadius
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.main.R
import com.snails.module.main.bean.CommonItemBean
import com.snails.module.main.databinding.ExpandBookshelfViewItemBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:49:09
 */
class ExpandBookshelfItemViewBinder(
    private val itemWidth: Int,
    private val itemClick: (String?) -> Unit
) : ViewBindingDelegate<CommonItemBean, ExpandBookshelfViewItemBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ExpandBookshelfViewItemBinding>, item: CommonItemBean
    ) {
        holder.binding.apply {
            val layoutParams = clyContainer.layoutParams
            layoutParams.width = itemWidth
            clyContainer.layoutParams = layoutParams

            if (!item.isPlaceholder) {
                civBookshelfPic.visible()
                ivPlay.visible()
                vBookshelfNameBg.visible()
                sivBookshelfName.visible()
                ivPlaceholder.gone()
                item.cover?.let {
                    civBookshelfPic.load(
                        it,
                        placeholder = R.drawable.svg_w106_h106_placeholder,
                        error = R.drawable.svg_w106_h106_error
                    )
                }
                sivBookshelfName.text = item.title
                ivPlay.setResType(item.contentResourceType)
            } else {
                civBookshelfPic.gone()
                ivPlay.gone()
                vBookshelfNameBg.gone()
                sivBookshelfName.gone()
                ivPlaceholder.visible()
            }

            clyContainer.addRadius(R.dimen.base_sw_dp_12)

        }
        holder.itemView.singleClick {
            item.route?.let { route ->
                HRouter.navigation(route)
                itemClick.invoke(item.contentId)
            }
        }
    }
}