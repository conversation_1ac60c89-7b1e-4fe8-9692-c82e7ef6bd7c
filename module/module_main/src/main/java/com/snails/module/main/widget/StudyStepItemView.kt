package com.snails.module.main.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.ColorUtils
import com.snails.base.network.repository.info.course.StepInfo
import com.snails.module.main.R

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月27日 15:03:46
 */
class StudyStepItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val ivStudyState: AppCompatImageView
    private val vLessonState: View
    private val tvLessonName: AppCompatTextView
    private val studyStepViewScore: StudyStepViewScore

    init {
        // 使用 LayoutInflater 加载布局
        val layoutId = R.layout.study_step_view_item
        val view = LayoutInflater.from(context).inflate(layoutId, this, true)
        ivStudyState = view.findViewById(R.id.ivStudyState)
        vLessonState = view.findViewById(R.id.vLessonState)
        tvLessonName = view.findViewById(R.id.tvLessonName)
        studyStepViewScore = view.findViewById(R.id.studyStepViewScore)
    }

    fun setLessonInfo(info: StepInfo) {
        info.stepStatus?.let { setStudyState(it) }
        tvLessonName.text = info.stepShowName
        studyStepViewScore.setScore(info.stepScore ?: 0) //课节分数，默认0
    }

    private fun setStudyState(lessonStatus: String) {
        when (lessonStatus) {
            "FINISHED",
            "UNLOCK" -> { //完成
                ivStudyState.setBackgroundResource(R.drawable.svg_lesson_complete)
                vLessonState.setBackgroundResource(R.drawable.shape_study_lesson_complete)
                tvLessonName.setTextColor(ColorUtils.getColor(R.color.text_headline))
            }

            "ACTIVE" -> { //进行中
                ivStudyState.setBackgroundResource(R.drawable.svg_lesson_in_progress)
                vLessonState.setBackgroundResource(R.drawable.shape_study_lesson_in_progress)
                tvLessonName.setTextColor(ColorUtils.getColor(R.color.text_on_primary_button))
            }

            "LOCKED" -> { //加锁
                ivStudyState.setBackgroundResource(R.drawable.svg_lesson_locked)
                vLessonState.setBackgroundResource(R.drawable.shape_study_lesson_locked)
                tvLessonName.setTextColor(ColorUtils.getColor(R.color.text_headline))
            }
        }
    }

}