package com.snails.module.main.adapter

import android.content.Context
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.image_loader.load
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.addRadius
import com.snails.base.utils.ext.commonMargin16Or24
import com.snails.base.utils.ext.singleClick
import com.snails.module.main.R
import com.snails.module.main.bean.CommonItemBean
import com.youth.banner.adapter.BannerAdapter

class CommonBannerAdapter(
    val context: Context,
    val list: List<CommonItemBean>,
) : BannerAdapter<CommonItemBean, CommonBannerAdapter.BannerViewHolder>(list) {

    private val dimension = context.commonMargin16Or24()

    override fun onCreateHolder(parent: ViewGroup, viewType: Int): BannerViewHolder {

        val imageView = ImageView(parent.context)
        //注意，必须设置为match_parent，这个是viewpager2强制要求的
        val layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )

        layoutParams.setMargins(dimension, 0, dimension, 0)
        imageView.layoutParams = layoutParams
        imageView.adjustViewBounds = true
        imageView.scaleType = ImageView.ScaleType.CENTER_CROP
        return BannerViewHolder(imageView)
    }

    override fun onBindView(
        holder: BannerViewHolder, data: CommonItemBean, position: Int, size: Int
    ) {
        data.cover?.let {
            holder.imageView.load(
                it,
                placeholder = R.drawable.svg_w343_h137_placeholder,
                error = R.drawable.svg_w343_h137_error
            )
        }
        holder.imageView.addRadius(R.dimen.base_sw_dp_16)
        holder.imageView.singleClick {
            data.route?.let { it1 ->
                HRouter.navigation(it1)
            }
        }
    }

    class BannerViewHolder(var imageView: ImageView) : RecyclerView.ViewHolder(imageView)
}