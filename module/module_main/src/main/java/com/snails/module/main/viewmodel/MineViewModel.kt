package com.snails.module.main.viewmodel

import androidx.lifecycle.MutableLiveData
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.info.login.UserInfo
import com.snails.module.base.BaseViewModel
import com.snails.module.base.bean.StateType
import com.snails.module.base.utils.SingleLiveEventLiveData

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 14:43:45
 */
class MineViewModel : BaseViewModel() {

    //获取个人信息
    val userInfoInfoLiveData: MutableLiveData<UserInfo> = SingleLiveEventLiveData()

    /**
     * 获取个人信息
     */
    fun getUserProfile(stateType: StateType = StateType.NONE) {
        request(
            stateType = stateType,
            request = {
                SnailRepository.me.getUserProfile()
            },
            success = { data ->
                data?.let {
                    userInfoInfoLiveData.value = it
                }
            }
        )
    }
}