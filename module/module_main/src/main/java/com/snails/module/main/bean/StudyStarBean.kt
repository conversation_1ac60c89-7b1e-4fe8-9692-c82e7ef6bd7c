package com.snails.module.main.bean

import com.snails.base.network.repository.info.ISame
import com.snails.module.main.bean.base.PlateInfo

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 13:24:16
 */
data class StudyStarBean(
    val items: List<CommonItemBean>? = null
) : PlateInfo() {
    override fun isSame(data: ISame): Boolean {
        if (data !is StudyStarBean) {
            return false
        }
        if (items?.size != data.items?.size) {
            return false
        }
        items?.forEachIndexed { index, info ->
            if (data.items?.get(index)?.isSame(info) == false) {
                return false
            }
        }
        return true
    }
}
