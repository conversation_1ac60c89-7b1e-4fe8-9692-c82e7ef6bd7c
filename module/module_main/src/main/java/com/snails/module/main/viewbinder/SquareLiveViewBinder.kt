package com.snails.module.main.viewbinder

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.ToastUtils
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.builder.GSYVideoOptionBuilder
import com.shuyu.gsyvideoplayer.listener.GSYSampleCallBack
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer
import com.shuyu.gsyvideoplayer.video.base.GSYVideoView.CURRENT_STATE_PAUSE
import com.shuyu.gsyvideoplayer.video.base.GSYVideoView.CURRENT_STATE_PLAYING
import com.snails.base.image_loader.load
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.addRadius
import com.snails.base.utils.ext.getRealPath
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.invisible
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.main.R
import com.snails.module.main.bean.LiveBean
import com.snails.module.main.databinding.SquareLiveBinding
import androidx.core.net.toUri

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 13:29:47
 */
class SquareLiveViewBinder(private val gsyVideoOptionBuilder: GSYVideoOptionBuilder) :
    ViewBindingDelegate<LiveBean, SquareLiveBinding>() {

    @SuppressLint("IntentWithNullActionLaunch")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<SquareLiveBinding>,
        item: LiveBean
    ) {
        holder.binding.apply {
            tvStudyCourse.text = item.title
            item.liveData?.liveCover?.let { ivLive.load(it) }
            tvWatchLiveNum.text = item.liveData?.liveUserCount ?: ""
            tvLiveName.text = item.liveData?.liveTitle ?: ""
            tvLiveDesc.text = item.liveData?.liveDescription ?: ""
            item.liveData?.goodsCover?.let {
                ivCoursePic.load(
                    it,
                    placeholder = R.drawable.svg_w88_h88_placeholder,
                    error = R.drawable.svg_w88_h88_error
                )
            }
            tvCourseName.text = item.liveData?.goodsTitle ?: ""
            tvCourseDesc.text = item.liveData?.goodsDescription ?: ""
            tvCoursePrice.text = item.liveData?.goodsPrice ?: ""

            ivLive.singleClick {
                item.liveData?.liveRoute?.let { it1 -> HRouter.navigation(it1) }
            }
            vCourseBg.singleClick {
                item.liveData?.goodsRoute?.let { it1 ->
                    if (it1.contains("snssdk1128://")) { //是否是抖音链接
                        if (AppUtils.isAppInstalled("com.ss.android.ugc.aweme")) {
                            val intent = Intent().apply {
                                setData(it1.toUri())
                            }
                            ActivityUtils.startActivity(intent)
                        } else {
                            ToastUtils.showShort("请安装“抖音”后，再进行尝试")
                        }
                    } else {
                        HRouter.navigation(it1)
                    }
                }
            }
            gsyVideoOptionBuilder.apply {
                setUrl(item.liveData?.liveVideo?.getRealPath())
                setPlayPosition(holder.layoutPosition)
                setCacheWithPlay(true)
                setVideoAllCallBack(
                    object : GSYSampleCallBack() {
                        override fun onPrepared(url: String?, vararg objects: Any?) {
                            super.onPrepared(url, *objects)
                            //静音
                            GSYVideoManager.instance().isNeedMute = true
                        }
                    }
                )
                setGSYStateUiListener { state ->
                    if (state == CURRENT_STATE_PLAYING) {
                        ivLive.invisible()
                        gsyVideoPlayer.visible()
                    } else if (state == CURRENT_STATE_PAUSE) {
                        ivLive.visible()
                        gsyVideoPlayer.gone()
                    }
                }
            }.build(gsyVideoPlayer as StandardGSYVideoPlayer)
            gsyVideoPlayer.addRadius(R.dimen.base_sw_dp_8)
            vLive.singleClick {
                gsyVideoPlayer.onVideoPause()
                item.liveData?.liveRoute?.let { it1 -> HRouter.navigation(it1) }
            }
        }
    }
}