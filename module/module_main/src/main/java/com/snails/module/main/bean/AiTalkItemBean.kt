package com.snails.module.main.bean

import com.google.gson.annotations.SerializedName
import com.snails.base.network.repository.info.ISame

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月28日 13:23:10
 */
data class AiTalkItemBean(
    @SerializedName("contentId")
    val contentId: String? = null,
    @SerializedName("cover")
    val cover: String? = null,
    @SerializedName("index")
    val index: Int? = null,
    @SerializedName("jumpType")
    val jumpType: String? = null,
    @SerializedName("contentResourceType")
    val resourceType: String? = null,
    @SerializedName("route")
    val route: String? = null,
    @SerializedName("speakerAvatar")
    val speakerAvatar: String? = null,
    @SerializedName("speakerName")
    val speakerName: String? = null,
    @SerializedName("topicId")
    val topicId: String? = null,
    @SerializedName("topicName")
    val topicName: String? = null,
    @SerializedName("ringtone")
    val ringtone: String? = null
) : ISame() {
    override fun isSame(data: ISame): Boolean {
        if (data !is AiTalkItemBean) {
            return false
        }
        if (index != data.index) {
            return false
        }
        if (jumpType != data.jumpType) {
            return false
        }
        if (cover != data.cover) {
            return false
        }
        if (route != data.route) {
            return false
        }
        if (resourceType != data.resourceType) {
            return false
        }
        if (contentId != data.contentId) {
            return false
        }
        if (speakerAvatar != data.speakerAvatar) {
            return false
        }
        if (speakerName != data.speakerName) {
            return false
        }
        if (topicId != data.topicId) {
            return false
        }
        if (topicName != data.topicName) {
            return false
        }
        if (ringtone != data.ringtone) {
            return false
        }
        return true
    }
}