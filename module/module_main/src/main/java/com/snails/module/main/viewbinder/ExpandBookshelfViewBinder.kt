package com.snails.module.main.viewbinder

import android.content.Context
import androidx.recyclerview.widget.GridLayoutManager
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.addRadius
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.isPortraitPadMode
import com.snails.base.utils.ext.showItemSize
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.main.R
import com.snails.module.main.bean.BookshelfBean
import com.snails.module.main.bean.CommonItemBean
import com.snails.module.main.databinding.ExpandBookshelfViewBinding
import com.snails.module.main.widget.BookshelfItemListView

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:49:09
 */
class ExpandBookshelfViewBinder(
    private val context: Context,
    private val itemClick: (String?) -> Unit
) :
    ViewBindingDelegate<BookshelfBean, ExpandBookshelfViewBinding>() {

    private val itemClickListener = object : BookshelfItemListView.ItemClickListener {
        override fun itemClick(contentId: String?) {
            itemClick.invoke(contentId)
        }
    }

    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ExpandBookshelfViewBinding>,
        item: BookshelfBean
    ) {
        holder.binding.apply {
            tvTitle.text = item.title
            checkBookshelfData(item.items).let {
                bookshelfItemListView.apply {
                    setHasFixedSize(true)
                    setData(it)
                }
            }
            bookshelfItemListView.setLayoutManager(
                GridLayoutManager(
                    context,
                    context.showItemSize()
                )
            )
            if (item.showMore == true) {
                tvLookMore.visible()
            } else {
                tvLookMore.gone()
            }
            bookshelfItemListView.itemClickListener = itemClickListener
            tvLookMore.singleClick {
                item.showMoreRoute?.let { it1 -> HRouter.navigation(it1) }
            }
            clyContainer.addRadius(R.dimen.base_sw_dp_16)
        }
    }

    /**
     * 检查书架数据，如果数据小于 showItemSize 则增添加占位数据
     */
    private fun checkBookshelfData(items: List<CommonItemBean>?): List<CommonItemBean> {
        val list = mutableListOf<CommonItemBean>()
        if (items.isNullOrEmpty()) {
            list.add(CommonItemBean(isPlaceholder = true))
            list.add(CommonItemBean(isPlaceholder = true))
            list.add(CommonItemBean(isPlaceholder = true))
            if (context.isPortraitPadMode()) {
                list.add(CommonItemBean(isPlaceholder = true))
                list.add(CommonItemBean(isPlaceholder = true))
            }
            return list
        }

        val showSize = context.showItemSize()
        val itemsSize = items.size
        if (itemsSize >= showSize) {
            //如果服务器返回的数量大于了显示的数量，则截取
            list.addAll(items.subList(0, showSize))
        } else {
            list.addAll(items)
            //如果服务器返回的数量小于了显示的数量，将服务器返回的加进去之后，再添加占位符
            val size = showSize - items.size
            for (i in 0 until size) {
                list.add(CommonItemBean(isPlaceholder = true))
            }
        }
        return list
    }
}