package com.snails.module.main.bean

import com.snails.base.network.repository.info.ISame

data class CommonItemBean(
    val title: String? = null,
    val index: Int? = null,
    val jumpType: String? = null, //URL
    val cover: String? = null,
    val contentResourceType: String? = null, //PIC, AUDIO, VIDEO, PIC_BOOK, GAME
    val route: String? = null,
    val isVideo: Boolean? = null,
    val subTitle: String? = null,
    val contentId: String? = null,
    val isPlaceholder: Boolean = false //书架位置的空白占位符
) : ISame() {
    override fun isSame(data: ISame): Bo<PERSON>an {
        if (data !is CommonItemBean) {
            return false
        }
        if (title != data.title) {
            return false
        }
        if (index != data.index) {
            return false
        }
        if (jumpType != data.jumpType) {
            return false
        }
        if (cover != data.cover) {
            return false
        }
        if (route != data.route) {
            return false
        }
        if (isVideo != data.isVideo) {
            return false
        }
        if (contentResourceType != data.contentResourceType) {
            return false
        }
        if (subTitle != data.subTitle) {
            return false
        }
        if (contentId != data.contentId) {
            return false
        }
        if (isPlaceholder != data.isPlaceholder) {
            return false
        }
        return true
    }
}