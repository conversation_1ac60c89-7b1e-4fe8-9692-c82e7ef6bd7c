package com.snails.module.main.viewmodel

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.snails.base.network.error.ApiException
import com.snails.base.network.error.ERROR
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.info.ISame
import com.snails.base.network.repository.info.course.CourseDetailsInfo
import com.snails.base.network.repository.info.course.StudyProgressInfo
import com.snails.base.network.repository.storage.UserStorage
import com.snails.module.base.BaseViewModel
import com.snails.module.base.bean.StateType
import com.snails.module.base.utils.SingleLiveEventLiveData
import com.snails.module.main.bean.MarkingInfoBean

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 14:43:45
 */
class StudyViewModel : BaseViewModel() {

    // 学习课程列表
    val studyListDataLiveData: MutableLiveData<List<ISame>> = SingleLiveEventLiveData()

    // 当前用户学习进度
    val studyProgressInfo: MutableLiveData<CourseDetailsInfo> = SingleLiveEventLiveData()

    // 显示无课程页面
    val showNoDataLiveData: MutableLiveData<Boolean> = SingleLiveEventLiveData()

    //课程商品路由
    var courseGoodsRoute: String? = null

    /**
     * 课程目录
     */
    fun getCourseCatalog(stateType: StateType = StateType.NONE) {
        val serialExecutor: suspend () -> Pair<StudyProgressInfo?, CourseDetailsInfo?> = {
            val courseId = UserStorage.me.getStudyCourseId()
            val studyProgressInfo = SnailRepository.me.queryUserStudyProgress(courseId)
            if (studyProgressInfo.isFailed()) {
                throw ApiException(studyProgressInfo.code, studyProgressInfo.message)
            }
            if (studyProgressInfo.data?.showCourse != true) {
                Pair(studyProgressInfo.data, null)
            } else {
                val id =
                    if (studyProgressInfo.data?.invalidCourseId == true || (courseId == null)) {
                        studyProgressInfo.data?.currentCourseId
                    } else {
                        courseId
                    }

                id ?: throw ApiException(ERROR.DATA_ERROR)
                val courseDetailsInfo = SnailRepository.me.queryCourseDetails(id)
                if (courseDetailsInfo.isFailed()) {
                    throw ApiException(courseDetailsInfo.code, courseDetailsInfo.message)
                }
                Pair(studyProgressInfo.data, courseDetailsInfo.data)
            }
        }

        multipleRequests(
            stateType = stateType,
            requestExecutor = serialExecutor,
            success = { pair ->
                if (pair.first?.showCourse == false) {
                    showNoDataLiveData.value = true
                    courseGoodsRoute = pair.first?.courseGoodsRoute
                } else {
                    showNoDataLiveData.value = false
                    pair.second?.let {
                        studyProgressInfo.value = it
                        it.courseId?.let { it1 ->
                            val condition =
                                (UserStorage.me.getStudyCourseId() == pair.first?.currentCourseId)
                            if (condition) {
                                UserStorage.me.setStudyCourseId("")
                            }
                        }
                        studyListDataLiveData.value = convertStudyData(it, pair.first?.msgCount)
                    }
                }
            }
        )
    }

    /**
     * 转换课程、课节数据结果
     */
    private fun convertStudyData(
        courseDetailsInfo: CourseDetailsInfo,
        msgCount: Int?
    ): List<ISame> {
        val list = mutableListOf<ISame>()
        list.add(
            MarkingInfoBean(
                msgCount = msgCount,
                courseStage = courseDetailsInfo.courseStage,
                courseType = courseDetailsInfo.courseType
            )
        )
        list.add(courseDetailsInfo)
        return list
    }
}