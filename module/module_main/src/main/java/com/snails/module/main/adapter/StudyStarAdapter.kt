package com.snails.module.main.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.imageview.ShapeableImageView
import com.snails.base.image_loader.load
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.widget.ResTypeView
import com.snails.module.main.R
import com.snails.module.main.bean.StudyStarColumnBean
import com.snails.module.main.bean.CommonItemBean

class StudyStarAdapter(private val context: Context) :
    RecyclerView.Adapter<StudyStarAdapter.VHolder>() {

    private val list = arrayListOf<StudyStarColumnBean>()
    private var canScroll: Boolean = false

    private val heightOne = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_102)
    private val heightTwo = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_136)
    private val heightThree = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_119)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VHolder {
        val inflater = LayoutInflater.from(parent.context)
        val layoutId = R.layout.square_study_star_item
        return VHolder(inflater.inflate(layoutId, parent, false))
    }

    override fun onBindViewHolder(holder: VHolder, position: Int) {
        val realPosition: Int = position % list.size
        val item = list[realPosition]
        val size = item.list.size
        if (size >= 2) {
            when (realPosition % 3) {
                0 -> {
                    dynamicSetContainerWidth(0, holder.clyContainer)
                    dynamicSetItemHeight(
                        holder.vOneGb,
                        holder.vTwoBg,
                        heightOne,
                        heightTwo
                    )
                }

                1 -> {
                    dynamicSetContainerWidth(1, holder.clyContainer)
                    dynamicSetItemHeight(
                        holder.vOneGb,
                        holder.vTwoBg,
                        heightThree,
                        heightThree
                    )
                }

                2 -> {
                    dynamicSetContainerWidth(2, holder.clyContainer)
                    dynamicSetItemHeight(
                        holder.vOneGb,
                        holder.vTwoBg,
                        heightTwo,
                        heightOne
                    )
                }
            }
            val dataOne = item.list[0]
            val dataTwo = item.list[1]
            setStudyStarInfo(dataOne, holder.sivOne, holder.ivPlayOne)
            setStudyStarInfo(dataTwo, holder.sivTwo, holder.ivPlayTwo)
            holder.sivOne?.singleClick {
                jump(dataOne)
            }
            holder.sivOne?.singleClick {
                jump(dataTwo)
            }
        }
    }

    private fun jump(data: CommonItemBean) {
        data.route?.let { HRouter.navigation(it) }
    }

    private fun setStudyStarInfo(
        data: CommonItemBean,
        siv: ShapeableImageView?,
        ivPlay: ResTypeView?,
    ) {
        data.cover?.let { siv?.load(it) }
        ivPlay?.setResType(data.contentResourceType)
    }

    /**
     * 动态设置容器的宽度
     */
    private fun dynamicSetContainerWidth(index: Int, clyContainer: ConstraintLayout?) {
        val width = when (index) {
            0 -> context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_102)
            1 -> context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_119)
            else -> context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_136)
        }
        clyContainer?.layoutParams?.apply {
            this.width = width
            clyContainer.layoutParams = this
        }
    }

    /**
     * 动态设置 Item 的高度
     */
    private fun dynamicSetItemHeight(
        sivOne: View?,
        sivTwo: View?,
        heightOne: Int,
        heightTwo: Int
    ) {

        sivOne?.layoutParams?.apply {
            this.height = heightOne
            sivOne.layoutParams = this
        }

        sivTwo?.layoutParams?.apply {
            this.height = heightTwo
            sivTwo.layoutParams = this
        }
    }

    fun setContents(
        data: List<StudyStarColumnBean>,
        scroll: Boolean
    ) {
        this.canScroll = scroll
        list.clear()
        list.addAll(data)
    }

    override fun getItemCount() = if (canScroll) {
        Int.MAX_VALUE
    } else {
        list.size
    }

    class VHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val clyContainer: ConstraintLayout? = itemView.findViewById(R.id.clyContainer)

        val vOneGb: View? = itemView.findViewById(R.id.vOneBg)
        val sivOne: ShapeableImageView? = itemView.findViewById(R.id.sivOne)
        val ivPlayOne: ResTypeView? = itemView.findViewById(R.id.ivPlayOne)

        val vTwoBg: View? = itemView.findViewById(R.id.vTwoBg)
        val sivTwo: ShapeableImageView? = itemView.findViewById(R.id.sivTwo)
        val ivPlayTwo: ResTypeView? = itemView.findViewById(R.id.ivPlayTwo)
    }
}