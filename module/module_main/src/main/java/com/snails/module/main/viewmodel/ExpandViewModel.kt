package com.snails.module.main.viewmodel

import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.GsonUtils
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.info.ISame
import com.snails.base.network.repository.info.square.Region
import com.snails.module.base.BaseViewModel
import com.snails.module.base.bean.StateType
import com.snails.module.base.utils.SingleLiveEventLiveData
import com.snails.module.main.bean.AiTalkBean
import com.snails.module.main.bean.BookshelfBean
import com.snails.module.main.bean.StoryBean

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 14:43:45
 */
class ExpandViewModel : BaseViewModel() {

    // 拓展列表数据
    val expandListDataLiveData: MutableLiveData<List<ISame>> = SingleLiveEventLiveData()

    fun getExpandList(stateType: StateType = StateType.NONE) {
        request(
            stateType = stateType,
            request = {
                SnailRepository.me.extendList(AppUtils.getAppVersionName())
            },
            success = { data ->
                data?.regions?.let { list ->
                    expandListDataLiveData.value = convertSquareData(list)
                }
            }
        )
    }

    /**
     * 更新拓展页点击记录：progress为空，targetId传 contentId，scene 传EXTEND_PAGE；
     * 更新播放记录：progress传列表资源序号 itemIndex，targetId传专辑 id，scene 传ALBUM_PAGE
     */
    fun historyRecord(
        progress: Int? = null,
        targetId: String? = null,
        scene: String,
    ) {
        val tId = targetId ?: return
        request(
            request = {
                SnailRepository.me.historyRecord(progress, tId, scene)
            }
        )
    }

    private fun convertSquareData(data: List<Region>): List<ISame> {
        val list = mutableListOf<ISame>()
        data.forEach { bean ->
            when (bean.type) {
                "EXTEND_TOP_BANNER" -> {
                    bean.content?.let {
                        kotlin.runCatching {
                            val aiTalkBean = GsonUtils.fromJson(it, AiTalkBean::class.java)
                            list.add(aiTalkBean)
                        }
                    }
                }

                "EXTEND_BOOK_MARKET" -> {
                    bean.content?.let {
                        kotlin.runCatching {
                            list.add(GsonUtils.fromJson(it, BookshelfBean::class.java))
                        }
                    }
                }

                "EXTEND_STORY_MARKET" -> {
                    bean.content?.let {
                        kotlin.runCatching {
                            list.add(GsonUtils.fromJson(it, StoryBean::class.java))
                        }
                    }
                }
            }
        }
        return list
    }
}