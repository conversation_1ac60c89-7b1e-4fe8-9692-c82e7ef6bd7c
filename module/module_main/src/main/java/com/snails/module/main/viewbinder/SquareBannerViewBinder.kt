package com.snails.module.main.viewbinder

import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.main.adapter.CommonBannerAdapter
import com.snails.module.main.bean.BannerBean
import com.snails.module.main.databinding.SquareBannerBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 13:29:47
 */
class SquareBannerViewBinder : ViewBindingDelegate<BannerBean, SquareBannerBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<SquareBannerBinding>,
        item: BannerBean
    ) {
        item.items?.let {
            holder.binding.banner.setAdapter(
                CommonBannerAdapter(
                    holder.itemView.context,
                    it,
                )
            )
        }
    }
}