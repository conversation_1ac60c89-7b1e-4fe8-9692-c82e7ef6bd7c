package com.snails.module.main.widget.tab

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.appcompat.widget.LinearLayoutCompat
import com.snails.base.utils.ext.singleClick
import com.snails.module.main.R

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月20日 11:21:44
 */
class BottomNavigationView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayoutCompat(context, attrs, defStyleAttr) {

    private val navItems: List<BottomNavigationItemView>
    private var selectedItemIndex = -1
    private var onItemSelectedListener: ((index: Int) -> Unit)? = null

    init {
        val layoutId = R.layout.tab_bottom_navigation_view
        orientation = HORIZONTAL
        val view = LayoutInflater.from(context).inflate(layoutId, this, true)

        // 初始化导航项
        navItems = listOf(
            view.findViewById(R.id.navSquare),
            view.findViewById(R.id.navStudy),
            view.findViewById(R.id.navExpand),
            view.findViewById(R.id.navMine)
        )

        // 设置导航项点击事件
        navItems.forEachIndexed { index, itemView ->
            itemView.singleClick {
                selectItem(index)
            }
        }
    }

    // 选择某个导航项
    private fun selectItem(index: Int) {
        if (selectedItemIndex == index) return // 如果已经是选中的，不做任何处理

        // 重置之前的选中项
        if (selectedItemIndex >= 0) {
            navItems[selectedItemIndex].setItemSelected(false)
        }

        // 设置当前选中项
        navItems[index].setItemSelected(true)
        selectedItemIndex = index

        onItemSelectedListener?.invoke(index)
    }

    // 设置选中某个导航项
    fun setSelectedItem(index: Int) {
        if (index in navItems.indices) {
            selectItem(index)
        }
    }

    // 设置点击事件回调
    fun setOnItemSelectedListener(listener: (index: Int) -> Unit) {
        this.onItemSelectedListener = listener
    }
}