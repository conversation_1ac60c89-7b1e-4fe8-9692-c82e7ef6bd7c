package com.snails.module.main.bean

import com.snails.base.network.repository.info.ISame

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 14:52:26
 */
data class ActivationCourseBean(val items: List<CommonItemBean>? = null) : ISame() {
    override fun isSame(data: ISame): Boolean {
        if (data !is ActivationCourseBean) {
            return false
        }
        if (items?.size != data.items?.size) {
            return false
        }
        items?.forEachIndexed { index, info ->
            if (data.items?.get(index)?.isSame(info) == false) {
                return false
            }
        }
        return true
    }
}