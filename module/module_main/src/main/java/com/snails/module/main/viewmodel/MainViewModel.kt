package com.snails.module.main.viewmodel

import android.os.Build
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.FileUtils
import com.snails.base.crash.CrashKitManager
import com.snails.base.network.manager.NetworkManager
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.storage.AppSettingStorage
import com.snails.module.base.BaseViewModel
import com.snails.module.base.bean.StateType
import com.snails.module.main.bean.FragmentVisibilityState
import java.util.Collections


/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 14:43:45
 */
class MainViewModel : BaseViewModel() {
    /**
     * Fragment 可见性LiveData
     */
    val fragmentVisibilityLiveData: MutableLiveData<FragmentVisibilityState> =
        MutableLiveData()

    fun uploadCrashLog() {
        localRequest(
            request = {
                val fileList = CrashKitManager.crashFiles().toList()
                if (fileList.isNotEmpty()) {
                    //如果是要自己拿到这些文件，建议根据时间来排个序
                    //排序
                    Collections.sort(
                        fileList,
                        Comparator { file01, file02 ->
                            try {
                                //根据修改时间排序
                                val lastModified01 = file01.lastModified()
                                val lastModified02 = file02.lastModified()
                                if (lastModified01 > lastModified02) {
                                    return@Comparator -1
                                } else {
                                    return@Comparator 1
                                }
                            } catch (e: Exception) {
                                return@Comparator 1
                            }
                        })
                    //第一步：获取签名
                    val suffix =
                        FileUtils.getFileExtension(fileList.firstOrNull())?.lowercase() ?: "png"
                    val ossAccessSignInfo =
                        SnailRepository.me.ossAccessSign(suffix, "APP_LOG", fileList.size, null)
                    if (ossAccessSignInfo.isFailed()) {
                        throw Throwable("upload crash log failed")
                    }
                    //第二步：设置OSS地址
                    NetworkManager.uploadOssHost = ossAccessSignInfo.data?.host

                    //第三步：上传OSS
                    val key = ossAccessSignInfo.data?.files
                    val policy = ossAccessSignInfo.data?.encodedPolicy
                    val ossAccessKeyId = ossAccessSignInfo.data?.accessKeyId
                    val signature = ossAccessSignInfo.data?.postSignature
                    val filePathList = mutableListOf<String>()
                    fileList.forEach {
                        filePathList.add(it.path)
                    }
                    SnailRepository.me.uploadOssList(
                        filePathList,
                        key,
                        policy,
                        ossAccessKeyId,
                        signature,
                        null
                    )
                }
            },
            success = {
                CrashKitManager.deleteFiles()
            }
        )
    }

    fun getAppConfig() {
        request(
            stateType = StateType.NONE,
            request = {
                SnailRepository.me.getAppConfig(
                    AppUtils.getAppVersionName(),
                    "${AppUtils.getAppVersionCode()}",
                    "ANDROID",
                    Build.BRAND,
                    Build.VERSION.RELEASE,
                    Build.MODEL,
                    Build.VERSION.SDK_INT.toString()
                )
            },
            success = {
                it?.videoRecordType?.let { type ->
                    AppSettingStorage.me.setServiceVideoRecordType(type)
                }
            }
        )
    }
}