package com.snails.module.main.viewbinder

import coil3.load
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.main.bean.CourseDescBean
import com.snails.module.main.databinding.SquareCourseDescBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 13:29:47
 */
class SquareCourseDescViewBinder : ViewBindingDelegate<CourseDescBean, SquareCourseDescBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<SquareCourseDescBinding>, item: CourseDescBean
    ) {
        holder.binding.apply {
            tvTitle.text = item.title
            item.items?.get(0)?.cover?.let {
                sivCourseDescPic.load(it)
            }
            sivCourseDescPic.singleClick {
                item.items?.get(0)?.route?.let { it1 -> HRouter.navigation(it1) }
            }
            if (item.showMore == true) {
                tvLookMore.visible()
            } else {
                tvLookMore.gone()
            }
            tvLookMore.singleClick {
                item.showMoreRoute?.let { it1 -> HRouter.navigation(it1) }
            }
        }
    }
}