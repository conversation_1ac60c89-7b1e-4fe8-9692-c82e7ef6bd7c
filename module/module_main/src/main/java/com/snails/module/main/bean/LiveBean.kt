package com.snails.module.main.bean

import com.snails.base.network.repository.info.ISame
import com.snails.module.main.bean.base.PlateInfo

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月21日 13:16:07
 */
data class LiveBean(
    val liveData: LiveData? = null,
) : PlateInfo() {
    override fun isSame(data: ISame): Boolean {
        if (data !is LiveBean) {
            return false
        }
        if (title != data.title) {
            return false
        }
        return data.liveData?.let { liveData?.isSame(it) } == true
    }

}

data class LiveData(
    val goodsCover: String? = null,
    val goodsDescription: String? = null,
    val goodsRoute: String? = null,
    val goodsTitle: String? = null,
    val goodsPrice: String? = null,
    val liveCover: String? = null,
    val liveDescription: String? = null,
    val liveRoute: String? = null,
    val liveTitle: String? = null,
    val liveUserCount: String? = null,
    val liveVideo: String? = null,
) : ISame() {
    override fun isSame(data: ISame): Boolean {
        if (data !is LiveData) {
            return false
        }
        if (goodsCover != data.goodsCover) {
            return false
        }
        if (goodsDescription != data.goodsDescription) {
            return false
        }
        if (goodsRoute != data.goodsRoute) {
            return false
        }
        if (goodsTitle != data.goodsTitle) {
            return false
        }
        if (goodsPrice != data.goodsPrice) {
            return false
        }
        if (liveCover != data.liveCover) {
            return false
        }
        if (liveDescription != data.liveDescription) {
            return false
        }
        if (liveRoute != data.liveRoute) {
            return false
        }
        if (liveTitle != data.liveTitle) {
            return false
        }
        if (liveUserCount != data.liveUserCount) {
            return false
        }
        if (liveVideo != data.liveVideo) {
            return false
        }
        return true
    }

}