package com.snails.module.main.bean

import com.snails.base.network.repository.info.ISame
import com.snails.module.main.bean.base.PlateInfo

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月26日 15:09:31
 */

data class StoryBean(
    val items: List<CommonItemBean>? = null
) : PlateInfo() {
    override fun isSame(data: ISame): <PERSON><PERSON>an {
        if (data !is StoryBean) {
            return false
        }
        if (items?.size != data.items?.size) {
            return false
        }
        items?.forEachIndexed { index, info ->
            if (data.items?.get(index)?.isSame(info) == false) {
                return false
            }
        }
        return true
    }
}