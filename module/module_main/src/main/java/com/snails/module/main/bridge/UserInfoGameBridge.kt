package com.snails.module.main.bridge

import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.constant.PermissionConstants
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.PermissionUtils
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.ToNumberPolicy
import com.google.gson.reflect.TypeToken
import com.snails.base.ffmpeg.FFmpegUtils
import com.snails.base.ffmpeg.callback.CommandCallback
import com.snails.base.game.GameBridge
import com.snails.base.game.GameEventListener
import com.snails.base.game.GameMethodManager
import com.snails.base.game.annotation.GameMethod
import com.snails.base.network.constant.ENVIRONMENT_CONFIG
import com.snails.base.network.constant.OSS_URL
import com.snails.base.network.environment.EnvironmentConfig
import com.snails.base.network.manager.NetworkManager
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.storage.EnvironmentStorage
import com.snails.base.network.repository.storage.UserStorage
import com.snails.base.network.repository.upload.ProgressListener
import com.snails.base.record.RecorderManager
import com.snails.base.record.bean.Formats
import com.snails.base.record.bean.RecordErrorInfo
import com.snails.base.record.config.RecorderConfig
import com.snails.base.record.interfaces.RecordingStateListener
import com.snails.base.utils.constants.AppConstants
import com.snails.base.utils.ext.getFileSize
import com.snails.module.main.BuildConfig
import com.study.base.snails.EvaluationListener
import com.study.base.snails.TaiEvaluation
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年09月25日 16:50:02
 */
class UserInfoGameBridge(
    activity: AppCompatActivity,
    private val gameEventListener: GameEventListener? = null
) : GameBridge {

    private val act = WeakReference(activity)

    /**
     * 权限检测
     */
    @GameMethod
    fun checkPermission(scene: String): String {
        val have = when (scene) {
            "MICROPHONE" -> {
                checkRecordPermission(PermissionConstants.MICROPHONE)
            }

            "CAMERA" -> {
                checkRecordPermission(PermissionConstants.CAMERA)
            }

            else -> {
                checkRecordPermission(PermissionConstants.STORAGE)
            }
        }
        val map = mutableMapOf<String, Any>()
        map["hadPermission"] = have
        return GsonUtils.toJson(map)
    }

    /**
     * 权限申请
     */
    @GameMethod
    fun requestPermission(scene: List<String>, openSetting: Boolean): String {
        if (openSetting) {
            AppUtils.launchAppDetailsSettings()
            val map = mutableMapOf<String, Any>()
            map["type"] = "SUCCESS"
            return getGson().toJson(map)
        }
        PermissionUtils.permission(*scene.toTypedArray()).request()
        val map = mutableMapOf<String, Any>()
        map["type"] = "SUCCESS"
        return getGson().toJson(map)
    }

    /**
     * 获取登录信息
     */
    @GameMethod
    fun getLoginInfo(): String? {
        return UserStorage.me.getLoginInfoJson()
    }

    @GameMethod
    fun getAppSettings(): String? {
        val env = if (BuildConfig.BUILD_TYPE != "release" && EnvironmentStorage.me.isGameConfig()) {
            ENVIRONMENT_CONFIG
        } else {
            EnvironmentStorage.me.getEnvironment()
        }
        val map = mutableMapOf<String, Any>()
        map["environment"] = env
        map["ossHost"] = OSS_URL
        map["apiHost"] = EnvironmentConfig.getBaseUrl()
        return GsonUtils.toJson(map)
    }

    /**
     * 退出游戏
     */
    @GameMethod
    fun exitGame() {
        gameEventListener?.gameExit()
    }

    private fun getGson(): Gson {
        val gsonBuilder = GsonBuilder()
        gsonBuilder.setObjectToNumberStrategy(ToNumberPolicy.LAZILY_PARSED_NUMBER)
        return gsonBuilder.create()
    }

    /**
     * 游戏录音
     */
    @GameMethod
    fun record(
        type: String,
        audioPath: String,
        callBackName: String,
        sampleRates: Number,
        channel: Number
    ): String {
        when (type) {
            "START" -> {
                if (checkRecordPermission(PermissionConstants.MICROPHONE)) {
                    try {
                        RecorderManager.getInstance().apply {
                            init(RecorderConfig().apply {
                                setFormat(Formats.MP3)
                                setFilePath(audioPath)
                            })
                            setRecordingStateListener(object : RecordingStateListener {
                                override fun recordStart() {
                                    val map = mutableMapOf<String, Any>()
                                    map["type"] = "START"
                                    GameMethodManager.callGameMethod(map, callBackName)
                                }

                                override fun recordStop() {
                                    val map = mutableMapOf<String, Any>()
                                    map["type"] = "STOP"
                                    GameMethodManager.callGameMethod(map, callBackName)
                                }

                                override fun recordCompleted(path: String?, duration: Int?) {
                                    val map = mutableMapOf<String, Any>()
                                    map["type"] = "RESULT"
                                    map["audioLength"] = duration ?: 0
                                    path?.let {
                                        map["audioPath"] = it
                                    }
                                    GameMethodManager.callGameMethod(map, callBackName)
                                }

                                override fun recordError(error: RecordErrorInfo) {
                                    val map = mutableMapOf<String, Any>()
                                    map["type"] = "ERROR"
                                    map["errorMsg"] = "${error.errorMsg}"
                                    GameMethodManager.callGameMethod(map, callBackName)
                                }
                            })
                            startRecord()
                        }
                    } catch (t: Throwable) {
                        val map = mutableMapOf<String, Any>()
                        map["type"] = "ERROR"
                        map["errorMsg"] = "start record failed,${t.message}"
                        GameMethodManager.callGameMethod(map, callBackName)
                    }
                } else {
                    reallyRequestPermission(PermissionConstants.MICROPHONE)
                    val map = mutableMapOf<String, Any>()
                    map["type"] = "ERROR"
                    map["errorMsg"] = "no record permission and request"
                    GameMethodManager.callGameMethod(map, callBackName)
                }
            }

            "STOP" -> {
                try {
                    RecorderManager.getInstance().stopRecord()
                } catch (t: Throwable) {
                    val map = mutableMapOf<String, Any>()
                    map["type"] = "ERROR"
                    map["errorMsg"] = "stop record failed.${t.message}"
                    GameMethodManager.callGameMethod(map, callBackName)
                }
            }
        }
        val map = mutableMapOf<String, Any>()
        map["type"] = "SUCCESS"
        return getGson().toJson(map)
    }

    /**
     * 游戏录音评测
     */
    @GameMethod
    fun recordEvaluation(
        type: String, //START、STOP、CANCEL、RELEASE
        config: String, //录音参数配置
        callBackName: String, //客户端回调录音结果给游戏的回调方法：
        volumeCallBackName: String //客户端回调录音音量给游戏的回调方法
    ): String {
        when (type) {
            "START" -> {
                if (checkRecordPermission(PermissionConstants.MICROPHONE)) {
                    TaiEvaluation.cancelOralEvaluation()
                    val mapType = object : TypeToken<Map<String, Any>>() {}.type
                    val map: Map<String, Any> = getGson().fromJson(config, mapType)
                    startRecordEvaluation(map, callBackName, volumeCallBackName)
                } else {
                    reallyRequestPermission(PermissionConstants.MICROPHONE)
                    val map = mutableMapOf<String, Any>()
                    map["type"] = "ERROR"
                    map["code"] = "40007"
                    map["errorMsg"] = "no record permission for evaluation and request"
                    GameMethodManager.callGameMethod(map, callBackName)
                }
            }

            "STOP" -> {
                TaiEvaluation.stopOralEvaluation()
            }

            "FORCE_STOP" -> {
                TaiEvaluation.forceStop()
            }

            "CANCEL" -> {
                TaiEvaluation.cancelOralEvaluation()
            }

            "RELEASE" -> {
                TaiEvaluation.release()
            }
        }
        val map = mutableMapOf<String, Any>()
        map["resultData"] = "SUCCESS"
        return getGson().toJson(map)
    }

    @OptIn(DelicateCoroutinesApi::class)
    @GameMethod
    fun uploadOSS(
        scene: String, //USER_COURSEWORK、
        callBackName: String, //回调方法名称
        uploadList: List<String>, //上传文件地址
        callBackProgressName: String? = null //进度回调方法名称
    ): String {
        GlobalScope.launch {
            try {
                delay(100) //加一个延时，防止文件还在写入
                val suffix =
                    FileUtils.getFileExtension(uploadList.firstOrNull())?.lowercase() ?: "png"
                val ossAccessSignInfo =
                    SnailRepository.me.ossAccessSign(suffix, scene, uploadList.size, null)
                if (ossAccessSignInfo.isFailed()) {
                    return@launch
                }
                //第二步：设置OSS地址
                NetworkManager.uploadOssHost = ossAccessSignInfo.data?.host

                //第三步：上传OSS
                val key = ossAccessSignInfo.data?.files
                val policy = ossAccessSignInfo.data?.encodedPolicy
                val ossAccessKeyId = ossAccessSignInfo.data?.accessKeyId
                val signature = ossAccessSignInfo.data?.postSignature
                val data = SnailRepository.me.uploadOssList(
                    uploadList, key, policy, ossAccessKeyId, signature,
                    object : ProgressListener {
                        override fun onProgress(
                            percent: Int,
                            uploadLength: Long,
                            contentLength: Long
                        ) {
                            runCatching {
                                callBackProgressName?.let {
                                    val map = mutableMapOf<String, Any>()
                                    map["type"] = "PROGRESS"
                                    map["progress"] = percent
                                    map["currentSize"] = uploadLength
                                    map["totalSize"] = contentLength
                                    GameMethodManager.callGameMethod(map, it)
                                }
                            }
                        }
                    }
                )
                if (data.any { it.uploadSuccess == false }) {
                    val map = mutableMapOf<String, Any>()
                    map["resultList"] = data
                    map["type"] = "ERROR"
                    map["errorMsg"] = "upload failed"
                    GameMethodManager.callGameMethod(map, callBackName)
                } else {
                    val map = mutableMapOf<String, Any>()
                    map["resultList"] = data
                    map["type"] = "SUCCESS"
                    GameMethodManager.callGameMethod(map, callBackName)
                }
            } catch (t: Throwable) {
                val map = mutableMapOf<String, Any>()
                map["type"] = "ERROR"
                map["errorMsg"] = "${t.message}"
                GameMethodManager.callGameMethod(map, callBackName)
            }
        }
        val map = mutableMapOf<String, Any>()
        map["resultData"] = "SUCCESS"
        return GsonUtils.toJson(map)
    }

    @GameMethod
    fun videoCompression(
        videoPath: String, //视频路径
        cmd: String, //压缩命令
        callBackName: String, //回调方法名称
        quality: String, //回调方法名称
    ) {
        try {
            val outputPath =
                AppConstants.getVideoCompressPath() + "/${FileUtils.getFileName(videoPath)}"
            //根据文件名，判断压缩目录下，这个文件是否已经存在，如果存在则，直接返回
            if (outputPath.getFileSize() > 0) {
                val map = mutableMapOf<String, Any>()
                map["type"] = "SUCCESS"
                map["result"] = outputPath
                GameMethodManager.callGameMethod(map, callBackName)
                return
            }
            FFmpegUtils.compressVideo(videoPath, outputPath, cmd, object : CommandCallback {
                override fun start() {
                    val map = mutableMapOf<String, Any>()
                    map["type"] = "START"
                    GameMethodManager.callGameMethod(map, callBackName)
                }

                override fun succeed() {
                    val map = mutableMapOf<String, Any>()
                    map["type"] = "SUCCESS"
                    map["result"] = outputPath
                    GameMethodManager.callGameMethod(map, callBackName)
                }

                override fun error(throwable: Throwable) {
                    val map = mutableMapOf<String, Any>()
                    map["type"] = "ERROR"
                    map["result"] = videoPath
                    map["errorMsg"] = "${throwable.message}"
                    GameMethodManager.callGameMethod(map, callBackName)
                }

            })
        } catch (t: Throwable) {
            val map = mutableMapOf<String, Any>()
            map["type"] = "ERROR"
            map["result"] = videoPath
            map["errorMsg"] = "${t.message}"
            GameMethodManager.callGameMethod(map, callBackName)
        }
    }

    private fun reallyRequestPermission(permission: String) {
        PermissionUtils.permission(permission).request()
    }

    private fun checkRecordPermission(permission: String): Boolean {
        return PermissionUtils.isGranted(permission)
    }

    /**
     * 开始录音评测
     */
    private fun startRecordEvaluation(
        config: Map<String, Any>?,
        callBackName: String, //客户端回调录音结果给游戏的回调方法：
        volumeCallBackName: String //客户端回调录音音量给游戏的回调方法
    ) {
        TaiEvaluation.initEvaluation(config, object : EvaluationListener {
            override fun evaluationResult(result: String, audioFile: String?) {
                val map = mutableMapOf<String, Any>()
                map["type"] = "RESULT"
                map["result"] = result
                audioFile?.let {
                    map["audioFile"] = it
                }
                GameMethodManager.callGameMethod(map, callBackName)
            }

            override fun cancelRecord() {
                val map = mutableMapOf<String, Any>()
                map["type"] = "CANCEL"
                GameMethodManager.callGameMethod(map, callBackName)
            }

            override fun onError(error: String?) {
                val map = mutableMapOf<String, Any>()
                map["type"] = "ERROR"
                map["errorMsg"] = "$error"
                GameMethodManager.callGameMethod(map, callBackName)
            }

            override fun onLog(error: String?) {
                val map = mutableMapOf<String, Any>()
                map["type"] = "LOG"
                map["errorMsg"] = "$error"
                GameMethodManager.callGameMethod(map, callBackName)
            }

            override fun startRecord() {
                val map = mutableMapOf<String, Any>()
                map["type"] = "START"
                GameMethodManager.callGameMethod(map, callBackName)
            }

            override fun stopRecord() {
                val map = mutableMapOf<String, Any>()
                map["type"] = "STOP"
                GameMethodManager.callGameMethod(map, callBackName)
            }

            override fun volumeDb(volumeDb: Float) {
                val map = mutableMapOf<String, Any>()
                map["volume"] = volumeDb.toInt()
                GameMethodManager.callGameMethod(map, volumeCallBackName)
            }

            override fun onVad() {
                val map = mutableMapOf<String, Any>()
                map["type"] = "VAD"
                GameMethodManager.callGameMethod(map, callBackName)
            }
        })
        TaiEvaluation.startOralEvaluation()
    }

    fun clean() {
        act.clear()
    }
}