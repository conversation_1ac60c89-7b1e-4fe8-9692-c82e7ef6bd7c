plugins {
    alias(libs.plugins.common.gradle)
    alias(libs.plugins.kotlin.ksp)
}

dependencies {
    api(project(":module:module_base"))

    implementation(project(":base:base_tai"))
    implementation(project(":base:base_video_player"))
    implementation(project(":base:base_audio"))
    implementation(project(":base:base_record"))
    implementation(project(":base:base_share_ui"))
    implementation(project(":base:base_ffmpeg"))

    implementation(libs.immersionbar)
    implementation(libs.permissions)
    implementation(libs.banner)
    implementation(libs.retrofit.gson)
    implementation(libs.bundles.coil)
    ksp(libs.router.ksp)
}
