package com.snails.module.setting.viewbinder

import com.snails.base.image_loader.load
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.setting.R
import com.snails.module.setting.databinding.LayoutUploadPicItemBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年12月17日 16:24:23
 */
class UploadPicItemViewBinder(
    private val itemWith: Int,
    private val itemClick: (String) -> Unit,
    private val delete: (Int) -> Unit
) : ViewBindingDelegate<String, LayoutUploadPicItemBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<LayoutUploadPicItemBinding>, item: String
    ) {
        if (item == "+") {
            holder.binding.ivDeleteUploadPic.gone()
            holder.binding.ivUploadPic.setImageResource(R.drawable.svg_add_upload_pic)
        } else {
            holder.binding.ivDeleteUploadPic.visible()
            holder.binding.ivUploadPic.load(item)
        }
        val layoutParams = holder.binding.flyContainer.layoutParams
        layoutParams.width = itemWith
        layoutParams.height = itemWith
        holder.binding.flyContainer.layoutParams = layoutParams
        holder.binding.ivUploadPic.singleClick {
            itemClick.invoke(item)
        }
        holder.binding.ivDeleteUploadPic.singleClick {
            delete.invoke(holder.layoutPosition)
        }
    }
}