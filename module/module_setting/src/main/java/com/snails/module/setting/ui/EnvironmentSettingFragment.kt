package com.snails.module.setting.ui

import androidx.navigation.findNavController
import com.blankj.utilcode.util.ActivityUtils
import com.snails.base.network.constant.ENVIRONMENT_CONFIG
import com.snails.base.network.constant.ENVIRONMENT_DEV
import com.snails.base.network.constant.ENVIRONMENT_PRE
import com.snails.base.network.constant.ENVIRONMENT_PRO
import com.snails.base.network.constant.ENVIRONMENT_UAT
import com.snails.base.network.repository.storage.EnvironmentStorage
import com.snails.base.network.repository.storage.UserStorage
import com.snails.base.utils.ext.safeNavigate
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.BaseStateFragment
import com.snails.module.setting.R
import com.snails.module.setting.databinding.FragmentEnvironmentSettingBinding
import kotlin.system.exitProcess

/**
 * @Description 环境设置
 * <AUTHOR>
 * @CreateTime 2025年02月12日 11:46:38
 */
class EnvironmentSettingFragment : BaseStateFragment<FragmentEnvironmentSettingBinding>() {

    private var isGameConfig = false

    private var environment = EnvironmentStorage.me.getEnvironment()
    override fun initView() {
        super.initView()
        when (environment) {
            ENVIRONMENT_DEV -> {
                isGameConfig = false
                binding.rbDev.isChecked = true
            }

            ENVIRONMENT_UAT -> {
                isGameConfig = false
                binding.rbUat.isChecked = true
            }

            ENVIRONMENT_PRE -> {
                isGameConfig = false
                binding.rbPre.isChecked = true
            }

            ENVIRONMENT_PRO -> {
                isGameConfig = false
                binding.rbPro.isChecked = true
            }

            ENVIRONMENT_CONFIG -> {
                isGameConfig = true
                binding.rbGameConfig.isChecked = true
            }
        }
        binding.switchUploadCrashLog.isChecked = EnvironmentStorage.me.getAutoUploadCrashLog()
    }

    override fun initClick() {
        super.initClick()
        binding.commonTitleView.setBackClickListener {
            requireActivity().finish()
        }

        binding.rgGroup.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rbDev -> {
                    isGameConfig = false
                    environment = ENVIRONMENT_DEV
                }

                R.id.rbUat -> {
                    isGameConfig = false
                    environment = ENVIRONMENT_UAT
                }

                R.id.rbPro -> {
                    isGameConfig = false
                    environment = ENVIRONMENT_PRO
                }

                R.id.rbPre -> {
                    isGameConfig = false
                    environment = ENVIRONMENT_PRE
                }

                R.id.rbGameConfig -> {
                    environment = ENVIRONMENT_DEV
                    isGameConfig = true
                }
            }
        }

        binding.tvLookCrashLog.singleClick {
            it.findNavController()
                .safeNavigate(R.id.action_environmentSettingFragment_to_crashLogFragment)
        }

        binding.tvSure.singleClick {
            EnvironmentStorage.me.setEnvironment(environment)
            EnvironmentStorage.me.setIsGameConfig(isGameConfig)
            cleanDataAndExitLogin()
        }

        binding.switchUploadCrashLog.setOnCheckedChangeListener { _, isChecked ->
            EnvironmentStorage.me.setAutoUploadCrashLog(isChecked)
        }
    }

    //清楚数据和退出登录
    private fun cleanDataAndExitLogin() {
        //清除 用户、关闭音频服务等 等信息
        UserStorage.me.clean()
        ActivityUtils.finishAllActivities()
        kotlin.runCatching {
            exitProcess(0)
        }
    }
}