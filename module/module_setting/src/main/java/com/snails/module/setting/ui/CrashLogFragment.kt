package com.snails.module.setting.ui

import android.content.Intent
import androidx.core.content.FileProvider
import androidx.fragment.app.activityViewModels
import com.snails.module.base.BaseStateFragment
import com.snails.module.setting.databinding.FragmentCrashLogBinding
import com.snails.module.setting.viewmodel.CrashLogViewModel
import com.snails.module.setting.widget.CrashLogListView
import java.io.File

/**
 * @Description Crash 日志
 * <AUTHOR>
 * @CreateTime 2025年03月19日 13:06:11
 */
class CrashLogFragment : BaseStateFragment<FragmentCrashLogBinding>() {

    private val crashLogViewModel: CrashLogViewModel by activityViewModels()

    override fun createViewModel() = crashLogViewModel

    override fun initData() {
        super.initData()
        crashLogViewModel.loadLocalCrashLog()
    }

    override fun initObserve() {
        super.initObserve()
        crashLogViewModel.crashLogListLiveData.observe(viewLifecycleOwner) { list ->
            if (!list.isNullOrEmpty()) {
                binding.crashLogListView.setList(list)
            }
        }
    }

    override fun initClick() {
        super.initClick()
        binding.crashLogListView.itemClickListener = object : CrashLogListView.ItemClickListener {
            override fun itemClick(file: File) {
                systemShare(file)
            }
        }
    }

    private fun systemShare(file: File) {
        val ctx = context ?: return
        val intent = Intent(Intent.ACTION_SEND)
        intent.putExtra("subject", "")
        intent.putExtra("body", "")

        val uri = FileProvider.getUriForFile(
            ctx,
            "com.snailReading.student.fileProvider",
            file
        )

        intent.putExtra(Intent.EXTRA_STREAM, uri)//添加文件
        if (file.name.endsWith(".txt")) {
            intent.type = "text/plain"//纯文本
        } else {
            intent.type = "application/octet-stream" //二进制文件流
        }
        startActivity(Intent.createChooser(intent, "分享Crash 日志文件"))
    }
}