package com.snails.module.setting.ui

import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.Navigation
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.BaseStateFragment
import com.snails.module.base.utils.SnailTextWatcher
import com.snails.module.setting.databinding.FragmentNikeNameBinding
import com.snails.module.setting.viewmodel.SettingViewModel

/**
 * @Description 昵称
 * <AUTHOR>
 * @CreateTime 2024年12月12日 17:34:49
 */
class NickNameFragment : BaseStateFragment<FragmentNikeNameBinding>() {

    private val settingViewModel: SettingViewModel by viewModels()
    private var fromTeacher = false //是否来自教师端的修改

    override fun createViewModel() = settingViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initData() {
        super.initData()
        fromTeacher = getBooleanExtra("fromTeacher")
    }

    override fun initObserve() {
        super.initObserve()
        settingViewModel.updateUserInfoLiveData.observe(viewLifecycleOwner) {
            val content = binding.etNikeName.text?.toString()?.trim()
            setFragmentResult("requestKey", bundleOf("nickName" to content))
            val pop = Navigation.findNavController(binding.commonTitleView).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
    }

    override fun initClick() {
        super.initClick()
        binding.apply {
            etNikeName.addTextChangedListener(
                object : SnailTextWatcher() {
                    override fun onTextChanged(
                        s: CharSequence?,
                        start: Int,
                        before: Int,
                        count: Int
                    ) {
                        // 输入框不为空时显示删除按钮，否则隐藏
                        if (s.isNullOrEmpty()) {
                            ivClear.visibility = View.GONE
                        } else {
                            ivClear.visibility = View.VISIBLE
                        }
                    }
                }
            )
            ivClear.singleClick {
                etNikeName.text?.clear()
            }
            commonTitleView.setBackClickListener {
                val pop = Navigation.findNavController(it).popBackStack()
                if (!pop) {
                    requireActivity().finish()
                }
            }
            commonTitleView.setRightTxtClickListener {
                val content = binding.etNikeName.text?.toString()?.trim()
                if (!content.isNullOrEmpty()) {
                    val map = hashMapOf<String, Any>()
                    map["nickname"] = content
                    if (fromTeacher) {
                        settingViewModel.updateTeacherInfo(map)
                    } else {
                        settingViewModel.updateUserInfo(map)
                    }
                }
            }
        }
    }
}