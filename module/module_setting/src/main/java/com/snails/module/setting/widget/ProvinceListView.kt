package com.snails.module.setting.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.setting.ProvinceInfo
import com.snails.common.widget.MyDividerItemDecoration
import com.snails.module.setting.R
import com.snails.module.setting.viewbinder.ProvinceItemViewBinder

/**
 * @Description 批改列表
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */
class ProvinceListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val listAdapter = MultiTypeAdapter()
    var listener: ItemClickListener? = null

    init {
        initView()
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(ProvinceInfo::class.java, ProvinceItemViewBinder {
                listener?.itemClick(it)
            })
        }
        adapter = listAdapter
        addItemDecoration(MyDividerItemDecoration(context, VERTICAL).apply {
            setDrawable(context.resources.getDrawable(R.drawable.divider_location_list_item, null))
        })
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setList(tabList: List<ProvinceInfo>) {
        listAdapter.items = tabList
        listAdapter.notifyDataSetChanged()
    }


    interface ItemClickListener {
        fun itemClick(data: ProvinceInfo)
    }
}