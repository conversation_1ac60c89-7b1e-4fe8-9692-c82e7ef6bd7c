package com.snails.module.setting.viewbinder

import com.snails.base.utils.ext.singleClick
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.setting.databinding.LayoutFaqItemBinding
import java.io.File

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年12月17日 16:24:23
 */
class CrashLogItemViewBinder(private val itemClick: (File) -> Unit) :
    ViewBindingDelegate<File, LayoutFaqItemBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<LayoutFaqItemBinding>,
        item: File
    ) {
        holder.binding.apply {
            tvMessageTitle.text = item.name
        }
        holder.itemView.singleClick {
            itemClick.invoke(item)
        }
    }
}