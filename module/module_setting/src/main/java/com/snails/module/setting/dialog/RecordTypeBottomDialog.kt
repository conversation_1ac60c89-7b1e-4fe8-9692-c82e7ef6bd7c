package com.snails.module.setting.dialog

import com.blankj.utilcode.util.StringUtils
import com.snails.base.dialog.BaseBottomDialog
import com.snails.base.network.repository.storage.AppSettingStorage
import com.snails.base.utils.ext.singleClick
import com.snails.common.widget.WheelView
import com.snails.module.setting.R
import com.snails.module.setting.databinding.DialogRecordTypeBinding

/**
 * @Description 录像方式设置
 * <AUTHOR>
 * @CreateTime 2024年11月27日 19:34:47
 */
class RecordTypeBottomDialog() : BaseBottomDialog<DialogRecordTypeBinding>(heightDimen = 268) {
    private var currentSelectReason: String? = null

    private var selected: ((String) -> Unit)? = null

    constructor(selected: (String) -> Unit) : this() {
        this.selected = selected
    }

    override fun initView() {
        super.initView()
        binding.wheelViewReason.setDataItems(recordTypeList())
        binding.tvTitle.text = StringUtils.getString(R.string.str_record_type)
    }


    override fun initClick() {
        super.initClick()
        binding.wheelViewReason.setOnItemSelectedListener(object :
            WheelView.OnItemSelectedListener {
            override fun onItemSelected(wheelView: WheelView, data: Any, position: Int) {
                currentSelectReason = data.toString()
            }
        })
        binding.tvCancel.singleClick {
            dismiss()
        }
        binding.tvSure.singleClick {
            currentSelectReason?.let {
                if (it == "默认") {
                    AppSettingStorage.me.setAppVideoRecordType("DEFAULT")
                } else {
                    AppSettingStorage.me.setAppVideoRecordType("APP")
                }
                selected?.invoke(it)
            }
            dismiss()
        }
    }

    private fun recordTypeList(): MutableList<String> {
        return mutableListOf<String>().apply {
            add("默认")
            add("原生")
        }
    }
}