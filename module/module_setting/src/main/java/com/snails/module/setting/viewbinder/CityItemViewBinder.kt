package com.snails.module.setting.viewbinder

import com.snails.base.network.repository.info.setting.CityInfo
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.setting.databinding.LayoutLocationItemBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年12月17日 16:24:23
 */
class CityItemViewBinder(private val itemClick: (CityInfo) -> Unit) :
    ViewBindingDelegate<CityInfo, LayoutLocationItemBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<LayoutLocationItemBinding>,
        item: CityInfo
    ) {
        holder.binding.tvTitle.text = item.dictName ?: ""
        holder.itemView.singleClick {
            itemClick.invoke(item)
        }
    }
}