package com.snails.module.setting.dialog

import com.blankj.utilcode.util.ColorUtils
import com.snails.base.dialog.BaseBottomDialog
import com.snails.base.network.repository.info.setting.CityInfo
import com.snails.base.network.repository.info.setting.ProvinceInfo
import com.snails.base.network.repository.info.setting.RegionInfo
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.setting.R
import com.snails.module.setting.databinding.DialogLocationBinding
import com.snails.module.setting.widget.CityListView
import com.snails.module.setting.widget.ProvinceListView

/**
 * @Description 地区
 * <AUTHOR>
 * @CreateTime 2024年11月27日 19:34:47
 */
class LocationBottomDialog() : BaseBottomDialog<DialogLocationBinding>(heightDimen = 673) {

    private var chooseProvince: ProvinceInfo? = null //是否选择了省
    private var provinceList = mutableListOf<ProvinceInfo>()

    private var selected: ((String) -> Unit)? = null

    constructor(selected: (String) -> Unit) : this() {
        this.selected = selected
    }

    override fun initView() {
        super.initView()
        binding.provinceListView.apply {
            setList(provinceList)
            listener = object : ProvinceListView.ItemClickListener {
                override fun itemClick(data: ProvinceInfo) {
                    chooseProvince = data
                    data.nodes?.let {
                        binding.cityListView.apply {
                            setList(it)
                            visible()
                        }
                        binding.provinceListView.gone()
                    }
                    changeState()
                }
            }
        }
        binding.cityListView.apply {
            listener = object : CityListView.ItemClickListener {
                override fun itemClick(data: CityInfo) {
                    chooseProvince?.let {
                        selected?.invoke(it.dictName + "," + data.dictName)
                    }
                    dismiss()
                }
            }
        }
    }

    fun setData(info: RegionInfo) {
        provinceList.apply {
            clear()
            info.nodes?.let { addAll(it) }
        }
    }

    override fun initClick() {
        super.initClick()
        binding.tvCancel.singleClick {
            dismiss()
        }

        binding.tvProvince.singleClick {
            binding.provinceListView.visible()
            binding.cityListView.gone()
            chooseProvince = null
            changeState()
        }
    }

    private fun changeState() {
        if (chooseProvince == null) {
            binding.tvProvince.setTextColor(ColorUtils.getColor(R.color.text_headline))
            binding.tvCity.setTextColor(ColorUtils.getColor(R.color.text_disable))
            return
        }
        binding.tvProvince.setTextColor(ColorUtils.getColor(R.color.text_disable))
        binding.tvCity.setTextColor(ColorUtils.getColor(R.color.text_headline))
    }
}