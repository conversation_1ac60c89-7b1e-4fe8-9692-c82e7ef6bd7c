package com.snails.module.setting

import android.content.Intent
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.module.base.BaseVBActivity
import com.snails.module.setting.databinding.ActivitySettingBinding
import com.therouter.router.Route

/**
 * @Description 设置
 * <AUTHOR>
 * @CreateTime 2024 年 9 月 21 日 10:49:09
 */
@Route(path = RouterPath.BASE_SETTING)
class SettingActivity : BaseVBActivity<ActivitySettingBinding>() {

    override fun initData() {
        checkRouter(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.let {
            checkRouter(it)
        }
    }

    private fun checkRouter(intent: Intent) {
        HRouter.getOriginalPath(intent)?.let { targetFragmentPath ->
            dealRouter(targetFragmentPath)
        }
    }

    private fun dealRouter(fragmentPath: String?) {
        val navController =
            binding.navHostFragment.getFragment<NavHostFragment>().findNavController()
        val navGraph = navController.navInflater.inflate(R.navigation.setting_navigation)
        val startDestinationId = when (fragmentPath) {
            RouterPath.SETTING_HOME -> {
                //设置主页
                R.id.settingFragment
            }

            RouterPath.BABY_INFO -> {
                //宝贝资料
                R.id.babyInfoFragment
            }

            RouterPath.MESSAGE_NOTIFY -> {
                //消息通知
                R.id.messageNotificationFragment
            }

            RouterPath.HELP_FEEDBACK -> {
                //帮助与反馈
                R.id.helpFeedbackFragment
            }

            RouterPath.DELETE_ACCOUNT -> {
                //帮助与反馈
                R.id.helpFeedbackFragment
            }

            RouterPath.ABOUT_US -> {
                //关于我们
                R.id.aboutUsFragment
            }

            RouterPath.CONTACT_US -> {
                //联系我们
                R.id.contactUsFragment
            }

            RouterPath.FEEDBACK -> {
                //投诉与建议
                R.id.feedbackFragment
            }

            RouterPath.ENVIRONMENT_SETTING -> {
                //环境设置
                R.id.environmentSettingFragment
            }

            RouterPath.CRASH_LOG -> {
                //崩溃列表
                R.id.crashLogFragment
            }

            else -> {
                //设置主页
                R.id.settingFragment
            }
        }
        navGraph.setStartDestination(startDestinationId)
        navController.graph = navGraph
    }
}