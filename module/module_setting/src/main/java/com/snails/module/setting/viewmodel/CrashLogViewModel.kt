package com.snails.module.setting.viewmodel

import androidx.lifecycle.MutableLiveData
import com.snails.base.crash.CrashKitManager
import com.snails.module.base.BaseViewModel
import com.snails.module.base.utils.SingleLiveEventLiveData
import java.io.File

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年03月19日 13:19:06
 */
class CrashLogViewModel : BaseViewModel() {

    //崩溃日志列表
    val crashLogListLiveData: MutableLiveData<List<File>> = SingleLiveEventLiveData()

    fun loadLocalCrashLog() {
        localRequest(
            showLoadingDialog = true,
            request = {
                CrashKitManager.crashFiles().toList()
            },
            success = {
                crashLogListLiveData.value = it
            }
        )
    }
}