package com.snails.module.setting.ui

import androidx.navigation.findNavController
import com.snails.module.base.BaseStateFragment
import com.snails.module.setting.databinding.FragmentFaqDetailsBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年03月24日 09:57:36
 */
class FaqDetailsFragment : BaseStateFragment<FragmentFaqDetailsBinding>() {

    override fun initClick() {
        super.initClick()
        binding.commonTitleView.setBackClickListener {
            val pop = it.findNavController().popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
    }

    override fun initView() {
        super.initView()
        getStringExtra("title")?.let {
            binding.tvFaqTitle.text = it
        }
        getStringExtra("data")?.let {
            binding.tvResult.text = it
        }
    }
}