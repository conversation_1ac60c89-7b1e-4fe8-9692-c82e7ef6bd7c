package com.snails.module.setting.dialog

import com.blankj.utilcode.util.StringUtils
import com.snails.base.dialog.BaseBottomDialog
import com.snails.base.utils.ext.singleClick
import com.snails.common.widget.WheelView
import com.snails.module.setting.R
import com.snails.module.setting.databinding.DialogFeedbackBinding

/**
 * @Description 反馈 or 投诉
 * <AUTHOR>
 * @CreateTime 2024年11月27日 19:34:47
 */
class FeedbackBottomDialog() : BaseBottomDialog<DialogFeedbackBinding>(heightDimen = 268) {
    //当前选中的原因
    private var currentSelectReason: String? = null

    private var type: String? = null
    private var selected: ((String) -> Unit)? = null

    constructor(type: String?, selected: (String) -> Unit) : this() {
        this.type = type
        this.selected = selected
    }

    override fun initView() {
        super.initView()
        if (type == "1") {
            binding.wheelViewReason.setDataItems(complaintReasonList())
            binding.tvTitle.text = StringUtils.getString(R.string.str_complaint_reason_text)
        } else {
            binding.wheelViewReason.setDataItems(feedbackReasonList())
            binding.tvTitle.text = StringUtils.getString(R.string.str_feedback_reason_text)
        }

    }


    override fun initClick() {
        super.initClick()
        binding.wheelViewReason.setOnItemSelectedListener(object :
            WheelView.OnItemSelectedListener {
            override fun onItemSelected(wheelView: WheelView, data: Any, position: Int) {
                currentSelectReason = data.toString()
            }
        })
        binding.tvCancel.singleClick {
            dismiss()
        }
        binding.tvSure.singleClick {
            currentSelectReason?.let { it1 -> selected?.invoke(it1) }
            dismiss()
        }
    }

    private fun feedbackReasonList(): MutableList<String> {
        return mutableListOf<String>().apply {
            add("界面设计不合理")
            add("操作复杂难以理解")
            add("希望增加新功能")
            add("对现有功能的改进建议")
            add("希望增加更多学习材料或资源")
            add("其他未列明的反馈原因")
        }
    }

    private fun complaintReasonList(): MutableList<String> {
        return mutableListOf<String>().apply {
            add("应用崩溃、闪退或部分功能不可用")
            add("内容不准确或误导")
            add("内容侵犯版权")
            add("内容不适当或冒犯")
            add("个人信息泄露")
            add("其他未列明的投诉原因")
        }
    }
}