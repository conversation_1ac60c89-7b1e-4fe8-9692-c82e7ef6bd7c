package com.snails.module.setting.utils

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper


/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年12月17日 14:56:48
 */
object ImageLoaderUtils {
    fun assertValidRequest(context: Context?): <PERSON><PERSON>an {
        if (context is Activity) {
            return !isDestroy(context)
        } else if (context is ContextWrapper) {
            val contextWrapper = context
            if (contextWrapper.baseContext is Activity) {
                val activity = contextWrapper.baseContext as Activity
                return !isDestroy(activity)
            }
        }
        return true
    }

    private fun isDestroy(activity: Activity?): <PERSON><PERSON><PERSON> {
        if (activity == null) {
            return true
        }
        return activity.isFinishing || activity.isDestroyed
    }
}