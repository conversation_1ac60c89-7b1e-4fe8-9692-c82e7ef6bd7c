package com.snails.module.setting.ui

import android.os.Build
import androidx.fragment.app.viewModels
import androidx.navigation.Navigation
import com.blankj.utilcode.util.StringUtils
import com.blankj.utilcode.util.ToastUtils
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.utils.PermissionCheckUtils
import com.snails.module.base.BaseStateFragment
import com.snails.module.base.bean.PermissionScene
import com.snails.module.base.dialog.PermissionDescDialog
import com.snails.module.setting.R
import com.snails.module.setting.databinding.FragmentFeedbackBinding
import com.snails.module.setting.dialog.FeedbackBottomDialog
import com.snails.module.setting.utils.GlideEngine
import com.snails.module.setting.utils.SandboxFileEngine
import com.snails.module.setting.viewmodel.SettingViewModel
import com.snails.module.setting.widget.UploadPicListView

/**
 * @Description 投诉 or 反馈
 * <AUTHOR>
 * @CreateTime 2024年12月24日 10:22:42
 */
class FeedbackFragment : BaseStateFragment<FragmentFeedbackBinding>() {

    private var type: String? = ""

    private val settingViewModel: SettingViewModel by viewModels()

    override fun createViewModel() = settingViewModel

    override fun initData() {
        super.initData()
        binding.uploadPicListView.setList(mutableListOf<String>().apply {
            add("+")
        })
    }

    override fun initView() {
        super.initView()
        type = getStringExtra("type")
        type?.let {
            if (it == "1") {
                binding.commonTitleView.setTitle(StringUtils.getString(R.string.str_complaint))
                binding.tvReasonTitle.text =
                    StringUtils.getString(R.string.str_complaint_reason_text)
                binding.etContent.setHint(StringUtils.getString(R.string.str_complaint_hint_text))
            } else {
                binding.commonTitleView.setTitle(StringUtils.getString(R.string.str_feedback))
                binding.tvReasonTitle.text =
                    StringUtils.getString(R.string.str_feedback_reason_text)
                binding.etContent.setHint(StringUtils.getString(R.string.str_feedback_hint_text))
            }
        }
    }

    override fun initClick() {
        super.initClick()
        //返回
        binding.commonTitleView.setBackClickListener {
            val pop = Navigation.findNavController(it).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
        binding.uploadPicListView.listener = object : UploadPicListView.ItemClickListener {
            override fun itemClick(data: String) {
                if (data == "+") {
                    openGallery()
                }
            }

            override fun delete(pos: Int) {
                binding.uploadPicListView.removeItem(pos)
            }
        }
        binding.tvReason.singleClick {
            FeedbackBottomDialog(type) {
                binding.tvReason.text = it
            }.show(childFragmentManager, "")
        }
        binding.tvCommit.singleClick {
            commit()
        }
    }

    private fun commit() {
        val tp = type ?: return
        val reason = binding.tvReason.text?.toString()?.trim()
        if (reason.isNullOrEmpty()) {
            ToastUtils.showShort("请选择" + binding.tvReasonTitle.text?.toString())
            return
        }
        val content = binding.etContent.text?.toString()?.trim()
        if (content.isNullOrEmpty()) {
            ToastUtils.showShort(binding.etContent.hint.toString())
            return
        }
        val list = binding.uploadPicListView.getItemList().apply {
            remove("+")
        }
        val phone = binding.etPhone.text.toString().trim()
        settingViewModel.commitFeedBackContent(
            tp,
            reason,
            content,
            phoneNumber = phone,
            pics = list
        ) {
            val pop = Navigation.findNavController(binding.tvCommit).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
    }

    private fun openGallery() {
        requestPermission {
            startGallery()
        }
    }

    private fun startGallery() {
        val act = activity ?: return
        PictureSelector
            .create(act)
            .openGallery(SelectMimeType.ofImage())
            .setImageEngine(GlideEngine.createGlideEngine())
            .isPreviewImage(false)
            .isDisplayCamera(false)
            .setMaxSelectNum(6 - binding.uploadPicListView.getItemList().size + 1)
            .setMinSelectNum(1)
            .setSelectMaxFileSize(20 * 1024)
            .setSelectMinFileSize(1)
            .setSandboxFileEngine(SandboxFileEngine())
            .forResult(object : OnResultCallbackListener<LocalMedia> {
                override fun onResult(result: ArrayList<LocalMedia>?) {
                    handleData(result)
                }

                override fun onCancel() {
                }
            })
    }

    /**
     * 请求权限
     */
    private fun requestPermission(callback: () -> Unit) {
        context?.let { ctx ->
            val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                Permission.READ_MEDIA_IMAGES
            } else {
                Permission.READ_EXTERNAL_STORAGE
            }
            val havaPermission = PermissionCheckUtils.havaPermission(ctx, permissions)
            if (havaPermission) {
                callback.invoke()
            } else {
                PermissionDescDialog(PermissionScene.PHOTO) {
                    XXPermissions.with(ctx).permission(permissions)
                        .request { _, allGranted ->
                            if (allGranted) {
                                callback.invoke()
                            }
                        }
                }.show(childFragmentManager, "")
            }
        }
    }

    private fun handleData(result: ArrayList<LocalMedia>?) {
        val list = mutableListOf<String>()
        result?.forEach { item ->
            item.sandboxPath?.let {
                list.add(it)
            }
        }
        binding.uploadPicListView.setList(list)
    }
}