package com.snails.module.setting.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.ToastUtils
import com.snails.base.network.error.ApiException
import com.snails.base.network.manager.NetworkManager
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.downloadx.download
import com.snails.base.network.repository.info.login.UserInfo
import com.snails.base.network.repository.info.setting.HelpFeedbackInfo
import com.snails.base.network.repository.info.setting.MessageNotifyListInfo
import com.snails.base.network.repository.info.setting.RegionInfo
import com.snails.base.network.repository.info.setting.UserSettingInfo
import com.snails.base.network.repository.storage.UserStorage
import com.snails.base.utils.constants.AppConstants
import com.snails.base.utils.ext.extractMediaFilesFromObject
import com.snails.module.base.BaseViewModel
import com.snails.module.base.bean.StateType
import com.snails.module.base.utils.SingleLiveEventLiveData
import java.io.File

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月28日 09:50:47
 */
class SettingViewModel : BaseViewModel() {

    //获取个人信息设置页的数据（年级和头像）
    val userSettingInfoLiveData: MutableLiveData<UserSettingInfo> = SingleLiveEventLiveData()

    //地区信息
    val provinceInfoLiveData: MutableLiveData<RegionInfo> = SingleLiveEventLiveData()

    //更新个人信息状态
    val updateUserInfoLiveData: MutableLiveData<Boolean> = SingleLiveEventLiveData()

    //个人信息状态
    val userInfoLiveData: MutableLiveData<UserInfo> = SingleLiveEventLiveData()

    //消息通知
    val msgNotifyListLiveData: MutableLiveData<MessageNotifyListInfo> = SingleLiveEventLiveData()

    //常见问题列表
    val faqListLiveData: MutableLiveData<HelpFeedbackInfo> = SingleLiveEventLiveData()

    //消息通知-分页
    var messagePage = 1

    /**
     * 获取个人信息设置页的数据（年级和头像）
     */
    fun getSettingInfoData(stateType: StateType = StateType.PAGE) {
        request(
            stateType = stateType,
            request = {
                SnailRepository.me.getSettingInfoData()
            },
            success = { data ->
                data?.let {
                    userSettingInfoLiveData.value = it
                    it.defaultAvatars?.extractMediaFilesFromObject()?.let { list ->
                        runCatching {
                            viewModelScope.download(list)
                        }
                    }
                }
            }
        )
    }

    /**
     * 获取省市地区数据
     */
    fun getAllRegionData(stateType: StateType = StateType.NONE) {
        request(stateType = stateType, request = {
            SnailRepository.me.getAllRegionData()
        }, success = { data ->
            data?.let {
                provinceInfoLiveData.value = it
            }
        })
    }

    /**
     * 获取个人信息
     */
    fun getUserProfile(stateType: StateType = StateType.NONE) {
        request(stateType = stateType, request = {
            SnailRepository.me.getUserProfile()
        }, success = { data ->
            data?.let {
                userInfoLiveData.value = it
            }
        })
    }

    /**
     * 更新个人信息
     * "address": "string", 地址，不同行政级别英文逗号分割
     * "avatar": "string", 用户头像文件id
     * "birthday": "string", yyyy-mm-dd
     * "classGrade": "string",
     * "gender": 0, 0 未知，1 为男性，2 为女性
     * "nickname": "string"
     */
    fun updateUserInfo(map: HashMap<String, Any>) {
        request(
            stateType = StateType.DIALOG,
            request = {
                SnailRepository.me.updateUserInfo(map)
            },
            success = {
                updateUserInfoLiveData.value = true
            },
            failed = {
                updateUserInfoLiveData.value = false
            }
        )
    }

    fun updateTeacherInfo(map: HashMap<String, Any>) {
        request(
            stateType = StateType.DIALOG,
            request = {
                SnailRepository.me.updateTeacherInfo(map)
            },
            success = {
                updateUserInfoLiveData.value = true
            },
            failed = {
                updateUserInfoLiveData.value = false
            }
        )
    }

    /**
     * 设置用户头像
     */
    fun setUserAvatar(filePath: String, isTeacher: Boolean = false, succeed: (String) -> Unit) {
        val serialExecutor: suspend () -> Triple<Any?, Any?, Any?> = {
            //第一步：拿签名
            val suffix = FileUtils.getFileExtension(filePath)?.lowercase() ?: "png"
            val ossAccessSignInfo = SnailRepository.me.ossAccessSign(suffix, "USER_AVATAR", 1, null)
            if (ossAccessSignInfo.isFailed()) {
                throw ApiException(ossAccessSignInfo.code, ossAccessSignInfo.message)
            }
            //第二步：设置OSS地址
            NetworkManager.uploadOssHost = ossAccessSignInfo.data?.host
            //第三步：上传OSS
            val key = ossAccessSignInfo.data?.files?.firstOrNull()?.filePath
            val policy = ossAccessSignInfo.data?.encodedPolicy
            val ossAccessKeyId = ossAccessSignInfo.data?.accessKeyId
            val signature = ossAccessSignInfo.data?.postSignature
            SnailRepository.me.uploadOss(filePath, key, policy, ossAccessKeyId, signature, null)
            //第四步：更新头像
            val map = HashMap<String, Any>()
            ossAccessSignInfo.data?.files?.firstOrNull()?.fileId?.let {
                map["avatar"] = it
            }
            val result = if (isTeacher) {
                SnailRepository.me.updateTeacherInfo(map)
            } else {
                SnailRepository.me.updateUserInfo(map)
            }
            if (result.isFailed()) {
                throw ApiException(result.code, result.message)
            }
            Triple(result.data, result.data, result.data)
        }

        executeRequestWithFlow(
            stateType = StateType.DIALOG,
            requestExecutor = serialExecutor,
            success = {
                succeed.invoke(filePath)
            },
            failed = {
                //上传失败
                ToastUtils.showShort("头像上传失败，请重试")
            })
    }

    /**
     * 获取消息列表
     */
    fun getMessageList(stateType: StateType = StateType.NONE) {
        request(stateType = stateType, request = {
            SnailRepository.me.getMessageList(messagePage)
        }, success = { data ->
            data?.let {
                msgNotifyListLiveData.value = it
            }
        })
    }

    /**
     * 获取帮助与反馈信息
     */
    fun getHelpFeedbackInfo() {
        request(stateType = StateType.PAGE, request = {
            SnailRepository.me.getHelpFeedbackInfo()
        }, success = { data ->
            data?.let {
                faqListLiveData.value = it
            }
        })
    }

    /**
     * 提交反馈内容
     */
    fun commitFeedBackContent(
        type: String,
        reason: String,
        message: String,
        phoneNumber: String?,
        pics: List<String>?,
        succeed: () -> Unit
    ) {
        if (pics?.isNotEmpty() == true) {
            commitFeedBackContentAndPic(type, reason, message, phoneNumber, pics, succeed)
            return
        }
        val map = HashMap<String, Any>()
        map["type"] = if (type == "1") "COMPLAINT" else "FEEDBACK"
        map["reason"] = reason
        map["message"] = message
        phoneNumber?.let {
            map["phoneNumber"] = it
        }
        request(
            stateType = StateType.DIALOG,
            request = {
                SnailRepository.me.commitFeedBackContent(map)
            },
            success = {
                succeedTips(type)
                succeed.invoke()
            },
            failed = {
                filedTips(type)
            }
        )
    }

    private fun commitFeedBackContentAndPic(
        type: String,
        reason: String,
        message: String,
        phoneNumber: String?,
        pics: List<String>,
        succeed: () -> Unit
    ) {
        val serialExecutor: suspend () -> Triple<Any?, Any?, Any?> = {
            val suffix = FileUtils.getFileExtension(pics.first())?.lowercase() ?: "png"
            //第一步：拿签名
            val ossAccessSignInfo =
                SnailRepository.me.ossAccessSign(suffix, "FEEDBACK", pics.size, null)
            if (ossAccessSignInfo.isFailed()) {
                throw ApiException(ossAccessSignInfo.code, ossAccessSignInfo.message)
            }
            //第二步：设置OSS地址
            NetworkManager.uploadOssHost = ossAccessSignInfo.data?.host
            //第三步：上传OSS
            val key = ossAccessSignInfo.data?.files
            val policy = ossAccessSignInfo.data?.encodedPolicy
            val ossAccessKeyId = ossAccessSignInfo.data?.accessKeyId
            val signature = ossAccessSignInfo.data?.postSignature
            SnailRepository.me.uploadOssList(pics, key, policy, ossAccessKeyId, signature, null)
            //第四步：更新头像
            val map = HashMap<String, Any>()
            map["type"] = if (type == "1") "COMPLAINT" else "FEEDBACK"
            map["reason"] = reason
            map["message"] = message
            phoneNumber?.let {
                map["phoneNumber"] = it
            }
            val result = SnailRepository.me.commitFeedBackContent(map)
            if (result.isFailed()) {
                throw ApiException(result.code, result.message)
            }
            Triple(result.data, result.data, result.data)
        }

        executeRequestWithFlow(
            stateType = StateType.DIALOG,
            requestExecutor = serialExecutor,
            success = {
                succeedTips(type)
                succeed.invoke()
            },
            failed = {
                //上传失败
                filedTips(type)
            }
        )
    }

    private fun succeedTips(type: String) {
        if (type == "1") {
            ToastUtils.showShort("投诉成功，我们将加急为您处理！")
        } else {
            ToastUtils.showShort("反馈成功，感谢您的建议！")
        }
    }

    private fun filedTips(type: String) {
        if (type == "1") {
            ToastUtils.showShort("投诉出现错误，您可以通过电话联系我们！")
        } else {
            ToastUtils.showShort("反馈失败，请稍后重试！")
        }
    }

    /**
     * 获取帮助与反馈信息
     */
    fun deleteAccount(succeed: () -> Unit) {
        val uid = UserStorage.me.getUserInfo()?.uid ?: return
        request(
            stateType = StateType.DIALOG,
            request = {
                SnailRepository.me.deleteAccount(uid)
            },
            success = {
                succeed.invoke()
            },
            failed = {
                ToastUtils.showShort("注销失败，请稍后重试。")
            }
        )
    }

    fun uploadGameLog() {
        val gameLogPath = AppConstants.gameLogSavePath()
        val fileList = File(gameLogPath).listFiles()?.toList()
        if (fileList.isNullOrEmpty()) {
            ToastUtils.showShort("日志已全部上传")
            return
        }
        val uid = UserStorage.me.getUserInfo()?.uid
        localRequest(
            showLoadingDialog = true,
            request = {
                if (fileList.isNotEmpty()) {
                    //如果是要自己拿到这些文件，建议根据时间来排个序
                    val paths = mutableListOf<String>()
                    fileList.forEach {
                        paths.add("GameLogs/Android/$uid/" + it.name)
                    }
                    //第一步：获取签名
                    val suffix =
                        FileUtils.getFileExtension(fileList.firstOrNull())?.lowercase() ?: "png"

                    val ossAccessSignInfo =
                        SnailRepository.me.ossAccessSign(suffix, "APP_LOG", fileList.size, paths)
                    if (ossAccessSignInfo.isFailed()) {
                        throw Throwable("upload crash log failed")
                    }
                    //第二步：设置OSS地址
                    NetworkManager.uploadOssHost = ossAccessSignInfo.data?.host

                    //第三步：上传OSS
                    val key = ossAccessSignInfo.data?.files
                    val policy = ossAccessSignInfo.data?.encodedPolicy
                    val ossAccessKeyId = ossAccessSignInfo.data?.accessKeyId
                    val signature = ossAccessSignInfo.data?.postSignature
                    val filePathList = mutableListOf<String>()
                    fileList.forEach {
                        filePathList.add(it.path)
                    }
                    SnailRepository.me.uploadOssList(
                        filePathList,
                        key,
                        policy,
                        ossAccessKeyId,
                        signature,
                        null
                    )
                }
            },
            success = {
                ToastUtils.showShort("日志已上传")
                FileUtils.deleteFilesInDir(gameLogPath)
            }
        )
    }
}