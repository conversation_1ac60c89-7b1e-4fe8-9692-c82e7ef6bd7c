package com.snails.module.setting.ui

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.navigation.Navigation
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.login.UserInfo
import com.snails.base.network.repository.info.setting.UserDefaultAvatarInfo
import com.snails.base.network.repository.storage.TeacherStorage
import com.snails.base.network.repository.storage.UserStorage
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.BaseStateFragment
import com.snails.module.setting.BuildConfig
import com.snails.module.setting.R
import com.snails.module.setting.databinding.FragmentBabyInfoBinding
import com.snails.module.setting.dialog.BirthdayBottomDialog
import com.snails.module.setting.dialog.GenderBottomDialog
import com.snails.module.setting.dialog.GradeBottomDialog
import com.snails.module.setting.dialog.LocationBottomDialog
import com.snails.module.setting.dialog.TakePhotoBottomDialog
import com.snails.module.setting.dialog.TakePhotoTeacherBottomDialog
import com.snails.module.setting.viewmodel.SettingViewModel

/**
 * @Description 宝贝信息
 * <AUTHOR>
 * @CreateTime 2024年12月12日 17:34:49
 */
class BabyInfoFragment : BaseStateFragment<FragmentBabyInfoBinding>() {

    private var takePhotoBottomDialog: TakePhotoBottomDialog? = null
    private var takePhotoTeacherBottomDialog: TakePhotoTeacherBottomDialog? = null
    private var genderBottomDialog: GenderBottomDialog? = null
    private var birthdayBottomDialog: BirthdayBottomDialog? = null
    private var gradeBottomDialog: GradeBottomDialog? = null
    private var locationBottomDialog: LocationBottomDialog? = null

    private var userInfo: UserInfo? = null

    private val settingViewModel: SettingViewModel by viewModels()
    private var fromTeacher = false //是否来自教师端的修改

    private var clickCount = 0
    private val clickThreshold = 5 // 连续点击五次
    private var lastClickTime: Long = 0
    private val clickInterval: Long = 300 // 设置点击间隔为300毫秒


    override fun createViewModel() = settingViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initData() {
        super.initData()
        initDialog()

        fromTeacher = getBooleanExtra("fromTeacher")
        userInfo = if (fromTeacher) {
            TeacherStorage.me.getTeacherInfo()
        } else {
            UserStorage.me.getUserInfo()
        }
        settingViewModel.apply {
            if (userSettingInfoLiveData.value == null && !fromTeacher) {
                getSettingInfoData()
            }
            if (provinceInfoLiveData.value == null && !fromTeacher) {
                getAllRegionData()
            }
        }
        parentFragmentManager.setFragmentResultListener("requestKey", this) { _, bundle ->
            // 获取结果
            val result = bundle.getString("nickName")
            // 处理结果
            binding.civBabyName.setItemRightTitle(result ?: "")
        }
    }

    override fun initView() {
        super.initView()
        showUserInfo(userInfo)
    }

    private fun showUserInfo(userInfo: UserInfo?) {
        userInfo?.let {
            binding.sivBabyHead.load(
                it.avatar,
                placeholder = R.drawable.svg_w102_h102_placeholder,
                error = R.drawable.svg_w102_h102_error
            )

            if (fromTeacher) { //老师
                binding.civBabyName.apply {
                    binding.civBabyName.setItemTitle("昵称")
                    binding.civBabyName.setItemRightTitle(it.nickname)
                }
                binding.civBabyGender.gone()
                binding.civBabyBirthday.gone()
                binding.civBabyStudyAge.gone()
                binding.civBabyLocation.gone()
            } else { //学生
                binding.civBabyName.setItemRightTitle(it.nickname)
                binding.civBabyGender.setItemRightTitle(it.getGenderTxt())
                binding.civBabyBirthday.setItemRightTitle(it.birthday)
                binding.civBabyStudyAge.setItemRightTitle(it.classGrade)
                binding.civBabyLocation.setItemRightTitle(it.address)
            }
        }
    }

    override fun initObserve() {
        super.initObserve()

        settingViewModel.apply {
            userSettingInfoLiveData.observe(viewLifecycleOwner) { info ->
                info.defaultAvatars?.let {
                    takePhotoBottomDialog?.setUserDefaultAvatar(it)
                }
                info.grades?.let {
                    gradeBottomDialog?.setGrades(it)
                }
            }

            provinceInfoLiveData.observe(viewLifecycleOwner) { info ->
                locationBottomDialog?.setData(info)
            }

            updateUserInfoLiveData.observe(viewLifecycleOwner) {
                settingViewModel.getUserProfile()
            }
            userInfoLiveData.observe(viewLifecycleOwner) {
                showUserInfo(it)
            }
        }
    }

    override fun initClick() {
        super.initClick()
        binding.commonTitleView.setBackClickListener {
            val pop = Navigation.findNavController(it).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
        binding.sivBabyHead.singleClick {
            if (fromTeacher) {
                takePhotoTeacherBottomDialog?.show(childFragmentManager, "")
            } else {
                takePhotoBottomDialog?.show(childFragmentManager, "")
            }
        }

        binding.civBabyName.singleClick {
            Navigation.findNavController(it)
                .navigate(R.id.action_babyInfoFragment_to_babyNickNameFragment, Bundle().apply {
                    putBoolean("fromTeacher", fromTeacher)
                })
        }

        binding.civBabyGender.singleClick {
            genderBottomDialog?.show(childFragmentManager, "")
        }

        binding.civBabyBirthday.singleClick {
            birthdayBottomDialog?.show(childFragmentManager, "")
        }

        binding.civBabyStudyAge.singleClick {
            gradeBottomDialog?.show(childFragmentManager, "")
        }

        binding.civBabyLocation.singleClick {
            locationBottomDialog?.show(childFragmentManager, "")
        }
        binding.vEnvironmentSetting.setOnClickListener {
            if (BuildConfig.BUILD_TYPE != "release") {
                val currentTime = System.currentTimeMillis()

                // 如果两次点击的时间差超过设置的点击间隔，重置点击次数
                if (currentTime - lastClickTime > clickInterval) {
                    clickCount = 0
                }

                lastClickTime = currentTime
                clickCount++

                if (clickCount >= clickThreshold) {
                    // 达到连续点击5次，执行事件
                    Navigation.findNavController(it)
                        .navigate(R.id.action_babyInfoFragment_to_environmentSettingFragment)
                    // 重置点击计数器
                    clickCount = 0
                }
            }
        }

        takePhotoBottomDialog?.takePhotoListener =
            object : TakePhotoBottomDialog.TakePhotoListener {
                override fun photo(path: String) {
                    settingViewModel.setUserAvatar(path) {
                        binding.sivBabyHead.load(
                            it,
                            placeholder = R.drawable.svg_w102_h102_placeholder,
                            error = R.drawable.svg_w102_h102_error
                        )
                    }
                }

                override fun chooseDefaultAvatar(data: UserDefaultAvatarInfo) {
                    data.avatarFileId?.let {
                        val map = hashMapOf<String, Any>()
                        map["avatar"] = it
                        settingViewModel.updateUserInfo(map)
                    }
                }
            }
        takePhotoTeacherBottomDialog?.takePhotoListener =
            object : TakePhotoTeacherBottomDialog.TakePhotoListener {
                override fun photo(path: String) {
                    settingViewModel.setUserAvatar(path, isTeacher = true) {
                        binding.sivBabyHead.load(
                            it,
                            placeholder = R.drawable.svg_w102_h102_placeholder,
                            error = R.drawable.svg_w102_h102_error
                        )
                    }
                }
            }
    }


    private fun initDialog() {
        if (takePhotoBottomDialog == null) {
            takePhotoBottomDialog = TakePhotoBottomDialog()
        }
        if (genderBottomDialog == null) {
            genderBottomDialog = GenderBottomDialog { gender ->
                val map = hashMapOf<String, Any>()
                map["gender"] = if (gender == "男") 1 else 2
                settingViewModel.updateUserInfo(map)
            }
        }
        if (birthdayBottomDialog == null) {
            birthdayBottomDialog = BirthdayBottomDialog { birthday ->
                val map = hashMapOf<String, Any>()
                map["birthday"] = birthday
                settingViewModel.updateUserInfo(map)
            }
        }
        if (gradeBottomDialog == null) {
            gradeBottomDialog = GradeBottomDialog { classGrade ->
                val map = hashMapOf<String, Any>()
                map["classGrade"] = classGrade
                settingViewModel.updateUserInfo(map)
            }
        }
        if (locationBottomDialog == null) {
            locationBottomDialog = LocationBottomDialog { location ->
                val map = hashMapOf<String, Any>()
                map["address"] = location
                settingViewModel.updateUserInfo(map)
            }
        }
        if (takePhotoTeacherBottomDialog == null) {
            takePhotoTeacherBottomDialog = TakePhotoTeacherBottomDialog()
        }
    }

    override fun onRetry() {
        super.onRetry()
        settingViewModel.getSettingInfoData()
    }
}