package com.snails.module.setting.dialog

import com.snails.base.dialog.BaseBottomDialog
import com.snails.base.utils.ext.singleClick
import com.snails.module.setting.databinding.DialogDeleteAccountBinding

/**
 * @Description 注销账号
 * <AUTHOR>
 * @CreateTime 2024年11月27日 19:34:47
 */
class DeleteAccountDialog() : BaseBottomDialog<DialogDeleteAccountBinding>(heightDimen = 378) {
    private var delete: (() -> Unit)? = null

    constructor(delete: () -> Unit) : this() {
        this.delete = delete
    }

    override fun initClick() {
        super.initClick()
        binding.tvSureDelete.singleClick {
            delete?.invoke()
            dismiss()
        }
        binding.ivClose.singleClick {
            dismiss()
        }
    }
}