package com.snails.module.setting.dialog

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Build
import android.widget.ImageView
import androidx.fragment.app.Fragment
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.engine.CropFileEngine
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.snails.base.dialog.BaseBottomDialog
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.utils.PermissionCheckUtils
import com.snails.module.base.bean.PermissionScene
import com.snails.module.base.dialog.PermissionDescDialog
import com.snails.module.setting.databinding.DialogTakePhotoTeacherBinding
import com.snails.module.setting.utils.GlideEngine
import com.snails.module.setting.utils.ImageLoaderUtils
import com.snails.module.setting.utils.SandboxFileEngine
import com.yalantis.ucrop.UCrop
import com.yalantis.ucrop.UCropImageEngine

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月27日 19:34:47
 */
class TakePhotoTeacherBottomDialog :
    BaseBottomDialog<DialogTakePhotoTeacherBinding>(heightDimen = 200) {

    var takePhotoListener: TakePhotoListener? = null

    override fun initClick() {
        super.initClick()
        binding.apply {
            tvTakePhotoTitle.singleClick {
                val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    Permission.READ_MEDIA_IMAGES
                } else {
                    Permission.READ_EXTERNAL_STORAGE
                }
                requestPermission(permissions) {
                    startGallery()
                }
            }
            tvTakePhoto.singleClick {
                requestPermission(Permission.CAMERA) {
                    startTakePic()
                }
            }
            tvCancel.singleClick {
                dismiss()
            }
        }
    }

    /**
     * 请求权限
     */
    private fun requestPermission(permissionName: String, callback: () -> Unit) {
        context?.let { ctx ->
            val havaPermission = PermissionCheckUtils.havaPermission(ctx, permissionName)
            if (havaPermission) {
                <EMAIL>()
                callback.invoke()
            } else {
                val scene = if (
                    permissionName == Permission.CAMERA
                ) {
                    PermissionScene.CAMERA
                } else {
                    PermissionScene.PHOTO
                }
                PermissionDescDialog(scene) {
                    XXPermissions.with(ctx).permission(permissionName).request { _, allGranted ->
                        if (allGranted) {
                            callback.invoke()
                            <EMAIL>()
                        }
                    }
                }.show(childFragmentManager, "")
            }
        }
    }

    private fun startGallery() {
        val act = activity ?: return
        PictureSelector.create(act).openGallery(SelectMimeType.ofImage())
            .setImageEngine(GlideEngine.createGlideEngine()).isPreviewImage(false)
            .isDisplayCamera(false).setMaxSelectNum(1).setMinSelectNum(1)
            .setSelectMaxFileSize(20 * 1024).setCropEngine(ImageFileCropEngine())
            .setSelectMinFileSize(1).setSandboxFileEngine(SandboxFileEngine())
            .forResult(object : OnResultCallbackListener<LocalMedia> {
                override fun onResult(result: ArrayList<LocalMedia>?) {
                    result?.firstOrNull()?.sandboxPath?.let {
                        takePhotoListener?.photo(it)
                    }
                }

                override fun onCancel() {
                }
            })
    }

    /**
     *   调用系统相机
     */
    private fun startTakePic() {
        val act = activity ?: return
        PictureSelector.create(act).openCamera(SelectMimeType.ofImage())
            .setSelectMaxFileSize(20 * 1024).setSelectMinFileSize(1)
            .setSandboxFileEngine(SandboxFileEngine()).setCropEngine(ImageFileCropEngine())
            .forResult(object : OnResultCallbackListener<LocalMedia> {
                override fun onResult(result: ArrayList<LocalMedia>?) {
                    result?.firstOrNull()?.sandboxPath?.let {
                        takePhotoListener?.photo(it)
                    }
                }

                override fun onCancel() {
                }
            })
    }

    /**
     * 自定义裁剪
     */
    private class ImageFileCropEngine : CropFileEngine {
        override fun onStartCrop(
            fragment: Fragment,
            srcUri: Uri?,
            destinationUri: Uri?,
            dataSource: ArrayList<String?>?,
            requestCode: Int
        ) {
            val options: UCrop.Options = buildOptions()
            val uCrop = UCrop.of(srcUri!!, destinationUri!!, dataSource)
            uCrop.withOptions(options)
            uCrop.setImageEngine(object : UCropImageEngine {
                override fun loadImage(context: Context, url: String, imageView: ImageView) {
                    if (!ImageLoaderUtils.assertValidRequest(context)) {
                        return
                    }
                    Glide.with(context).load(url).override(180, 180).into(imageView)
                }

                override fun loadImage(
                    context: Context,
                    url: Uri,
                    maxWidth: Int,
                    maxHeight: Int,
                    call: UCropImageEngine.OnCallbackListener<Bitmap>
                ) {
                    Glide.with(context).asBitmap().load(url).override(maxWidth, maxHeight)
                        .into(object : CustomTarget<Bitmap?>() {
                            override fun onResourceReady(
                                resource: Bitmap, transition: Transition<in Bitmap?>?
                            ) {
                                call.onCall(resource)
                            }

                            override fun onLoadCleared(placeholder: Drawable?) {
                                call.onCall(null)
                            }
                        })
                }
            })
            uCrop.start(fragment.requireActivity(), fragment, requestCode)
        }

        /**
         * 配制UCrop，可根据需求自我扩展
         *
         * @return
         */
        private fun buildOptions(): UCrop.Options {
            val options = UCrop.Options()
                .apply {
                    setShowCropGrid(false) //显示网格
                    withAspectRatio(1f, 1f)
                    setCircleDimmedLayer(true) //圆形
                }
            return options
        }
    }

    interface TakePhotoListener {
        fun photo(path: String)
    }
}