package com.snails.module.setting.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.setting.MessageNotifyInfo
import com.snails.common.widget.MyDividerItemDecoration
import com.snails.module.setting.R
import com.snails.module.setting.viewbinder.MessageItemViewBinder

/**
 * @Description 消息通知
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */
class MessageNotifyListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val messageList = mutableListOf<MessageNotifyInfo>()
    private val listAdapter = MultiTypeAdapter()
    var listener: ItemClickListener? = null

    init {
        initView()
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(MessageNotifyInfo::class.java, MessageItemViewBinder {
                listener?.itemClick(it)
            })
        }
        adapter = listAdapter
        addItemDecoration(ItemDecoration())
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setList(tabList: List<MessageNotifyInfo>, clean: Boolean) {
        if (clean) {
            messageList.clear()
        }
        messageList.addAll(tabList)
        listAdapter.items = messageList
        listAdapter.notifyDataSetChanged()
    }


    interface ItemClickListener {
        fun itemClick(data: MessageNotifyInfo)
    }

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State,
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            kotlin.runCatching {
                val position = parent.getChildAdapterPosition(view)
                // 判断是否为最后一个数据项
                if (position == 0) {
                    outRect.top = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_12)
                } else {
                    outRect.top = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_16)
                }
            }
        }
    }
}