package com.snails.module.setting.ui

import androidx.fragment.app.viewModels
import androidx.navigation.Navigation
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.visible
import com.snails.module.base.BaseStateFragment
import com.snails.module.base.bean.StateType
import com.snails.module.setting.databinding.FragmentMessageNotificationBinding
import com.snails.module.setting.viewmodel.SettingViewModel

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年12月19日 14:05:12
 */
class MessageNotificationFragment : BaseStateFragment<FragmentMessageNotificationBinding>() {

    private val settingViewModel: SettingViewModel by viewModels()

    override fun createViewModel() = settingViewModel

    override fun initData() {
        super.initData()
        settingViewModel.getMessageList(stateType = StateType.PAGE)
    }

    override fun initObserve() {
        super.initObserve()
        settingViewModel.msgNotifyListLiveData.observe(viewLifecycleOwner) { info ->
            if (settingViewModel.messagePage == 1 && info.items.isNullOrEmpty()) {
                binding.refreshLayout.gone()
                binding.flyContainer.visible()
            } else {
                info.items?.let {
                    binding.messageNotifyListView.setList(it, settingViewModel.messagePage == 1)
                }
                binding.refreshLayout.visible()
                binding.flyContainer.gone()
                if ((info.items?.size ?: 0) < 10) {
                    binding.refreshLayout.setEnableLoadMore(false)
                } else {
                    binding.refreshLayout.setEnableLoadMore(true)
                }
            }
            binding.refreshLayout.finishLoadMore()
            binding.refreshLayout.finishRefresh()
        }
    }

    override fun initClick() {
        super.initClick()
        binding.commonTitleView.setBackClickListener {
            val pop = Navigation.findNavController(it).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }

        binding.refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                settingViewModel.messagePage = 1
                settingViewModel.getMessageList()
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                settingViewModel.messagePage += 1
                settingViewModel.getMessageList()
            }
        })
    }

    override fun onRetry() {
        super.onRetry()
        settingViewModel.getMessageList(stateType = StateType.PAGE)
    }
}