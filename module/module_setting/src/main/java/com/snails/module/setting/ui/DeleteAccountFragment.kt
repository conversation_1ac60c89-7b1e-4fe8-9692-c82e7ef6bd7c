package com.snails.module.setting.ui

import androidx.fragment.app.viewModels
import androidx.navigation.findNavController
import com.blankj.utilcode.util.ActivityUtils
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.audio.AudioPlayerManager
import com.snails.base.network.repository.storage.UserStorage
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.BaseStateFragment
import com.snails.module.setting.databinding.FragmentDeleteAccountBinding
import com.snails.module.setting.dialog.DeleteAccountDialog
import com.snails.module.setting.viewmodel.SettingViewModel

/**
 * @Description 账号注销
 * <AUTHOR>
 * @CreateTime 2024年09月21日 10:53:40
 */
class DeleteAccountFragment : BaseStateFragment<FragmentDeleteAccountBinding>() {

    private val settingViewModel: SettingViewModel by viewModels()

    override fun createViewModel() = settingViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initClick() {
        //返回
        binding.commonTitleView.setBackClickListener {
            val pop = it.findNavController().popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }

        binding.tvSureDelete.singleClick {
            DeleteAccountDialog {
                settingViewModel.deleteAccount {
                    cleanDataAndExitLogin()
                }
            }.show(childFragmentManager, "")
        }
    }

    //清楚数据和退出登录
    private fun cleanDataAndExitLogin() {
        AudioPlayerManager.release()
        //清除 用户、关闭音频服务等 等信息
        UserStorage.me.clean()
        ActivityUtils.finishAllActivities()
        HRouter.navigation(RouterPath.SPLASH_HOME)
    }
}