package com.snails.module.setting.ui

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.Navigation
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.BaseStateFragment
import com.snails.module.setting.R
import com.snails.module.setting.databinding.FragmentHelpFeedbackBinding
import com.snails.module.setting.viewmodel.SettingViewModel

/**
 * @Description 帮助与反馈
 * <AUTHOR>
 * @CreateTime 2024年12月19日 16:22:15
 */
class HelpFeedbackFragment : BaseStateFragment<FragmentHelpFeedbackBinding>() {

    private val settingViewModel: SettingViewModel by activityViewModels()

    override fun createViewModel() = settingViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initData() {
        super.initData()
        settingViewModel.getHelpFeedbackInfo()
    }

    override fun initObserve() {
        super.initObserve()
        settingViewModel.faqListLiveData.observe(viewLifecycleOwner) { info ->
            if (info.questions.isNullOrEmpty()) {
                binding.vFAQ.gone()
                binding.tvFaqTitle.gone()
                binding.faqListView.gone()
            } else {
                binding.vFAQ.visible()
                binding.tvFaqTitle.visible()
                binding.faqListView.visible()
                info.questions?.let {
                    binding.faqListView.setList(it)
                }
            }
        }
    }

    override fun initClick() {
        super.initClick()
        //返回
        binding.commonTitleView.setBackClickListener {
            val pop = Navigation.findNavController(it).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }

        binding.ivContactUs.singleClick {
            Navigation.findNavController(it)
                .navigate(R.id.action_helpFeedbackFragment_to_contactUsFragment, Bundle().apply {
                    putString("phone", settingViewModel.faqListLiveData.value?.phoneNumber ?: "")
                    putString("address", settingViewModel.faqListLiveData.value?.address ?: "")
                })
        }
        binding.ivComplaint.singleClick {
            Navigation.findNavController(it)
                .navigate(R.id.action_helpFeedbackFragment_to_feedbackFragment, Bundle().apply {
                    putString("type", "1")
                })
        }
        binding.ivFeedback.singleClick {
            Navigation.findNavController(it)
                .navigate(R.id.action_helpFeedbackFragment_to_feedbackFragment, Bundle().apply {
                    putString("type", "2")
                })
        }
    }

    override fun onRetry() {
        super.onRetry()
        settingViewModel.getHelpFeedbackInfo()
    }
}