package com.snails.module.setting.viewbinder

import android.os.Bundle
import androidx.navigation.findNavController
import com.snails.base.network.repository.info.setting.QuestionInfo
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.safeNavigate
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.setting.R
import com.snails.module.setting.databinding.LayoutFaqItemBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年12月17日 16:24:23
 */
class FaqItemViewBinder : ViewBindingDelegate<QuestionInfo, LayoutFaqItemBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<LayoutFaqItemBinding>, item: QuestionInfo
    ) {
        holder.binding.apply {
            tvMessageTitle.text = item.title ?: ""
            if (!item.answer.isNullOrEmpty()) {
                holder.itemView.singleClick {
                    it.findNavController().safeNavigate(
                        R.id.action_helpFeedbackFragment_to_faqDetailsFragment, Bundle().apply {
                            putString("title", item.title)
                            putString("data", item.answer)
                        }
                    )
                }
            } else {
                holder.itemView.singleClick {
                    item.url?.let { it1 -> HRouter.navigation(it1) }
                }
            }
        }
    }
}