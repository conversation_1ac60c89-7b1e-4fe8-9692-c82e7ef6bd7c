package com.snails.module.setting.ui

import androidx.navigation.Navigation
import com.gyf.immersionbar.ImmersionBar
import com.snails.module.base.BaseStateFragment
import com.snails.module.setting.databinding.FragmentContactUsBinding

/**
 * @Description 联系我们
 * <AUTHOR>
 * @CreateTime 2024年12月20日 11:47:00
 */
class ContactUsFragment : BaseStateFragment<FragmentContactUsBinding>() {

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initView() {
        super.initView()
        arguments?.getString("phone")?.let {
            binding.tvServicePhone.text = it
        }
        arguments?.getString("address")?.let {
            binding.tvCompanyAddress.text = it
        }
    }

    override fun initClick() {
        super.initClick()
        //返回
        binding.commonTitleView.setBackClickListener {
            val pop = Navigation.findNavController(it).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
    }
}