package com.snails.module.setting.utils

import android.content.Context
import com.luck.picture.lib.engine.UriToFileTransformEngine
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener
import com.luck.picture.lib.utils.SandboxTransformUtils

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年10月15日 22:24:05
 */
class SandboxFileEngine : UriToFileTransformEngine {
    override fun onUriToFileAsyncTransform(
        context: Context?,
        srcPath: String?,
        mineType: String?,
        call: OnKeyValueResultCallbackListener?
    ) {
        if (call != null) {
            val sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType)
            call.onCallback(srcPath, sandboxPath)
        }
    }
}