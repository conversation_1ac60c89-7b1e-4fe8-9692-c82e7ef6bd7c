package com.snails.module.setting.ui

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.navigation.Navigation
import com.blankj.utilcode.util.AppUtils
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.network.ext.PrivacyPolicyConstants
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.BaseStateFragment
import com.snails.module.setting.databinding.FragmentAboutUsBinding

/**
 * @Description 关于我们
 * <AUTHOR>
 * @CreateTime 2024年09月21日 10:53:40
 */
class AboutUsFragment : BaseStateFragment<FragmentAboutUsBinding>() {

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initClick() {
        //返回
        binding.commonTitleView.setBackClickListener {
            val pop = Navigation.findNavController(it).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
        binding.civServiceAgreement.singleClick {
            HRouter.navigation(PrivacyPolicyConstants.USER_SERVICE_AGREEMENT, Bundle().apply {
                putString("title", "用户服务协议")
            })
        }
        binding.civPrivacyAgreement.singleClick {
            HRouter.navigation(PrivacyPolicyConstants.USER_PRIVACY_AGREEMENT, Bundle().apply {
                putString("title", "用户隐私协议")
            })
        }
        binding.civChildrenAgreement.singleClick {
            HRouter.navigation(PrivacyPolicyConstants.CHILDREN_PRIVACY_AGREEMENT, Bundle().apply {
                putString("title", "儿童隐私协议")
            })
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        super.initView()
        binding.tvAppVersion.text = "V${AppUtils.getAppVersionName()}"
    }
}