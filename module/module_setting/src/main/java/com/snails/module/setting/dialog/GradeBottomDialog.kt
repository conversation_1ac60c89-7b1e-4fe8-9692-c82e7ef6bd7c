package com.snails.module.setting.dialog

import com.snails.base.dialog.BaseBottomDialog
import com.snails.base.utils.ext.singleClick
import com.snails.common.widget.WheelView
import com.snails.module.setting.databinding.DialogGradeBinding

/**
 * @Description 年级
 * <AUTHOR>
 * @CreateTime 2024年11月27日 19:34:47
 */
class GradeBottomDialog() : BaseBottomDialog<DialogGradeBinding>(heightDimen = 312) {

    //当前选中的年级
    private var currentSelectGrade: String? = null

    private var grades = mutableListOf<String>()

    private var selected: ((String) -> Unit)? = null

    constructor(selected: (String) -> Unit) : this() {
        this.selected = selected
    }

    override fun initView() {
        super.initView()
        binding.wheelViewGrade.setDataItems(grades)
    }

    fun setGrades(list: List<String>) {
        this.grades.apply {
            clear()
            addAll(list)
        }
    }

    override fun initClick() {
        super.initClick()
        binding.wheelViewGrade.setOnItemSelectedListener(object : WheelView.OnItemSelectedListener {
            override fun onItemSelected(wheelView: WheelView, data: Any, position: Int) {
                currentSelectGrade = data.toString()
            }
        })
        binding.tvCancel.singleClick {
            dismiss()
        }
        binding.tvSure.singleClick {
            currentSelectGrade?.let { it1 -> selected?.invoke(it1) }
            dismiss()
        }
    }
}