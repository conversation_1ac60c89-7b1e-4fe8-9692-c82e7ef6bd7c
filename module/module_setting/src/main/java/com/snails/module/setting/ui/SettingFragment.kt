package com.snails.module.setting.ui

import androidx.fragment.app.viewModels
import androidx.navigation.findNavController
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.PathUtils
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.audio.AudioPlayerManager
import com.snails.base.network.repository.storage.AppSettingStorage
import com.snails.base.network.repository.storage.UserStorage
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.BaseStateFragment
import com.snails.module.setting.R
import com.snails.module.setting.databinding.FragmentSettingBinding
import com.snails.module.setting.dialog.RecordTypeBottomDialog
import com.snails.module.setting.viewmodel.SettingViewModel

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年09月21日 10:53:40
 */
class SettingFragment : BaseStateFragment<FragmentSettingBinding>() {

    private val settingViewModel: SettingViewModel by viewModels()

    override fun createViewModel() = settingViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initData() {
        super.initData()
        settingViewModel.apply {
            if (userSettingInfoLiveData.value == null) {
                getSettingInfoData()
            }
        }
    }

    override fun initClick() {
        //返回
        binding.commonTitleView.setBackClickListener {
            val pop = it.findNavController().popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
        //录像方式
        binding.civRecordType.singleClick {
            RecordTypeBottomDialog {
                binding.civRecordType.setItemRightTitle(getRecordType())
            }.show(childFragmentManager, "")
        }
        //退出登录
        binding.tvExitApp.singleClick {
            cleanDataAndExitLogin()
        }
        //注销账号
        binding.civDeleteAccount.singleClick {
            it.findNavController()
                .navigate(R.id.action_settingFragment_to_deleteAccountFragment)
        }
        //清除缓存
        binding.civCleanCache.singleClick {
            FileUtils.delete(PathUtils.getInternalAppCachePath())
            val size = FileUtils.getSize(PathUtils.getInternalAppCachePath())
            binding.civCleanCache.setItemRightTitle(size)
        }
        //日志上报
        binding.civUploadLog.singleClick {
            settingViewModel.uploadGameLog()
        }
        //关于我们
        binding.civAboutUs.singleClick {
            it.findNavController()
                .navigate(R.id.action_settingFragment_to_aboutUsFragment)
        }
        //个人信息收集清单
        binding.civCollectionPersonalInfo.singleClick {
            settingViewModel.userSettingInfoLiveData.value?.personalDataChecklistUrl?.let {
                HRouter.navigation(it)
            }
        }
        //第三方信息共享清单
        binding.civCollectionThirdInfo.singleClick {
            settingViewModel.userSettingInfoLiveData.value?.thirdPartyDataChecklistUrl?.let {
                HRouter.navigation(it)
            }
        }
    }

    override fun initView() {
        super.initView()
        val size = FileUtils.getSize(PathUtils.getInternalAppCachePath())
        binding.civCleanCache.setItemRightTitle(size)
        binding.civRecordType.setItemRightTitle(getRecordType())
    }

    //清楚数据和退出登录
    private fun cleanDataAndExitLogin() {
        AudioPlayerManager.release()
        //清除 用户、关闭音频服务等 等信息
        UserStorage.me.clean()
        ActivityUtils.finishAllActivities()
        HRouter.navigation(RouterPath.SPLASH_HOME)
    }

    private fun getRecordType(): String {
        val type = AppSettingStorage.me.getReallyVideoRecordType()
        return if (type == "DEFAULT") {
            "默认"
        } else {
            "原生"
        }
    }
}