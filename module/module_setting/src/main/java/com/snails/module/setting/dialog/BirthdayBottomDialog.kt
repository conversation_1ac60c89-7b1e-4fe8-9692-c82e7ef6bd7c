package com.snails.module.setting.dialog

import com.snails.base.dialog.BaseBottomDialog
import com.snails.base.utils.ext.singleClick
import com.snails.common.widget.WheelView
import com.snails.module.setting.databinding.DialogBirthdayBinding
import java.time.YearMonth
import java.util.Calendar

/**
 * @Description 生日弹窗
 * <AUTHOR>
 * @CreateTime 2024年11月27日 19:34:47
 */
class BirthdayBottomDialog() :
    BaseBottomDialog<DialogBirthdayBinding>(heightDimen = 332) {

    private var selected: ((String) -> Unit)? = null

    constructor(selected: (String) -> Unit) : this() {
        this.selected = selected
    }

    //当前选中的年月日
    private var currentSelectYear: String? = null
    private var currentSelectMonth: String? = null
    private var currentSelectDay: String? = null

    private val currentYear = Calendar.getInstance().get(Calendar.YEAR)
    private val currentMonth = Calendar.getInstance().get(Calendar.MONTH) + 1
    private val currentDay = Calendar.getInstance().get(Calendar.DAY_OF_MONTH)

    override fun initData() {
        super.initData()
        binding.wheelViewYear.setDataItems(generateYearList().toMutableList())
        binding.wheelViewMonth.setDataItems(generateMonthList(currentMonth).toMutableList())
        binding.wheelViewDay.setDataItems(generateDayList(currentDay).toMutableList())
    }

    override fun initClick() {
        super.initClick()
        //年
        binding.wheelViewYear.setOnItemSelectedListener(object : WheelView.OnItemSelectedListener {
            override fun onItemSelected(wheelView: WheelView, data: Any, position: Int) {
                currentSelectYear = data.toString().split("年")[0]
                //当前年发生变化时，动态设置月份和天数
                if (currentSelectYear == currentYear.toString()) {
                    binding.wheelViewMonth.setDataItems(generateMonthList(currentMonth).toMutableList())
                    if (currentSelectMonth == currentMonth.toString().padStart(2, '0')) {
                        binding.wheelViewDay.setDataItems(generateDayList(currentDay).toMutableList())
                    } else {
                        binding.wheelViewDay.setDataItems(generateDayList().toMutableList())
                    }
                } else {
                    binding.wheelViewMonth.setDataItems(generateMonthList(12).toMutableList())
                    binding.wheelViewDay.setDataItems(generateDayList().toMutableList())
                }
            }
        })
        //月
        binding.wheelViewMonth.setOnItemSelectedListener(object : WheelView.OnItemSelectedListener {
            override fun onItemSelected(wheelView: WheelView, data: Any, position: Int) {
                currentSelectMonth = data.toString().split("月")[0]
                //当前月发生变化时，动态设置天数
                if (currentSelectMonth == currentMonth.toString().padStart(2, '0')) {
                    binding.wheelViewDay.setDataItems(generateDayList(currentDay).toMutableList())
                } else {
                    binding.wheelViewDay.setDataItems(generateDayList().toMutableList())
                }
            }
        })
        //日
        binding.wheelViewDay.setOnItemSelectedListener(object : WheelView.OnItemSelectedListener {
            override fun onItemSelected(wheelView: WheelView, data: Any, position: Int) {
                currentSelectDay = data.toString().split("日")[0]
            }
        })
        binding.tvCancel.singleClick {
            dismiss()
        }
        binding.tvSure.singleClick {
            selected?.invoke("$currentSelectYear-$currentSelectMonth-$currentSelectDay")
            dismiss()
        }
    }

    private fun generateYearList(): List<String> {
        return (1990..currentYear).reversed().map { "${it}年" }
    }

    private fun generateMonthList(maxMonth: Int): List<String> {
        return (1..maxMonth).map { month -> "${month.toString().padStart(2, '0')}月" }
    }

    fun generateDayList(maxDay: Int? = null): List<String> {
        if (maxDay != null) {
            return (1..maxDay).map { day -> "${day.toString().padStart(2, '0')}日" }
        }
        val year = currentSelectYear?.toInt() ?: Calendar.getInstance().get(Calendar.YEAR)
        val month = currentSelectMonth?.toInt() ?: 1
        val daysInMonth = YearMonth.of(year, month).lengthOfMonth()
        return (1..daysInMonth).map { day -> "${day.toString().padStart(2, '0')}日" }
    }
}