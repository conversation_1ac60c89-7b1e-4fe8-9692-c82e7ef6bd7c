package com.snails.module.setting.dialog

import com.snails.base.dialog.BaseBottomDialog
import com.snails.base.utils.ext.singleClick
import com.snails.common.widget.WheelView
import com.snails.module.setting.databinding.DialogGenderBinding

/**
 * @Description 性别
 * <AUTHOR>
 * @CreateTime 2024年11月27日 19:34:47
 */
class GenderBottomDialog() : BaseBottomDialog<DialogGenderBinding>(heightDimen = 268) {

    private var selectedGender: String? = null
    private var selected: ((String) -> Unit)? = null

    constructor(selected: (String) -> Unit) : this() {
        this.selected = selected
    }

    override fun initData() {
        super.initData()
        binding.wheelViewGender.setDataItems(mutableListOf<String>().apply {
            add("男")
            add("女")
        })
    }

    override fun initClick() {
        super.initClick()
        binding.wheelViewGender.setOnItemSelectedListener(object :
            WheelView.OnItemSelectedListener {
            override fun onItemSelected(wheelView: WheelView, data: Any, position: Int) {
                selectedGender = data.toString()
            }
        })
        binding.tvCancel.singleClick {
            dismiss()
        }
        binding.tvSure.singleClick {
            selectedGender?.let {
                selected?.invoke(it)
                dismiss()
            }
        }
    }
}