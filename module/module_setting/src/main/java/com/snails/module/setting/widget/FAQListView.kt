package com.snails.module.setting.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.setting.QuestionInfo
import com.snails.module.setting.R
import com.snails.module.setting.viewbinder.FaqItemViewBinder

/**
 * @Description 消息通知
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */
class FAQListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val listAdapter = MultiTypeAdapter()

    init {
        initView()
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(QuestionInfo::class.java, FaqItemViewBinder())
        }
        adapter = listAdapter
        addItemDecoration(ItemDecoration())
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setList(tabList: List<QuestionInfo>) {
        listAdapter.items = tabList
        listAdapter.notifyDataSetChanged()
    }

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State,
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_20)
        }
    }
}