package com.snails.module.setting.viewbinder

import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.setting.MessageNotifyInfo
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.setting.R
import com.snails.module.setting.databinding.LayoutMessageItemBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年12月17日 16:24:23
 */
class MessageItemViewBinder(private val itemClick: (MessageNotifyInfo) -> Unit) :
    ViewBindingDelegate<MessageNotifyInfo, LayoutMessageItemBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<LayoutMessageItemBinding>,
        item: MessageNotifyInfo
    ) {
        holder.binding.apply {
            if (item.cover.isNullOrEmpty()) {
                sivMessagePic.gone()
            } else {
                sivMessagePic.apply {
                    load(
                        item.cover,
                        placeholder = R.drawable.svg_w106_h106_placeholder,
                        error = R.drawable.svg_w106_h106_placeholder
                    )
                    visible()
                }
            }
            tvMessageTitle.text = item.title ?: ""
            tvMessageContent.text = item.content ?: ""
        }
        holder.itemView.singleClick {
            if (!item.route.isNullOrEmpty()) {
                item.route?.let {
                    HRouter.navigation(it)
                }
            }
        }
    }
}