package com.snails.module.setting.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.module.setting.R
import com.snails.module.setting.viewbinder.CrashLogItemViewBinder
import java.io.File

/**
 * @Description 崩溃日志列表
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */
class CrashLogListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    var itemClickListener: ItemClickListener? = null
    private val listAdapter = MultiTypeAdapter()

    init {
        initView()
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(File::class.java, CrashLogItemViewBinder {
                itemClickListener?.itemClick(it)
            })
        }
        adapter = listAdapter
        addItemDecoration(ItemDecoration())
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setList(tabList: List<File>) {
        listAdapter.items = tabList
        listAdapter.notifyDataSetChanged()
    }

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State,
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_20)
        }
    }

    interface ItemClickListener {
        fun itemClick(file: File)
    }
}