package com.snails.module.setting.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ScreenUtils
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.module.setting.R
import com.snails.module.setting.viewbinder.UploadPicItemViewBinder

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:40:49
 */
class UploadPicListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val picList = mutableListOf<String>()
    private val listAdapter = MultiTypeAdapter()
    var listener: ItemClickListener? = null

    init {
        initView()
    }

    private fun initView() {
        val itemWith =
            (ScreenUtils.getScreenWidth() - resources.getDimension(R.dimen.base_sw_dp_56)) / 4
        // 列表项
        listAdapter.apply {
            register(
                String::class.java,
                UploadPicItemViewBinder(itemWith.toInt(),
                    itemClick = { path ->
                        listener?.itemClick(path)
                    }, delete = { pos ->
                        listener?.delete(pos)
                    }
                )
            )
        }
        adapter = listAdapter
        addItemDecoration(ItemDecoration())
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setList(tabList: List<String>) {
        picList.addAll(0, tabList)
        if (picList.size > 6) {
            picList.remove("+")
        } else if (picList.size < 6 && !picList.contains("+")) {
            picList.add("+")
        }
        listAdapter.items = picList
        listAdapter.notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun removeItem(pos: Int) {
        if (pos < 0 || pos >= picList.size) {
            return
        }
        kotlin.runCatching {
            picList.removeAt(pos)
            listAdapter.items = picList
            if (picList.size < 6 && !picList.contains("+")) {
                picList.add("+")
            }
            listAdapter.notifyDataSetChanged()
        }
    }

    fun getItemList() = picList

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State,
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8)
        }
    }

    interface ItemClickListener {
        fun itemClick(data: String)
        fun delete(pos: Int)
    }
}