<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:commTitle="@string/str_faq" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFaqTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvResult"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_medium" />
</LinearLayout>