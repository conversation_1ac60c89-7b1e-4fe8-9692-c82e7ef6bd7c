<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_sw_dp_312"
    android:layout_gravity="bottom">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCancel"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_80"
        android:background="@color/surface_background_white"
        android:gravity="center"
        android:text="@string/str_cancel"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/vSpaceTwo"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_1"
        android:background="@color/border_divider"
        app:layout_constraintBottom_toTopOf="@+id/tvCancel" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTakePhoto"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_44"
        android:background="@color/surface_background_white"
        android:gravity="center"
        android:text="@string/str_take_photo_head"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintBottom_toTopOf="@+id/vSpaceTwo" />

    <View
        android:id="@+id/vSpaceThree"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_1"
        android:background="@color/border_divider"
        app:layout_constraintBottom_toTopOf="@+id/tvTakePhoto" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTakePhotoTitle"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_56"
        android:background="@drawable/shape_top20r_sbw_bg"
        android:gravity="center"
        android:text="@string/str_take_photo_from_phone"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintBottom_toTopOf="@+id/vSpaceThree" />
</androidx.constraintlayout.widget.ConstraintLayout>