<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_sw_dp_332"
    android:layout_gravity="bottom">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:text="@string/str_cancel"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_13"
        android:text="@string/str_baby_birthday"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSure"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:text="@string/str_sure"
        android:textColor="@color/text_link"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <com.snails.common.widget.WheelView
        android:id="@+id/wheelViewYear"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_15"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/wheelViewMonth"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:wv_autoFitTextSize="true"
        app:wv_curvedArcDirection="center"
        app:wv_cyclic="false"
        app:wv_dividerHeight="0dp"
        app:wv_drawSelectedRect="true"
        app:wv_lineSpacing="@dimen/base_sw_dp_54"
        app:wv_normalItemTextColor="#BEBEBE"
        app:wv_selectedItemPosition="0"
        app:wv_selectedItemTextColor="#000000"
        app:wv_selectedRectColor="@color/surface_background"
        app:wv_selectedRectLeftRadius="@dimen/base_sw_dp_12"
        app:wv_showDivider="false"
        app:wv_textAlign="left"
        app:wv_textBoundaryMargin="@dimen/base_sw_dp_10"
        app:wv_textSize="@dimen/headline_h4"
        app:wv_visibleItems="9" />

    <com.snails.common.widget.WheelView
        android:id="@+id/wheelViewMonth"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/base_sw_dp_15"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/wheelViewDay"
        app:layout_constraintStart_toEndOf="@+id/wheelViewYear"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:wv_autoFitTextSize="true"
        app:wv_curvedArcDirection="center"
        app:wv_cyclic="false"
        app:wv_dividerHeight="0dp"
        app:wv_drawSelectedRect="true"
        app:wv_lineSpacing="@dimen/base_sw_dp_54"
        app:wv_normalItemTextColor="#BEBEBE"
        app:wv_selectedItemPosition="0"
        app:wv_selectedItemTextColor="#000000"
        app:wv_selectedRectColor="@color/surface_background"
        app:wv_showDivider="false"
        app:wv_textSize="@dimen/headline_h4"
        app:wv_visibleItems="9" />

    <com.snails.common.widget.WheelView
        android:id="@+id/wheelViewDay"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/base_sw_dp_15"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/wheelViewMonth"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:wv_autoFitTextSize="true"
        app:wv_curvedArcDirection="center"
        app:wv_cyclic="false"
        app:wv_dividerHeight="0dp"
        app:wv_drawSelectedRect="true"
        app:wv_lineSpacing="@dimen/base_sw_dp_54"
        app:wv_normalItemTextColor="#BEBEBE"
        app:wv_selectedItemPosition="0"
        app:wv_selectedItemTextColor="#000000"
        app:wv_selectedRectColor="@color/surface_background"
        app:wv_selectedRectRightRadius="@dimen/base_sw_dp_12"
        app:wv_showDivider="false"
        app:wv_textAlign="right"
        app:wv_textBoundaryMargin="@dimen/base_sw_dp_10"
        app:wv_textSize="@dimen/headline_h4"
        app:wv_visibleItems="9" />

</androidx.constraintlayout.widget.ConstraintLayout>