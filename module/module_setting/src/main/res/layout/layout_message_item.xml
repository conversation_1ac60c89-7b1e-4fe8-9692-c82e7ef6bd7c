<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clyContainer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_sbw_12r_bg"
    android:paddingBottom="@dimen/base_sw_dp_12">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivMessagePic"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_114"
        android:scaleType="centerCrop"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/RoundedTop12Style" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvMessageTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:drawableEnd="@drawable/svg_arrow_right"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sivMessagePic"
        tools:text="消息提醒链接" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvMessageContent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvMessageTitle"
        tools:text="你有一个消息吧啦吧啦吧" />

</androidx.constraintlayout.widget.ConstraintLayout>