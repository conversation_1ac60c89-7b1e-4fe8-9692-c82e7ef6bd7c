<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:commTitle="@string/str_about_us"
        app:commTitleBg="#00000000"
        app:commTitleColor="@color/text_headline"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivSlogan"
        android:layout_width="@dimen/base_sw_dp_160"
        android:layout_height="@dimen/base_sw_dp_160"
        android:layout_marginTop="@dimen/base_sw_dp_97"
        android:background="@drawable/img_logo_slogan"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAppVersion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_4"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_medium"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivSlogan"
        tools:text="V1.0.0" />

    <View
        android:id="@+id/vOneBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_48"
        android:background="@drawable/shape_sbw_12r_bg"
        app:layout_constraintBottom_toBottomOf="@+id/civChildrenAgreement"
        app:layout_constraintTop_toBottomOf="@+id/tvAppVersion" />

    <com.snails.module.base.widget.CommonItemView
        android:id="@+id/civServiceAgreement"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        app:itemTitle="@string/str_user_service_agreement_title"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toTopOf="@+id/vOneBg" />

    <com.snails.module.base.widget.CommonItemView
        android:id="@+id/civPrivacyAgreement"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        app:itemTitle="@string/str_user_privacy_agreement_title"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toBottomOf="@+id/civServiceAgreement" />

    <com.snails.module.base.widget.CommonItemView
        android:id="@+id/civChildrenAgreement"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        app:itemTitle="@string/str_children_privacy_agreement_title"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toBottomOf="@+id/civPrivacyAgreement" />

</androidx.constraintlayout.widget.ConstraintLayout>