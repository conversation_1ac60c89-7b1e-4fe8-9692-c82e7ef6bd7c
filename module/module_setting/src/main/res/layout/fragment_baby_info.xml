<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:commTitle="@string/str_setting"
        app:commTitleBg="#00000000"
        app:commTitleColor="@color/text_headline"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivBabyHead"
        android:layout_width="@dimen/base_sw_dp_96"
        android:layout_height="@dimen/base_sw_dp_96"
        android:layout_marginTop="@dimen/base_sw_dp_40"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView"
        app:shapeAppearance="@style/CircleStyle" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivTakePhotoLabel"
        android:layout_width="@dimen/base_sw_dp_32"
        android:layout_height="@dimen/base_sw_dp_32"
        android:layout_marginTop="@dimen/base_sw_dp_40"
        android:background="@drawable/svg_camera_bw"
        app:layout_constraintBottom_toBottomOf="@+id/sivBabyHead"
        app:layout_constraintEnd_toEndOf="@+id/sivBabyHead" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_40"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:background="@drawable/shape_sbw_16r_bg"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sivBabyHead">

        <com.snails.module.base.widget.CommonItemView
            android:id="@+id/civBabyName"
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_64"
            android:layout_marginStart="@dimen/base_sw_dp_10"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            app:itemTitle="@string/str_baby_name" />

        <com.snails.module.base.widget.CommonItemView
            android:id="@+id/civBabyGender"
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_64"
            android:layout_marginStart="@dimen/base_sw_dp_10"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            app:itemTitle="@string/str_baby_sex" />

        <com.snails.module.base.widget.CommonItemView
            android:id="@+id/civBabyBirthday"
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_64"
            android:layout_marginStart="@dimen/base_sw_dp_10"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            app:itemTitle="@string/str_baby_birthday" />

        <com.snails.module.base.widget.CommonItemView
            android:id="@+id/civBabyStudyAge"
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_64"
            android:layout_marginStart="@dimen/base_sw_dp_10"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            app:itemTitle="@string/str_baby_study_age" />

        <com.snails.module.base.widget.CommonItemView
            android:id="@+id/civBabyLocation"
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_64"
            android:layout_marginStart="@dimen/base_sw_dp_10"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            app:itemTitle="@string/str_baby_location" />
    </androidx.appcompat.widget.LinearLayoutCompat>


    <View
        android:id="@+id/vEnvironmentSetting"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_120"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>