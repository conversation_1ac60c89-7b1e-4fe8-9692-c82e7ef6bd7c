<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/vTopSpace"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_44"
        android:background="@color/surface_background_white" />

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:commTitle="@string/str_message_notification"
        app:commTitleBg="@color/surface_background_white"
        app:commTitleColor="@color/text_headline" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:visibility="gone">

        <com.snails.module.base.widget.refresh.CommonRefresh
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.snails.module.setting.widget.MessageNotifyListView
            android:id="@+id/messageNotifyListView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <com.snails.module.base.widget.refresh.CommonRefresh
            android:id="@+id/footer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <LinearLayout
        android:id="@+id/flyContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivError"
            android:layout_width="@dimen/base_sw_dp_200"
            android:layout_height="@dimen/base_sw_dp_160"
            android:background="@drawable/svg_common_no_data"
            app:layout_constraintBottom_toTopOf="@+id/tvErrorMsg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/commonTitleView"
            app:layout_constraintVertical_chainStyle="packed" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvErrorMsg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_12"
            android:gravity="center"
            android:text="@string/str_message_empty"
            android:textColor="@color/text_body"
            android:textSize="@dimen/text_body_medium"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivError" />
    </LinearLayout>
</LinearLayout>