<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_sw_dp_378"
    android:layout_gravity="bottom">

    <TextView
        android:id="@+id/tvDialogTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_15"
        android:gravity="center"
        android:text="@string/str_sure_delete_account_tips"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_marginEnd="@dimen/base_sw_dp_10"
        android:background="@drawable/svg_close_24"
        app:layout_constraintBottom_toBottomOf="@+id/tvDialogTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvDialogTitle" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPointOne"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_23"
        android:background="@drawable/svg_black_point"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivClose" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTipsOne"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:text="@string/str_tips_desc_one"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivPointOne"
        app:layout_constraintTop_toTopOf="@+id/ivPointOne" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPointTwo"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:background="@drawable/svg_black_point"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTipsOne" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTipsTwo"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:text="@string/str_tips_desc_two"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivPointTwo"
        app:layout_constraintTop_toTopOf="@+id/ivPointTwo" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPointThree"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:background="@drawable/svg_black_point"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTipsTwo" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTipsThree"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:text="@string/str_tips_desc_three"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivPointThree"
        app:layout_constraintTop_toTopOf="@+id/ivPointThree" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSureDelete"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:background="@drawable/shape_spb_32r_bg"
        android:gravity="center"
        android:text="@string/str_sure_delete_account"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTipsThree" />
</androidx.constraintlayout.widget.ConstraintLayout>