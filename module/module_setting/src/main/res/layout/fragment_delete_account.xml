<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:commTitle="@string/str_delete_account"
        app:commTitleBg="#********"
        app:commTitleColor="@color/text_headline"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivTips"
        android:layout_width="@dimen/base_sw_dp_72"
        android:layout_height="@dimen/base_sw_dp_72"
        android:layout_marginTop="@dimen/base_sw_dp_32"
        android:background="@drawable/svg_waring"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_32"
        android:gravity="center"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivTips" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDesc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_5"
        android:gravity="center"
        android:text="@string/str_delete_tips_desc"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <View
        android:id="@+id/vOneBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_32"
        android:background="@drawable/shape_sbw_12r_bg"
        app:layout_constraintBottom_toBottomOf="@+id/vSpace"
        app:layout_constraintTop_toBottomOf="@+id/tvDesc" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTipsTitleOne"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:text="@string/str_tips_title_one"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toTopOf="@+id/vOneBg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTipsDescOne"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:text="@string/str_tips_desc_one"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toBottomOf="@+id/tvTipsTitleOne" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTipsTitleTwo"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:text="@string/str_tips_title_two"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toBottomOf="@+id/tvTipsDescOne" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTipsDescTwo"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:text="@string/str_tips_desc_two"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toBottomOf="@+id/tvTipsTitleTwo" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTipsTitleThree"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:text="@string/str_tips_title_three"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toBottomOf="@+id/tvTipsDescTwo" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTipsDescThree"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:text="@string/str_tips_desc_three"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toBottomOf="@+id/tvTipsTitleThree" />

    <View
        android:id="@+id/vSpace"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_16"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toBottomOf="@+id/tvTipsDescThree" />

    <View
        android:id="@+id/vBottomBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_102"
        android:background="@color/surface_background_white"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSureDelete"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:background="@drawable/shape_spb_32r_bg"
        android:gravity="center"
        android:text="@string/str_sure_delete_account"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/vBottomBg" />
</androidx.constraintlayout.widget.ConstraintLayout>