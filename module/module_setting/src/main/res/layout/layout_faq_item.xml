<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_sw_dp_44"
    android:layout_marginHorizontal="@dimen/base_sw_dp_16"
    android:background="@drawable/shape_sbw_13r_bg">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvMessageTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:paddingStart="@dimen/base_sw_dp_16"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_medium"
        tools:ignore="RtlSymmetry"
        tools:text="消息提醒链接" />
</FrameLayout>
