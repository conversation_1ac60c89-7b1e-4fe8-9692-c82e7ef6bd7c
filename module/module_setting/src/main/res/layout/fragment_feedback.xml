<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/surface_background_white"
    android:fitsSystemWindows="true">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:commTitleBg="#00000000"
        app:commTitleColor="@color/text_headline"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvReasonTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView"
        tools:text="投诉原因" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvReason"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:drawableEnd="@drawable/svg_arrow_right"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintBottom_toBottomOf="@+id/tvReasonTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvReasonTitle"
        app:layout_constraintTop_toTopOf="@+id/tvReasonTitle"
        tools:text="投诉原因投诉原因投原因" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPhoneTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:text="@string/str_phone_number_txt"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvReasonTitle" />

    <View
        android:id="@+id/vPhoneBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:background="@drawable/shape_sb_12r_bg"
        app:layout_constraintTop_toBottomOf="@+id/tvPhoneTitle" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etPhone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:background="@null"
        android:hint="@string/str_phone_number_hint_text"
        android:inputType="phone"
        android:maxLength="20"
        android:textColor="@color/text_describe"
        android:textColorHint="@color/text_disable"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintBottom_toBottomOf="@+id/vPhoneBg"
        app:layout_constraintEnd_toEndOf="@+id/vPhoneBg"
        app:layout_constraintStart_toStartOf="@+id/vPhoneBg"
        app:layout_constraintTop_toTopOf="@+id/vPhoneBg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDescribeTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:text="@string/str_describe_txt"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vPhoneBg" />

    <FrameLayout
        android:id="@+id/flyContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:background="@drawable/shape_sb_12r_bg"
        android:minHeight="@dimen/base_sw_dp_160"
        app:layout_constraintTop_toBottomOf="@+id/tvDescribeTitle">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/base_sw_dp_12"
            android:background="@null"
            android:gravity="top|start"
            android:maxLength="300"
            android:minHeight="@dimen/base_sw_dp_160"
            android:textColor="@color/text_describe"
            android:textColorHint="@color/text_disable"
            android:textSize="@dimen/text_body_small" />
    </FrameLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUploadPicTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:text="@string/str_upload_pic_txt"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/flyContainer" />

    <com.snails.module.setting.widget.UploadPicListView
        android:id="@+id/uploadPicListView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintTop_toBottomOf="@+id/tvUploadPicTitle"
        app:spanCount="4" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCommit"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:layout_marginBottom="@dimen/base_sw_dp_42"
        android:background="@drawable/shape_spb_32r_bg"
        android:gravity="center"
        android:text="@string/str_commit_text"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>