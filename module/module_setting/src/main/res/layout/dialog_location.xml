<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_sw_dp_673"
    android:layout_gravity="bottom">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:text="@string/str_cancel"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_13"
        android:text="@string/str_choose_location"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vTitleBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_42"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:background="@color/surface_background"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvProvince"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/base_sw_dp_16"
        android:paddingEnd="@dimen/base_sw_dp_12"
        android:text="@string/str_province"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        app:layout_constraintBottom_toBottomOf="@+id/vTitleBg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/vTitleBg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_city"
        android:textColor="@color/text_disable"
        android:textSize="@dimen/text_body_large"
        app:layout_constraintBottom_toBottomOf="@+id/vTitleBg"
        app:layout_constraintStart_toEndOf="@+id/tvProvince"
        app:layout_constraintTop_toTopOf="@+id/vTitleBg" />

    <com.snails.module.setting.widget.ProvinceListView
        android:id="@+id/provinceListView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vTitleBg" />

    <com.snails.module.setting.widget.CityListView
        android:id="@+id/cityListView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:orientation="vertical"
        android:visibility="gone"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vTitleBg" />

</androidx.constraintlayout.widget.ConstraintLayout>