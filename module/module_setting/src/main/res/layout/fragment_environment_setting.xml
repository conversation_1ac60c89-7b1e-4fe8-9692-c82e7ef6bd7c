<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:commTitle="@string/str_environment_setting"
        app:commTitleBg="#00000000"
        app:commTitleColor="@color/text_headline"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/llyOne"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:background="@drawable/shape_sbw_16r_bg"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView">

        <RadioGroup
            android:id="@+id/rgGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginVertical="@dimen/base_sw_dp_10">

            <RadioButton
                android:id="@+id/rbDev"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="(DEV)开发环境"
                android:textColor="@color/text_headline"
                tools:ignore="HardcodedText" />

            <RadioButton
                android:id="@+id/rbUat"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="(UAT)集成测试环境"
                android:textColor="@color/text_headline"
                tools:ignore="HardcodedText" />

            <RadioButton
                android:id="@+id/rbPre"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="(PRE)预发布环境"
                android:textColor="@color/text_headline"
                tools:ignore="HardcodedText" />

            <RadioButton
                android:id="@+id/rbPro"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="(PROD)线上正式环境"
                android:textColor="@color/text_headline"
                tools:ignore="HardcodedText" />

            <RadioButton
                android:id="@+id/rbGameConfig"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="(Config)开发环境"
                android:textColor="@color/text_headline"
                tools:ignore="HardcodedText" />
        </RadioGroup>
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSure"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="12dp"
        android:background="@drawable/shape_spb_32r_bg"
        android:gravity="center"
        android:text="@string/str_sure"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.514"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/llyOne" />

    <Switch
        android:id="@+id/switchUploadCrashLog"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_10"
        android:text="是否打开自动日志上传"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvSure"
        tools:ignore="HardcodedText,UseSwitchCompatOrMaterialXml" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLookCrashLog"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="12dp"
        android:background="@drawable/shape_spb_32r_bg"
        android:gravity="center"
        android:text="查看崩溃日志"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/switchUploadCrashLog"
        tools:ignore="HardcodedText" />
</androidx.constraintlayout.widget.ConstraintLayout>