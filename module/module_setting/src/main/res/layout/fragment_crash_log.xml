<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:commTitle="@string/str_crash_log"
        app:commTitleBg="#00000000"
        app:commTitleColor="@color/text_headline"
        app:layout_constraintTop_toTopOf="parent" />

    <com.snails.module.setting.widget.CrashLogListView
        android:id="@+id/crashLogListView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layoutManager="com.snails.common.widget.SnailLinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView" />

</androidx.constraintlayout.widget.ConstraintLayout>