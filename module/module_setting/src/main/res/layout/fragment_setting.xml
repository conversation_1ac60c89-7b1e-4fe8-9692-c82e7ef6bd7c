<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:commTitle="@string/str_setting"
        app:commTitleBg="#00000000"
        app:commTitleColor="@color/text_headline"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vOneBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:background="@drawable/shape_sbw_12r_bg"
        app:layout_constraintBottom_toBottomOf="@+id/civUploadLog"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView" />

    <com.snails.module.base.widget.CommonItemView
        android:id="@+id/civRecordType"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        app:itemTitle="@string/str_record_type"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toTopOf="@+id/vOneBg" />

    <com.snails.module.base.widget.CommonItemView
        android:id="@+id/civCleanCache"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        app:itemTitle="@string/str_delete_cache"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toBottomOf="@+id/civRecordType" />

    <com.snails.module.base.widget.CommonItemView
        android:id="@+id/civUploadLog"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        app:itemTitle="@string/str_upload_log"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toBottomOf="@+id/civCleanCache" />

    <View
        android:id="@+id/vTwoBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:background="@drawable/shape_sbw_12r_bg"
        app:layout_constraintBottom_toBottomOf="@+id/civDeleteAccount"
        app:layout_constraintTop_toBottomOf="@+id/vOneBg" />

    <com.snails.module.base.widget.CommonItemView
        android:id="@+id/civCollectionPersonalInfo"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        app:itemTitle="@string/str_collection_personal_info"
        app:layout_constraintEnd_toEndOf="@+id/vTwoBg"
        app:layout_constraintStart_toStartOf="@+id/vTwoBg"
        app:layout_constraintTop_toTopOf="@+id/vTwoBg" />

    <com.snails.module.base.widget.CommonItemView
        android:id="@+id/civCollectionThirdInfo"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        app:itemTitle="@string/str_collection_third_info"
        app:layout_constraintEnd_toEndOf="@+id/vTwoBg"
        app:layout_constraintStart_toStartOf="@+id/vTwoBg"
        app:layout_constraintTop_toBottomOf="@+id/civCollectionPersonalInfo" />

    <com.snails.module.base.widget.CommonItemView
        android:id="@+id/civAboutUs"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        app:itemTitle="@string/str_about_us"
        app:layout_constraintEnd_toEndOf="@+id/vTwoBg"
        app:layout_constraintStart_toStartOf="@+id/vTwoBg"
        app:layout_constraintTop_toBottomOf="@+id/civCollectionThirdInfo" />

    <com.snails.module.base.widget.CommonItemView
        android:id="@+id/civDeleteAccount"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        app:itemTitle="@string/str_delete_account"
        app:layout_constraintEnd_toEndOf="@+id/vTwoBg"
        app:layout_constraintStart_toStartOf="@+id/vTwoBg"
        app:layout_constraintTop_toBottomOf="@+id/civAboutUs" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvExitApp"
        android:layout_width="@dimen/base_sw_dp_295"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:layout_marginBottom="@dimen/base_sw_dp_30"
        android:background="@drawable/shape_sbw_32r_bg"
        android:gravity="center"
        android:text="@string/str_exit_login"
        android:textColor="@color/text_error"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vTwoBg" />
</androidx.constraintlayout.widget.ConstraintLayout>