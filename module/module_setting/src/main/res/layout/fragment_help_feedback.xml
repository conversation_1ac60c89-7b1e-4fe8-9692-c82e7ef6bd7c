<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:commTitle="@string/str_help_and_feedback"
        app:commTitleBg="#00000000"
        app:commTitleColor="@color/text_headline"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vMenuBg"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_106"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:background="@drawable/shape_sbw_12r_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivContactUs"
        android:layout_width="@dimen/base_sw_dp_48"
        android:layout_height="@dimen/base_sw_dp_48"
        android:background="@drawable/svg_contact_us"
        app:layout_constraintBottom_toTopOf="@+id/tvContactUs"
        app:layout_constraintEnd_toStartOf="@+id/ivComplaint"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@+id/vMenuBg"
        app:layout_constraintTop_toTopOf="@+id/vMenuBg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvContactUs"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_6"
        android:text="@string/str_contact_us"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintBottom_toBottomOf="@+id/vMenuBg"
        app:layout_constraintStart_toEndOf="@+id/ivComplaint"
        app:layout_constraintStart_toStartOf="@+id/ivContactUs"
        app:layout_constraintTop_toBottomOf="@+id/ivContactUs" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivComplaint"
        android:layout_width="@dimen/base_sw_dp_48"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_60"
        android:background="@drawable/svg_complaint"
        app:layout_constraintBottom_toTopOf="@+id/tvContactUs"
        app:layout_constraintEnd_toStartOf="@+id/ivFeedback"
        app:layout_constraintStart_toEndOf="@+id/ivContactUs"
        app:layout_constraintTop_toTopOf="@+id/vMenuBg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvComplaint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_6"
        android:text="@string/str_complaint"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintBottom_toBottomOf="@+id/vMenuBg"
        app:layout_constraintEnd_toEndOf="@+id/ivComplaint"
        app:layout_constraintStart_toStartOf="@+id/ivComplaint"
        app:layout_constraintTop_toBottomOf="@+id/ivComplaint" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivFeedback"
        android:layout_width="@dimen/base_sw_dp_48"
        android:layout_height="@dimen/base_sw_dp_48"
        android:background="@drawable/svg_feedback"
        app:layout_constraintBottom_toTopOf="@+id/tvContactUs"
        app:layout_constraintEnd_toEndOf="@+id/vMenuBg"
        app:layout_constraintStart_toEndOf="@+id/ivComplaint"
        app:layout_constraintTop_toTopOf="@+id/vMenuBg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFeedback"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_6"
        android:text="@string/str_feedback"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintBottom_toBottomOf="@+id/vMenuBg"
        app:layout_constraintEnd_toEndOf="@+id/ivFeedback"
        app:layout_constraintStart_toStartOf="@+id/ivFeedback"
        app:layout_constraintTop_toBottomOf="@+id/ivFeedback" />

    <View
        android:id="@+id/vFAQ"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="@dimen/base_sw_dp_16"
        android:background="@drawable/shape_sbw_12r_bg"
        app:layout_constraintBottom_toBottomOf="@+id/faqListView"
        app:layout_constraintTop_toBottomOf="@+id/vMenuBg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFaqTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:text="@string/str_faq"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@+id/vFAQ"
        app:layout_constraintTop_toTopOf="@+id/vFAQ" />

    <com.snails.module.setting.widget.FAQListView
        android:id="@+id/faqListView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:paddingBottom="@dimen/base_sw_dp_20"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintEnd_toEndOf="@+id/vFAQ"
        app:layout_constraintStart_toStartOf="@+id/vFAQ"
        app:layout_constraintTop_toBottomOf="@+id/tvFaqTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>