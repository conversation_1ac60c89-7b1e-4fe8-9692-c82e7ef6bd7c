<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:commTitle="@string/str_contact_us"
        app:commTitleBg="#00000000"
        app:commTitleColor="@color/text_headline"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvServicePhoneTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_32"
        android:drawableStart="@drawable/svg_service_phone"
        android:drawablePadding="@dimen/base_sw_dp_4"
        android:gravity="center"
        android:text="@string/str_service_phone"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvServicePhone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintBottom_toBottomOf="@+id/tvServicePhoneTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvServicePhoneTitle" />

    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_1"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:background="@color/border_divider"
        app:layout_constraintTop_toBottomOf="@+id/tvServicePhoneTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCompanyAddressTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_32"
        android:drawableStart="@drawable/svg_location"
        android:drawablePadding="@dimen/base_sw_dp_4"
        android:gravity="center"
        android:text="@string/str_company_address"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vLine" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCompanyAddress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:gravity="end"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintBottom_toBottomOf="@+id/tvCompanyAddressTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvCompanyAddressTitle"
        app:layout_constraintTop_toTopOf="@+id/tvCompanyAddressTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>