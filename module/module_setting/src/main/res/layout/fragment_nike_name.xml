<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/surface_background_white"
    android:fitsSystemWindows="true">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:commTitle="@string/str_nike_name"
        app:commTitleBg="#00000000"
        app:commTitleColor="@color/text_headline"
        app:layout_constraintTop_toTopOf="parent"
        app:rightTxt="@string/str_sure" />

    <View
        android:id="@+id/vNikeNameBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:background="@drawable/shape_sb_24r_bg"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNikeName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:text="@string/str_nike_name_txt"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintBottom_toBottomOf="@+id/vNikeNameBg"
        app:layout_constraintStart_toStartOf="@+id/vNikeNameBg"
        app:layout_constraintTop_toTopOf="@+id/vNikeNameBg" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etNikeName"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginStart="@dimen/base_sw_dp_28"
        android:layout_marginEnd="@dimen/base_sw_dp_14"
        android:background="@null"
        android:gravity="center_vertical"
        android:hint="@string/str_input_content"
        android:maxLength="20"
        android:maxLines="2"
        android:textColor="@color/text_headline"
        android:textColorHint="@color/text_describe"
        android:textCursorDrawable="@drawable/color_cursor"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintBottom_toBottomOf="@+id/vNikeNameBg"
        app:layout_constraintEnd_toStartOf="@+id/ivClear"
        app:layout_constraintStart_toEndOf="@+id/tvNikeName"
        app:layout_constraintTop_toTopOf="@+id/vNikeNameBg" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClear"
        android:layout_width="@dimen/base_sw_dp_20"
        android:layout_height="@dimen/base_sw_dp_20"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:src="@drawable/svg_gray_close"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/vNikeNameBg"
        app:layout_constraintEnd_toEndOf="@+id/vNikeNameBg"
        app:layout_constraintTop_toTopOf="@+id/vNikeNameBg"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNikeNameTips"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:text="@string/str_input_content_tips"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_footnote"
        app:layout_constraintStart_toStartOf="@+id/vNikeNameBg"
        app:layout_constraintTop_toBottomOf="@+id/vNikeNameBg" />

</androidx.constraintlayout.widget.ConstraintLayout>