<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/setting_navigation"
    app:startDestination="@id/settingFragment">
    <fragment
        android:id="@+id/settingFragment"
        android:name="com.snails.module.setting.ui.SettingFragment"
        android:label="SettingFragment"
        tools:layout="@layout/fragment_setting">
        <action
            android:id="@+id/action_settingFragment_to_deleteAccountFragment"
            app:destination="@id/deleteAccountFragment" />
        <action
            android:id="@+id/action_settingFragment_to_aboutUsFragment"
            app:destination="@id/aboutUsFragment" />
    </fragment>

    <fragment
        android:id="@+id/babyInfoFragment"
        android:name="com.snails.module.setting.ui.BabyInfoFragment"
        android:label="BabyInfoFragment"
        tools:layout="@layout/fragment_baby_info">
        <action
            android:id="@+id/action_babyInfoFragment_to_babyNickNameFragment"
            app:destination="@id/babyNickNameFragment" />
        <action
            android:id="@+id/action_babyInfoFragment_to_environmentSettingFragment"
            app:destination="@id/environmentSettingFragment" />
    </fragment>

    <fragment
        android:id="@+id/babyNickNameFragment"
        android:name="com.snails.module.setting.ui.NickNameFragment"
        android:label="BabyNickNameFragment"
        tools:layout="@layout/fragment_nike_name" />

    <fragment
        android:id="@+id/messageNotificationFragment"
        android:name="com.snails.module.setting.ui.MessageNotificationFragment"
        android:label="MessageNotificationFragment"
        tools:layout="@layout/fragment_message_notification" />

    <fragment
        android:id="@+id/helpFeedbackFragment"
        android:name="com.snails.module.setting.ui.HelpFeedbackFragment"
        android:label="HelpFeedbackFragment"
        tools:layout="@layout/fragment_help_feedback">
        <action
            android:id="@+id/action_helpFeedbackFragment_to_contactUsFragment"
            app:destination="@id/contactUsFragment" />
        <action
            android:id="@+id/action_helpFeedbackFragment_to_feedbackFragment"
            app:destination="@id/feedbackFragment" />
        <action
            android:id="@+id/action_helpFeedbackFragment_to_faqDetailsFragment"
            app:destination="@id/faqDetailsFragment" />
    </fragment>

    <fragment
        android:id="@+id/deleteAccountFragment"
        android:name="com.snails.module.setting.ui.DeleteAccountFragment"
        android:label="DeleteAccountFragment"
        tools:layout="@layout/fragment_delete_account" />

    <fragment
        android:id="@+id/aboutUsFragment"
        android:name="com.snails.module.setting.ui.AboutUsFragment"
        android:label="AboutUsFragment"
        tools:layout="@layout/fragment_about_us" />

    <fragment
        android:id="@+id/contactUsFragment"
        android:name="com.snails.module.setting.ui.ContactUsFragment"
        android:label="ContactUsFragment"
        tools:layout="@layout/fragment_contact_us" />

    <fragment
        android:id="@+id/feedbackFragment"
        android:name="com.snails.module.setting.ui.FeedbackFragment"
        android:label="FeedbackFragment"
        tools:layout="@layout/fragment_feedback" />
    <fragment
        android:id="@+id/environmentSettingFragment"
        android:name="com.snails.module.setting.ui.EnvironmentSettingFragment"
        android:label="EnvironmentSettingFragment"
        tools:layout="@layout/fragment_environment_setting">
        <action
            android:id="@+id/action_environmentSettingFragment_to_crashLogFragment"
            app:destination="@id/crashLogFragment" />
    </fragment>

    <fragment
        android:id="@+id/crashLogFragment"
        android:name="com.snails.module.setting.ui.CrashLogFragment"
        android:label="CrashLogFragment"
        tools:layout="@layout/fragment_crash_log" />

    <fragment
        android:id="@+id/faqDetailsFragment"
        android:name="com.snails.module.setting.ui.FaqDetailsFragment"
        android:label="FaqDetailsFragment"
        tools:layout="@layout/fragment_faq_details" />
</navigation>
