<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_44"
        app:commTitleBg="#00000000"
        app:commTitleColor="@color/text_headline" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.snails.module.base.widget.refresh.CommonRefresh
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.snails.module.bookshelf.widget.AlbumListListView
            android:id="@+id/albumListListView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/base_sw_dp_18"
            android:layout_marginTop="@dimen/base_sw_dp_8" />

        <com.snails.module.base.widget.refresh.CommonRefresh
            android:id="@+id/footer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>
</LinearLayout>