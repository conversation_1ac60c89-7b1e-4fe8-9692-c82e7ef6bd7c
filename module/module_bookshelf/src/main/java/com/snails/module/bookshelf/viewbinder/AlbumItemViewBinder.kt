package com.snails.module.bookshelf.viewbinder

import com.snails.base.network.repository.info.expand.ExpandAlbumInfo
import com.snails.base.router.HRouter
import com.snails.base.utils.ext.addRadius
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.bookshelf.R
import com.snails.module.bookshelf.databinding.LayoutAlbumItemBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月26日 14:49:09
 */
class AlbumItemViewBinder(
    private val itemWidth: Int,
    private val itemClick: (String?) -> Unit
) : ViewBindingDelegate<ExpandAlbumInfo, LayoutAlbumItemBinding>() {
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<LayoutAlbumItemBinding>,
        item: ExpandAlbumInfo
    ) {
        holder.binding.apply {
            val layoutParams = clyContainer.layoutParams
            layoutParams.width = itemWidth
            clyContainer.layoutParams = layoutParams

            item.cover?.let {
                civBookshelfPic.load(
                    it,
                    placeholder = R.drawable.svg_w106_h106_placeholder,
                    error = R.drawable.svg_w106_h106_error
                )
            }
            sivBookshelfName.text = item.title
            ivPlay.setResType(item.contentResourceType)
            clyContainer.addRadius(R.dimen.base_sw_dp_12)

        }
        holder.itemView.singleClick {
            item.route?.let { route ->
                HRouter.navigation(route)
                itemClick.invoke(item.contentId)
            }
        }
    }
}