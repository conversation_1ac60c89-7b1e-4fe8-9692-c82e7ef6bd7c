package com.snails.module.bookshelf.widget

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ScreenUtils
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.expand.ExpandAlbumInfo
import com.snails.base.utils.ext.isPortraitPadMode
import com.snails.base.utils.ext.showItemSize
import com.snails.module.bookshelf.R
import com.snails.module.bookshelf.viewbinder.AlbumItemViewBinder

/**
 * @Description 专辑列表
 * <AUTHOR>
 * @CreateTime 2024年12月12日10:56:50
 */
class AlbumListListView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val albumList = mutableListOf<ExpandAlbumInfo>()
    var itemClickListener: ItemClickListener? = null
    private val listAdapter = MultiTypeAdapter()

    init {
        initView()
    }

    private fun initView() {
        val itemWidth = calculateItemWidth()
        // 列表项
        listAdapter.apply {
            register(ExpandAlbumInfo::class.java, AlbumItemViewBinder(itemWidth) {
                itemClickListener?.itemClick(it)
            })
        }
        adapter = listAdapter
        this.addItemDecoration(ItemDecoration())
    }

    private fun calculateItemWidth(): Int {
        return (ScreenUtils.getScreenWidth() - spaceWidth()) / context.showItemSize()
    }

    private fun spaceWidth(): Int {
        if (context.isPortraitPadMode()) {
            return resources.getDimensionPixelSize(R.dimen.base_sw_dp_96)
        }
        return resources.getDimensionPixelSize(R.dimen.base_sw_dp_56)
    }

    @Suppress("UNCHECKED_CAST")
    fun setData(new: List<ExpandAlbumInfo>, clean: Boolean) {
        if (clean) {
            albumList.clear()
        }
        albumList.addAll(new)
        val old = listAdapter.items as List<ExpandAlbumInfo>
        val diffResult: DiffUtil.DiffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition].javaClass == albumList[newItemPosition].javaClass
            }

            override fun getOldListSize(): Int {
                return old.size
            }

            override fun getNewListSize(): Int {
                return albumList.size
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition].isSame(albumList[newItemPosition])
            }
        })
        listAdapter.items = albumList
        diffResult.dispatchUpdatesTo(listAdapter)
    }

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State,
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect.left = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_6)
            outRect.right = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_6)
            outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_1)
        }
    }

    interface ItemClickListener {
        fun itemClick(contentId: String?)
    }
}