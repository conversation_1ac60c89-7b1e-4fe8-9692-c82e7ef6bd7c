package com.snails.module.bookshelf

import android.content.Intent
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.module.base.BaseStateActivity
import com.snails.module.bookshelf.databinding.ActivityBookshelfBinding
import com.therouter.router.Route

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月27日13:29:58
 */
@Route(path = RouterPath.BOOKSHELF_BASE)
class BookshelfActivity : BaseStateActivity<ActivityBookshelfBinding>() {

    override fun initData() {
        checkRouter(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.let {
            checkRouter(it)
        }
    }

    private fun checkRouter(intent: Intent) {
        HRouter.getOriginalPath(intent)?.let { targetFragmentPath ->
            dealRouter(targetFragmentPath)
        }
    }

    private fun dealRouter(fragmentPath: String?) {
        val navController =
            binding.navHostFragment.getFragment<NavHostFragment>().findNavController()
        val navGraph = navController.navInflater.inflate(R.navigation.bookshelf_navigation)
        val startDestinationId = when (fragmentPath) {
            RouterPath.BOOKSHELF_ALBUM_LIST -> {
                R.id.albumListFragment
            }

            else -> {
                R.id.albumListFragment
            }
        }
        navGraph.setStartDestination(startDestinationId)
        navController.graph = navGraph
    }
}