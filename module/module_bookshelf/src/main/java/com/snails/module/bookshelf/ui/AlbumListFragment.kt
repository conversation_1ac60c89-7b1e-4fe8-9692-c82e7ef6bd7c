package com.snails.module.bookshelf.ui

import androidx.fragment.app.viewModels
import androidx.navigation.Navigation
import androidx.recyclerview.widget.GridLayoutManager
import com.gyf.immersionbar.ImmersionBar
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.snails.base.utils.ext.showItemSize
import com.snails.module.base.BaseStateFragment
import com.snails.module.base.bean.StateType
import com.snails.module.bookshelf.databinding.FragmentAlbumListBinding
import com.snails.module.bookshelf.viewmodel.BookshelfViewModel

/**
 * @Description 专辑列表页
 * <AUTHOR>
 * @CreateTime 2024年12月12日 10:36:57
 */
class AlbumListFragment : BaseStateFragment<FragmentAlbumListBinding>() {

    private val bookshelfViewModel: BookshelfViewModel by viewModels()

    override fun createViewModel() = bookshelfViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initView() {
        super.initView()
        binding.albumListListView.setLayoutManager(
            GridLayoutManager(
                context,
                context.showItemSize()
            )
        )
    }

    override fun initData() {
        super.initData()
        bookshelfViewModel.subMode = activity?.intent?.getStringExtra("subMode")
        activity?.intent?.getStringExtra("title")?.let {
            binding.commonTitleView.setTitle(it)
        }

        bookshelfViewModel.getExpandAlbumList(StateType.PAGE)
    }

    override fun initClick() {
        super.initClick()
        binding.commonTitleView.setBackClickListener {
            val pop = Navigation.findNavController(it).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
        binding.refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                bookshelfViewModel.albumPage = 1
                bookshelfViewModel.getExpandAlbumList()
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                bookshelfViewModel.albumPage += 1
                bookshelfViewModel.getExpandAlbumList()
            }
        })
    }

    override fun initObserve() {
        super.initObserve()
        bookshelfViewModel.expandAlbumInfoLiveData.observe(viewLifecycleOwner) { info ->
            info?.let {
                binding.albumListListView.setData(info, bookshelfViewModel.albumPage == 1)
                if (info.size < 20) {
                    binding.refreshLayout.setEnableLoadMore(false)
                } else {
                    binding.refreshLayout.setEnableLoadMore(true)
                }
            }
            binding.refreshLayout.finishLoadMore()
            binding.refreshLayout.finishRefresh()
        }
    }

    override fun onRetry() {
        super.onRetry()
        bookshelfViewModel.getExpandAlbumList(StateType.PAGE)
    }
}