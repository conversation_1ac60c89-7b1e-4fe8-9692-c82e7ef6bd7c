package com.snails.module.bookshelf.viewmodel

import androidx.lifecycle.MutableLiveData
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.info.expand.ExpandAlbumInfo
import com.snails.module.base.BaseViewModel
import com.snails.module.base.bean.StateType
import com.snails.module.base.utils.SingleLiveEventLiveData

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年12月12日 11:10:16
 */
class BookshelfViewModel : BaseViewModel() {

    var subMode: String? = null

    //拓展页-查看更多-专辑列表
    val expandAlbumInfoLiveData: MutableLiveData<List<ExpandAlbumInfo>> = SingleLiveEventLiveData()

    //拓展页-查看更多-专辑列表-分页
    var albumPage = 1

    /**
     * 拓展页-查看更多-专辑列表
     */
    fun getExpandAlbumList(stateType: StateType = StateType.NONE) {
        val mode = subMode ?: return
        request(
            stateType = stateType,
            request = {
                SnailRepository.me.getExpandAlbumList(albumPage, mode)
            },
            success = { data ->
                data?.items.let {
                    expandAlbumInfoLiveData.value = it
                }
            }
        )
    }
}