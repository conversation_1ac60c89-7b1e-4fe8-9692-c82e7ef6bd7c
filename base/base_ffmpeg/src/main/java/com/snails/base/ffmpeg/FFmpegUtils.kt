package com.snails.base.ffmpeg

import android.os.Handler
import android.os.Looper
import com.arthenica.ffmpegkit.FFmpegKit
import com.arthenica.ffmpegkit.Level
import com.arthenica.ffmpegkit.ReturnCode
import com.blankj.utilcode.util.FileUtils
import com.sanils.base.log.HLog
import com.snails.base.ffmpeg.callback.CommandCallback
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年04月15日 09:46:12
 */
object FFmpegUtils {

    // 任务管理
    private val activeTasks = ConcurrentHashMap<Long, TaskInfo>()
    private val taskIdGenerator = AtomicLong(0)
    private val handler = Handler(Looper.getMainLooper())

    // 默认超时时间（30分钟）
    private const val DEFAULT_TIMEOUT_MS = 30 * 60 * 1000L

    data class TaskInfo(
        val callback: CommandCallback?,
        val outputPath: String,
        val timeoutRunnable: Runnable
    )

    /**
     * @param list 需要合并的音频列表
     * @param outputPath 合并后的输出目录
     * @param callback 回调
     * @param timeoutMs 超时时间，默认30分钟
     * @return 任务ID，可用于取消任务
     */
    fun mergeAudioFiles(
        list: List<String>,
        outputPath: String,
        callback: CommandCallback? = null,
        timeoutMs: Long = DEFAULT_TIMEOUT_MS
    ): Long {
        val taskId = taskIdGenerator.incrementAndGet()
        
        try {
            // 创建超时任务
            val timeoutRunnable = Runnable {
                val taskInfo = activeTasks.remove(taskId)
                if (taskInfo != null) {
                    HLog.i("FFmpeg", "音频合并任务超时，taskId: $taskId")
                    FFmpegKit.cancel()
                    FileUtils.delete(outputPath)
                    taskInfo.callback?.error(Throwable("任务超时"))
                }
            }

            // 注册任务
            val taskInfo = TaskInfo(callback, outputPath, timeoutRunnable)
            activeTasks[taskId] = taskInfo
            handler.postDelayed(timeoutRunnable, timeoutMs)
            
            callback?.start()

            // 构建输入文件参数
            val inputFiles = list.joinToString(" ") { "-i \"$it\"" }

            // 构建 filter_complex 字符串
            val filterInputs = list.indices.joinToString("") { "[$it:a]" }
            val filterComplex = "\"$filterInputs concat=n=${list.size}:v=0:a=1[outa]\""
            // 拼接完整命令
            val command =
                "$inputFiles -filter_complex $filterComplex -map \"[outa]\" \"$outputPath\""

            FFmpegKit.executeAsync(command) { session ->
                // 移除任务并取消超时
                val completedTaskInfo = activeTasks.remove(taskId)
                completedTaskInfo?.let {
                    handler.removeCallbacks(it.timeoutRunnable)
                }
                
                HLog.i("FFmpeg", "音频合并。命令-> ${session.command}")
                if (ReturnCode.isSuccess(session.returnCode)) {
                    completedTaskInfo?.callback?.succeed()
                } else if (ReturnCode.isCancel(session.returnCode)) {
                    completedTaskInfo?.callback?.error(Throwable("取消"))
                } else {
                    val failStackTrace =
                        session.logs.filter { it.level != Level.AV_LOG_INFO }.toString()
                    HLog.i("FFmpeg", "音频失败。命令-> $failStackTrace")
                    completedTaskInfo?.callback?.error(Throwable("失败，failStackTrace"))
                }
            }
        } catch (t: Throwable) {
            // 异常时清理任务
            val taskInfo = activeTasks.remove(taskId)
            taskInfo?.let { handler.removeCallbacks(it.timeoutRunnable) }
            
            HLog.i("FFmpeg", "音频失败。异常-> ${t.message}")
            callback?.error(Throwable("异常，${t.message}"))
        }

        return taskId
    }

    /**
     * @param inputPath 输入视频路径
     * @param outputPath 输出视频路径
     * @param cmd 压缩命令参数
     * @param callback 回调
     * @param timeoutMs 超时时间，默认30分钟
     * @return 任务ID，可用于取消任务
     */
    fun compressVideo(
        inputPath: String,
        outputPath: String,
        cmd: String,
        callback: CommandCallback? = null,
        timeoutMs: Long = DEFAULT_TIMEOUT_MS
    ): Long {
        val taskId = taskIdGenerator.incrementAndGet()
        
        try {
            // 创建超时任务
            val timeoutRunnable = Runnable {
                val taskInfo = activeTasks.remove(taskId)
                if (taskInfo != null) {
                    HLog.i("FFmpeg", "视频压缩任务超时，taskId: $taskId")
                    FFmpegKit.cancel()
                    FileUtils.delete(outputPath)
                    taskInfo.callback?.error(Throwable("任务超时"))
                }
            }

            // 注册任务
            val taskInfo = TaskInfo(callback, outputPath, timeoutRunnable)
            activeTasks[taskId] = taskInfo
            handler.postDelayed(timeoutRunnable, timeoutMs)
            
            val cmdLine = cmd.ifEmpty {
                "-vcodec libx264 -crf 23 -preset ultrafast -c:a copy"
            }
            val command = "-i \"$inputPath\" $cmdLine \"$outputPath\""
            callback?.start()

            FFmpegKit.executeAsync(command) { session ->
                // 移除任务并取消超时
                val completedTaskInfo = activeTasks.remove(taskId)
                completedTaskInfo?.let {
                    handler.removeCallbacks(it.timeoutRunnable)
                }
                
                HLog.i("FFmpeg", "视频压缩。命令-> ${session.command}")
                if (ReturnCode.isSuccess(session.returnCode)) {
                    completedTaskInfo?.callback?.succeed()
                } else if (ReturnCode.isCancel(session.returnCode)) {
                    val delete = FileUtils.delete(outputPath)
                    completedTaskInfo?.callback?.error(Throwable("取消.delete=$delete"))
                } else {
                    val failStackTrace =
                        session.logs.filter { it.level != Level.AV_LOG_INFO }.toString()
                    val delete = FileUtils.delete(outputPath)
                    HLog.i("FFmpeg", "视频压缩失败 -> $failStackTrace...delete=$delete")
                    completedTaskInfo?.callback?.error(Throwable("失败，failStackTrace...delete=$delete"))
                }
            }
        } catch (t: Throwable) {
            // 异常时清理任务
            val taskInfo = activeTasks.remove(taskId)
            taskInfo?.let { handler.removeCallbacks(it.timeoutRunnable) }
            
            val delete = FileUtils.delete(outputPath)
            HLog.i("FFmpeg", "视频压缩异常 -> ${t.message}...delete=$delete")
            callback?.error(Throwable("异常，${t.message}"))
        }

        return taskId
    }

    /**
     * 取消指定任务
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    fun cancelTask(taskId: Long): Boolean {
        val taskInfo = activeTasks.remove(taskId)
        return if (taskInfo != null) {
            handler.removeCallbacks(taskInfo.timeoutRunnable)
            FileUtils.delete(taskInfo.outputPath)
            taskInfo.callback?.error(Throwable("任务被取消"))
            HLog.i("FFmpeg", "任务已取消，taskId: $taskId")
            true
        } else {
            false
        }
    }

    /**
     * 取消所有任务
     */
    fun cancelAllTasks() {
        FFmpegKit.cancel()
        val taskIds = activeTasks.keys.toList()
        taskIds.forEach { taskId ->
            val taskInfo = activeTasks.remove(taskId)
            taskInfo?.let {
                handler.removeCallbacks(it.timeoutRunnable)
                FileUtils.delete(it.outputPath)
                it.callback?.error(Throwable("所有任务被取消"))
            }
        }
        HLog.i("FFmpeg", "所有任务已取消，共取消 ${taskIds.size} 个任务")
    }

    /**
     * 获取当前活跃任务数量
     */
    fun getActiveTaskCount(): Int = activeTasks.size

    /**
     * 获取所有活跃任务ID
     */
    fun getActiveTaskIds(): List<Long> = activeTasks.keys.toList()

    /**
     * 清理超时或无效的任务（通常在应用内存紧张时调用）
     */
    fun cleanupTasks() {
        val currentTime = System.currentTimeMillis()
        val tasksToRemove = mutableListOf<Long>()

        activeTasks.forEach { (taskId, taskInfo) ->
            // 这里可以添加更多清理逻辑，比如检查任务创建时间等
            if (taskInfo.callback == null) {
                tasksToRemove.add(taskId)
            }
        }

        tasksToRemove.forEach { taskId ->
            val taskInfo = activeTasks.remove(taskId)
            taskInfo?.let {
                handler.removeCallbacks(it.timeoutRunnable)
                FileUtils.delete(it.outputPath)
            }
        }

        if (tasksToRemove.isNotEmpty()) {
            HLog.i("FFmpeg", "清理了 ${tasksToRemove.size} 个无效任务")
        }
    }
}