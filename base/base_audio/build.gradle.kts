plugins {
    alias(libs.plugins.common.gradle)
}

dependencies {
    api(libs.media3.exoplayer)
    api(libs.media3.datasource.okhttp)
    api(libs.media3.session)
    api(libs.media3.ui)
    implementation(libs.utilcodex)
    implementation(libs.androidx.core.ktx)
    implementation(project(":base:base_utils"))
}
//val versionName = "${libs.versions.mavenVersion.get()}-${libs.versions.mavenMode.get()}"
//
//// 生成 sources.jar
//val sourcesJar by tasks.registering(Jar::class) {
//    archiveClassifier.set("sources")
//    from(android.sourceSets["main"].java.srcDirs)
//}
//
//// 配置 Maven 发布
//publishing {
//    publications {
//        create<MavenPublication>("maven") { // 统一名称
//            groupId = "com.snails.base"
//            artifactId = "audio"
//            // 根据 libs.versions.toml 配置判断是打debug 还是 release
//            afterEvaluate {
//                val isDebug = libs.versions.mavenMode.get().contains("debug")
//                val aarTaskName = if (isDebug) {
//                    "bundleDebugAar"
//                } else {
//                    "bundleReleaseAar"
//                }
//                println("打包：${groupId}:${artifactId} AAR: $aarTaskName, Version: $versionName")
//                version = versionName
//                artifact(tasks.getByName(aarTaskName))
//            }
//            // 添加 sources 和 javadoc
//            artifact(sourcesJar)
//        }
//    }
//    repositories {
//        mavenLocal()
//    }
//}
