package com.snails.base.video_player.control

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.SeekBar
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airbnb.lottie.LottieAnimationView
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.invisible
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.base.video.player.R
import com.snails.base.video_player.bean.ClarityInfo
import com.snails.base.video_player.widget.ClarityListView

class VideoControlVideo : StandardGSYVideoPlayer {

    var videoListener: VideoListener? = null

    //清晰度列表数据
    private var clarityList: List<ClarityInfo>? = null

    //是否是全屏
    private var isFullScreen = false
    private var clyContainer: ConstraintLayout? = null
    private var ivPlayOrPause: AppCompatImageView? = null
    private var tvClarity: AppCompatTextView? = null
    private var lottieLoading: LottieAnimationView? = null
    private var progress: SeekBar? = null
    private var popView: View? = null
    private val viewWidth = context.resources.getDimension(R.dimen.base_sw_dp_63).toInt()

    /**
     * 清晰度列表
     */
    private var clarityListView: ClarityListView? = null

    //pop 显示的X坐标
    private var popShowX = 0

    //pop 显示的y坐标
    private var popShowY = 0

    //清晰度弹窗
    private var clarityPopupWindow: PopupWindow? = null

    constructor(context: Context?) : super(context)

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)

    override fun getLayoutId() = R.layout.video_control_video

    override fun touchSurfaceMoveFullLogic(absDeltaX: Float, absDeltaY: Float) {
        super.touchSurfaceMoveFullLogic(absDeltaX, absDeltaY)
        //不给触摸快进，如果需要，屏蔽下方代码即可
        mChangePosition = false

        //不给触摸音量，如果需要，屏蔽下方代码即可
        mChangeVolume = false

        //不给触摸亮度，如果需要，屏蔽下方代码即可
        mBrightness = false
    }

    override fun init(context: Context?) {
        super.init(context)
        clyContainer = findViewById(R.id.clyContainer)
        ivPlayOrPause = findViewById(R.id.ivPlayOrPause)
        tvClarity = findViewById(R.id.tvClarity)
        progress = findViewById(R.id.progress)
        lottieLoading = findViewById(R.id.lottieLoading)
        progress?.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                showDragProgressTextOnSeekBar(fromUser, progress)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                videoListener?.resetTimer(false)
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                <EMAIL>(seekBar)
                videoListener?.resetTimer(true)
            }
        })
        ivPlayOrPause?.singleClick {
            if (mCurrentState == CURRENT_STATE_PLAYING) {
                videoListener?.changePlayState(false)
            } else {
                videoListener?.changePlayState(true)
            }
        }
        tvClarity?.singleClick {
            if ((clarityList?.size ?: 0) > 0) {
                //清晰度
                showClarityPopupWindow(it)
            }
        }
    }

    override fun touchDoubleUp(e: MotionEvent) {
        //super.touchDoubleUp();
        //不需要双击暂停
    }

    override fun onClickUiToggle(e: MotionEvent?) {
        videoListener?.resetTimer(true)
        if (isFullScreen) {
            videoListener?.screenClick(false)
            isFullScreen = false
            clyContainer?.visible()
        } else {
            videoListener?.screenClick(true)
            isFullScreen = true
            clyContainer?.gone()
        }
    }

    override fun setStateAndUi(state: Int) {
        super.setStateAndUi(state)
        videoListener?.playState(state)
        when (state) {
            CURRENT_STATE_PREPAREING -> {
                //加载中
                lottieLoading?.visible()
                ivPlayOrPause?.invisible()
            }

            CURRENT_STATE_PLAYING -> {
                //播放中
                lottieLoading?.gone()
                ivPlayOrPause?.visible()
                ivPlayOrPause?.setBackgroundResource(R.drawable.svg_video_play_bw)
            }

            else -> {
                lottieLoading?.gone()
                ivPlayOrPause?.visible()
                ivPlayOrPause?.setBackgroundResource(R.drawable.svg_video_pause_bw)
            }
        }
    }

    /**
     * 初始化清晰度PopupWindow
     */
    private fun initClarityPopupWindow() {
        popView = LayoutInflater.from(context).inflate(R.layout.layout_clarity_pop, null)
        clarityListView = popView?.findViewById(R.id.clarityListView)
        clarityListView?.itemClickListener = object : ClarityListView.ItemClickListener {
            override fun itemClick(info: ClarityInfo) {
                tvClarity?.text = info.qualityName
                videoListener?.chooseClarity(info)
                closeClarityPopupWindow()
            }
        }
        clarityPopupWindow = PopupWindow(
            popView,
            viewWidth,
            LinearLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            isOutsideTouchable = true //设置点击外部区域可以取消popupWindow
        }
    }

    private fun measurePopSize(
        anchorView: AppCompatTextView,
        viewWidth: Int
    ) {
        // 测量 PopupWindow 的宽高
        popView?.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED)
        val popupHeight = popView?.measuredHeight ?: 0
        // 获取 anchorView 的位置
        val location = IntArray(2)
        anchorView.getLocationOnScreen(location)
        popShowX = location[0] - viewWidth / 4
        popShowY = location[1] - popupHeight
    }

    private fun showClarityPopupWindow(anchorView: AppCompatTextView) {
        if (clarityPopupWindow == null) {
            initClarityPopupWindow()
        }
        clarityPopupWindow?.let { pop ->
            if (!pop.isShowing) {
                this.clarityList?.let { clarityListView?.setData(it) }
                measurePopSize(anchorView, viewWidth)
                clarityPopupWindow?.showAtLocation(
                    anchorView,
                    Gravity.NO_GRAVITY,
                    popShowX,
                    popShowY
                )
                videoListener?.resetTimer(false)
            } else {
                pop.dismiss()
                videoListener?.resetTimer(true)
            }
        }
    }

    private fun closeClarityPopupWindow() {
        clarityPopupWindow?.dismiss()
        videoListener?.resetTimer(true)
    }

    fun setScreenState(isFull: Boolean) {
        isFullScreen = isFull
        if (isFull) {
            clyContainer?.gone()
        } else {
            clyContainer?.visible()
        }
    }

    /**
     * 设置清晰度数据
     */
    fun setClarityData(list: List<ClarityInfo>) {
        this.clarityList = list
        tvClarity?.text = list.firstOrNull()?.qualityName
    }

    interface VideoListener {
        /**
         * 屏幕点击事件
         * @param needFull 是否需要全屏
         */
        fun screenClick(needFull: Boolean)

        /**
         * 改变播放状态
         * @param playing true:播放，false：暂停
         */
        fun changePlayState(playing: Boolean)

        /**
         * 清晰度
         */
        fun chooseClarity(info: ClarityInfo)

        /**
         * 播放状态
         */
        fun playState(state: Int)

        /**
         * 重置定时器
         * @param isFull 是否全屏
         */
        fun resetTimer(isFull: Boolean)
    }
}
