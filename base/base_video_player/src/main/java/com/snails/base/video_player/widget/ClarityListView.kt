package com.snails.base.video_player.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ColorUtils
import com.snails.base.utils.ext.singleClick
import com.snails.base.video.player.R
import com.snails.base.video_player.bean.ClarityInfo

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月25日 18:14:28
 */
class ClarityListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    var itemClickListener: ItemClickListener? = null
    private var clarityAdapter: ClarityAdapter

    init {
        clarityAdapter = ClarityAdapter()
        this.adapter = clarityAdapter
    }

    fun setData(list: List<ClarityInfo>) {
        clarityAdapter.setData(list)
    }

    inner class ClarityAdapter : Adapter<ClarityAdapter.ClarityViewHolder>() {

        private var listClarity: List<ClarityInfo>? = null

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ClarityViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.layout_clarity_pop_item, parent, false)
            return ClarityViewHolder(view)
        }

        override fun getItemCount() = listClarity?.size ?: 0

        override fun onBindViewHolder(holder: ClarityViewHolder, position: Int) {
            val data = listClarity?.get(position) ?: return
            holder.tvClarity?.text = data.qualityName ?: ""

            if (data.choose) {
                holder.tvClarity?.setTextColor(ColorUtils.getColor(R.color.text_link))
            } else {
                holder.tvClarity?.setTextColor(ColorUtils.getColor(R.color.text_headline))
            }

            holder.tvClarity?.singleClick {
                setChooseItem(position)
                itemClickListener?.itemClick(data)
            }
        }

        @SuppressLint("NotifyDataSetChanged")
        fun setData(list: List<ClarityInfo>) {
            this.listClarity = list
            notifyDataSetChanged()
        }

        @SuppressLint("NotifyDataSetChanged")
        private fun setChooseItem(pos: Int) {
            this.listClarity?.forEachIndexed { index, clarityInfo ->
                clarityInfo.choose = pos == index
                notifyDataSetChanged()
            }
        }

        inner class ClarityViewHolder(itemView: View) : ViewHolder(itemView) {
            var tvClarity: AppCompatTextView? = null

            init {
                tvClarity = itemView.findViewById(R.id.tvClarity)
            }
        }
    }

    interface ItemClickListener {
        fun itemClick(info: ClarityInfo)
    }
}