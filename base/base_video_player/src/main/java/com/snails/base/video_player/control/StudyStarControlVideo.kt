package com.snails.base.video_player.control

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatImageView
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.invisible
import com.snails.base.utils.ext.visible
import com.snails.base.video.player.R

/**
 * 自由播放停止UI
 */
class StudyStarControlVideo : StandardGSYVideoPlayer {

    constructor(context: Context?) : super(context)

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)

    private var ivPlay: AppCompatImageView? = null

    override fun getLayoutId() = R.layout.study_star_control_video

    override fun touchSurfaceMoveFullLogic(absDeltaX: Float, absDeltaY: Float) {
        super.touchSurfaceMoveFullLogic(absDeltaX, absDeltaY)
        //不给触摸快进，如果需要，屏蔽下方代码即可
        mChangePosition = false

        //不给触摸音量，如果需要，屏蔽下方代码即可
        mChangeVolume = false

        //不给触摸亮度，如果需要，屏蔽下方代码即可
        mBrightness = false
    }

    override fun touchDoubleUp(e: MotionEvent) {
        //super.touchDoubleUp();
        //不需要双击暂停
    }

    override fun init(context: Context?) {
        super.init(context)
        ivPlay = findViewById(R.id.ivPlay)
    }

    override fun onClickUiToggle(e: MotionEvent?) {
        if (mCurrentState == CURRENT_STATE_PLAYING) {
            onVideoPause()
            ivPlay?.visible()
            mProgressBar?.visible()
        } else {
            onVideoResume(false)
            ivPlay?.gone()
            mProgressBar?.invisible()
        }
    }

    override fun setProgressAndTime(
        progress: Long,
        secProgress: Long,
        currentTime: Long,
        totalTime: Long,
        forceChange: Boolean
    ) {
        mProgressBar.progress = progress.toInt()
    }
}
