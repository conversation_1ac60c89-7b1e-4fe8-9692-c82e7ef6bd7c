package com.snails.base.video_player.control

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatImageView
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.base.video.player.R

class SimpleControlVideo : StandardGSYVideoPlayer {

    var videoListener: VideoListener? = null

    private var ivPlayOrPause: AppCompatImageView? = null


    constructor(context: Context?) : super(context)

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)

    override fun getLayoutId() = R.layout.simple_control_video

    override fun touchSurfaceMoveFullLogic(absDeltaX: Float, absDeltaY: Float) {
        super.touchSurfaceMoveFullLogic(absDeltaX, absDeltaY)
        //不给触摸快进，如果需要，屏蔽下方代码即可
        mChangePosition = false

        //不给触摸音量，如果需要，屏蔽下方代码即可
        mChangeVolume = false

        //不给触摸亮度，如果需要，屏蔽下方代码即可
        mBrightness = false
    }

    override fun init(context: Context?) {
        super.init(context)
        ivPlayOrPause = findViewById(R.id.ivPlayOrPause)
        ivPlayOrPause?.singleClick {
            if (mCurrentState == CURRENT_STATE_PLAYING) {
                videoListener?.changePlayState(false)
            } else {
                videoListener?.changePlayState(true)
                startPlayLogic()
            }
        }
    }

    override fun touchDoubleUp(e: MotionEvent) {
        //super.touchDoubleUp();
        //不需要双击暂停
    }


    override fun setStateAndUi(state: Int) {
        super.setStateAndUi(state)
        videoListener?.playState(state)
        if (state == CURRENT_STATE_PLAYING) {
            ivPlayOrPause?.gone()
        } else {
            ivPlayOrPause?.setBackgroundResource(R.drawable.svg_video_pause_bw)
            ivPlayOrPause?.visible()
        }
    }


    interface VideoListener {
        /**
         * 改变播放状态
         * @param playing true:播放，false：暂停
         */
        fun changePlayState(playing: Boolean)

        /**
         * 播放状态
         */
        fun playState(state: Int)
    }
}
