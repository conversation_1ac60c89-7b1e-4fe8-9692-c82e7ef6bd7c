package com.snails.base.video_player.control

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.invisible
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.base.video.player.R

/**
 * 横竖屏切换
 */
class NormalControlVideo : StandardGSYVideoPlayer {

    constructor(context: Context?) : super(context)

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)

    private var ivPlay: AppCompatImageView? = null //播放按钮
    private var ivChangeScreenOrientation: AppCompatImageView? = null //横竖屏切换按钮
    private var tvVideoSpeed: AppCompatTextView? = null //横竖屏切换按钮
    private var ivPlayCover: AppCompatImageView? = null //视频封面
    private var vBg: View? = null //视频封面

    private var currentScreenOrientation = 1 //0：竖屏，1：横屏
    var normalControlListener: NormalControlListener? = null
    override fun getLayoutId() = R.layout.normal_control_video

    override fun touchSurfaceMoveFullLogic(absDeltaX: Float, absDeltaY: Float) {
        super.touchSurfaceMoveFullLogic(absDeltaX, absDeltaY)
        //不给触摸快进，如果需要，屏蔽下方代码即可
        mChangePosition = false

        //不给触摸音量，如果需要，屏蔽下方代码即可
        mChangeVolume = false

        //不给触摸亮度，如果需要，屏蔽下方代码即可
        mBrightness = false
    }

    override fun touchDoubleUp(e: MotionEvent) {
        //super.touchDoubleUp();
        //不需要双击暂停
    }

    override fun init(context: Context?) {
        super.init(context)

        ivPlay = findViewById(R.id.ivPlay)
        ivChangeScreenOrientation = findViewById(R.id.ivChangeScreenOrientation)
        ivPlayCover = findViewById(R.id.ivPlayCover)
        vBg = findViewById(R.id.vBg)
        tvVideoSpeed = findViewById(R.id.tvVideoSpeed)

        ivPlay?.singleClick {
            clickStartIcon()
            hideCoverUi()
            hideInfoUi()
        }

        tvVideoSpeed?.singleClick {
            normalControlListener?.clickVideoSpeed()
        }

        ivChangeScreenOrientation?.singleClick {
            normalControlListener?.changeOrientation(
                if (currentScreenOrientation == 1) {
                    currentScreenOrientation = 0
                    0
                } else {
                    currentScreenOrientation = 1
                    1
                }
            )
        }
    }

    override fun onClickUiToggle(e: MotionEvent?) {
        if (mCurrentState == CURRENT_STATE_PLAYING) {
            onVideoPause()
            ivPlay?.visible()
            showInfoUi()
        } else if (mCurrentState == CURRENT_STATE_PAUSE) {
            onVideoResume(false)
            ivPlay?.gone()
            hideInfoUi()
        }
    }

    fun getPlayCoverView() = ivPlayCover

    fun showCoverUi() {
        ivPlayCover?.visible()
        ivPlay?.visible()
        hideInfoUi()
    }

    fun showSpeed(speed: Float) {
        val showSpeed = when (speed) {
            1.0f -> {
                "倍数"
            }

            1.5f -> {
                "1.5x"
            }

            2.0f -> {
                "2.0x"
            }

            else -> {
                "倍数"
            }
        }
        tvVideoSpeed?.text = showSpeed
    }

    private fun hideCoverUi() {
        ivPlayCover?.gone()
        ivPlay?.gone()
    }

    private fun hideInfoUi() {
        mProgressBar?.invisible()
        mCurrentTimeTextView?.invisible()
        mTotalTimeTextView?.invisible()
        ivChangeScreenOrientation?.invisible()
        tvVideoSpeed?.invisible()
        vBg?.invisible()
    }

    private fun showInfoUi() {
        mProgressBar?.visible()
        mCurrentTimeTextView?.visible()
        mTotalTimeTextView?.visible()
        ivChangeScreenOrientation?.visible()
        tvVideoSpeed?.visible()
        vBg?.visible()
    }

    interface NormalControlListener {
        /**
         * @param orientation //0：竖屏，1：横屏
         */
        fun changeOrientation(orientation: Int)

        /**
         * 视频倍数
         */
        fun clickVideoSpeed()
    }
}
