<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <FrameLayout
        android:id="@+id/surface_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPlayOrPause"
        android:layout_width="@dimen/base_sw_dp_32"
        android:layout_height="@dimen/base_sw_dp_32"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/base_sw_dp_20"
        android:layout_marginTop="@dimen/base_sw_dp_30"
        android:background="@drawable/svg_wb_play"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</RelativeLayout>
