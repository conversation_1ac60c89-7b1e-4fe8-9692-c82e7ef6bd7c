<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <FrameLayout
        android:id="@+id/surface_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPlay"
        android:layout_width="@dimen/base_sw_dp_72"
        android:layout_height="@dimen/base_sw_dp_72"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:background="@drawable/svg_video_play"
        android:visibility="gone" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_100"
        android:layout_above="@+id/vBottom"
        android:background="@drawable/shape_study_star_gradient_bg" />

    <View
        android:id="@+id/vBottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_45"
        android:layout_alignParentBottom="true"
        android:background="#000000" />

    <SeekBar
        android:id="@+id/progress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center_vertical"
        android:layout_marginHorizontal="@dimen/base_sw_dp_6"
        android:layout_marginBottom="@dimen/base_sw_dp_30"
        android:background="@null"
        android:max="100"
        android:maxHeight="@dimen/base_sw_dp_4"
        android:minHeight="@dimen/base_sw_dp_4"
        android:progressDrawable="@drawable/study_star_video_seek_progress"
        android:splitTrack="false"
        android:thumb="@drawable/video_seek_thumb"
        android:visibility="invisible"
        tools:visibility="visible" />
</RelativeLayout>
