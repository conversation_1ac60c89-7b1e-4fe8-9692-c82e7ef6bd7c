<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/base_sw_dp_63"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <com.snails.base.video_player.widget.ClarityListView
        android:id="@+id/clarityListView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_clarity_pop_bg"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="@dimen/base_sw_dp_10"
        android:layout_height="@dimen/base_sw_dp_5"
        android:background="@drawable/svg_white_arrow_down_pop" />
</LinearLayout>
