<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <FrameLayout
        android:id="@+id/surface_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clyContainer"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_80"
        android:layout_alignParentBottom="true"
        android:background="@drawable/shape_video_control_bg">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivPlayOrPause"
            android:layout_width="@dimen/base_sw_dp_32"
            android:layout_height="@dimen/base_sw_dp_32"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/base_sw_dp_20"
            android:layout_marginTop="@dimen/base_sw_dp_30"
            android:background="@drawable/svg_video_play_bw"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottieLoading"
            android:layout_width="@dimen/base_sw_dp_32"
            android:layout_height="@dimen/base_sw_dp_32"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/base_sw_dp_20"
            android:layout_marginTop="@dimen/base_sw_dp_30"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/lottie_video_loading"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvClarity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/base_sw_dp_20"
            android:drawableEnd="@drawable/svg_white_arrow_down"
            android:text="@string/str_sd"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/text_body_small"
            app:layout_constraintBottom_toBottomOf="@+id/ivPlayOrPause"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivPlayOrPause"
            tools:ignore="HardcodedText" />

        <SeekBar
            android:id="@+id/progress"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_gravity="center_vertical"
            android:background="@null"
            android:max="100"
            android:maxHeight="@dimen/base_sw_dp_4"
            android:minHeight="@dimen/base_sw_dp_4"
            android:progressDrawable="@drawable/study_star_video_seek_progress"
            android:splitTrack="false"
            android:thumb="@drawable/ic_play_progress_label"
            app:layout_constraintBottom_toBottomOf="@+id/ivPlayOrPause"
            app:layout_constraintEnd_toStartOf="@+id/tvClarity"
            app:layout_constraintStart_toEndOf="@+id/ivPlayOrPause"
            app:layout_constraintTop_toTopOf="@+id/ivPlayOrPause" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/total"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            android:text="00:00"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/text_body_small"
            app:layout_constraintEnd_toEndOf="@+id/progress"
            app:layout_constraintTop_toBottomOf="@+id/progress"
            tools:ignore="HardcodedText" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLine"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="/"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/text_body_small"
            app:layout_constraintBottom_toBottomOf="@+id/total"
            app:layout_constraintEnd_toStartOf="@+id/total"
            app:layout_constraintTop_toTopOf="@+id/total"
            tools:ignore="HardcodedText" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/current"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/text_body_small"
            app:layout_constraintBottom_toBottomOf="@+id/total"
            app:layout_constraintEnd_toStartOf="@+id/tvLine"
            app:layout_constraintTop_toTopOf="@+id/total"
            tools:ignore="HardcodedText" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>
