<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <FrameLayout
        android:id="@+id/surface_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center" />

    <View
        android:id="@+id/vBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_100"
        android:background="@drawable/shape_study_star_gradient_bg"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/current"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:text="00:00"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/text_body_small"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/progress"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/progress"
        tools:ignore="HardcodedText"
        tools:visibility="visible" />

    <SeekBar
        android:id="@+id/progress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:layout_marginBottom="@dimen/base_sw_dp_42"
        android:background="@null"
        android:max="100"
        android:maxHeight="@dimen/base_sw_dp_4"
        android:minHeight="@dimen/base_sw_dp_4"
        android:progressDrawable="@drawable/study_star_video_seek_progress"
        android:splitTrack="false"
        android:thumb="@drawable/video_seek_thumb"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/total"
        app:layout_constraintStart_toEndOf="@+id/current"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/total"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:text="00:00"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/text_body_small"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/progress"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/progress"
        app:layout_constraintTop_toTopOf="@+id/progress"
        tools:ignore="HardcodedText"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivChangeScreenOrientation"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:layout_marginBottom="@dimen/base_sw_dp_12"
        android:background="@drawable/svg_change_screen_orientation"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@+id/progress"
        app:layout_constraintEnd_toStartOf="@+id/tvVideoSpeed"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVideoSpeed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:layout_marginBottom="@dimen/base_sw_dp_12"
        android:background="@drawable/shape_20ffffff_4r_tb3_lr8_bg"
        android:text="倍数"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/text_body_footnote"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@+id/progress"
        app:layout_constraintEnd_toEndOf="parent"
        tools:ignore="HardcodedText" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPlayCover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:visibility="gone"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPlay"
        android:layout_width="@dimen/base_sw_dp_48"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_gravity="center_vertical"
        android:background="@drawable/svg_wb_play"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>
