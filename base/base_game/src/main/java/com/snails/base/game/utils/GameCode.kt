package com.snails.base.game.utils

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年09月24日 17:21:27
 */
enum class GameCode(val code: Int, val description: String) {
    SUCCESS(200, "succeed"),
    METHOD_NOT_FOUND(40004, "方法不存在"),
    METHOD_LENGTH_ERROR(40001, "方法参数数量错误"),
    METHOD_TYPE_ERROR(40002, "方法参数类型错误"),
    REQUEST_DATA_ERROR(40003, "请求数据解析错误"),
    GET_DATA_FAILED(40006, "数据获取失败"),
    OBJECT_DEAD(40007, "对象已被回收");

    // 通过 code 获取对应的枚举
    companion object {
        fun fromCode(code: Int): GameCode? {
            return entries.find { it.code == code }
        }
    }
}