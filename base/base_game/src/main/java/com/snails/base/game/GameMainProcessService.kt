package com.snails.base.game

import android.app.Service
import android.content.Intent
import android.os.IBinder
import com.snails.base.game.bean.GameRequestDataInfo
import com.snails.base.game.bean.ProtocolType
import com.snails.base.game.utils.GameCode
import com.snails.base.game.utils.dataDeadError
import com.snails.base.game.utils.dataParsing
import com.snails.base.game.utils.dataParsingError
import com.snails.base.game.utils.handleReturnValues

/**
 * @Description 游戏主进程服务，用于和游戏进程进行通信
 * <AUTHOR>
 * @CreateTime 2024年09月26日 14:48:27
 */
class GameMainProcessService : Service() {

    override fun onBind(intent: Intent?): IBinder {
        return GameBinder()
    }

    inner class GameBinder : GameCallClientController.Stub() {

        private var clientCallGameController: ClientCallGameController? = null

        override fun gameCallClient(data: String): String {
            if (isBinderAlive) {
                dataParsing(data)?.let { info ->
                    if (info is GameRequestDataInfo) {
                        val map = mutableMapOf<String, Any>()
                        val result = GameMethodManager.invokeMethod(info.method, info.parameters)
                        if (result?.isNotEmpty() == true) {
                            map["data"] = result
                            map["code"] = GameCode.SUCCESS.code
                            map["message"] = GameCode.SUCCESS.description
                        } else {
                            map["code"] = GameCode.GET_DATA_FAILED.code
                            map["message"] = GameCode.GET_DATA_FAILED.description
                        }
                        map["eventId"] = info.eventId ?: ""
                        return handleReturnValues(ProtocolType.RESPONSE, map)
                    }
                }
                return dataParsingError(ProtocolType.RESPONSE)
            }
            return dataDeadError(ProtocolType.RESPONSE)
        }

        override fun gameCallClientAsync(data: String) {
            if (isBinderAlive) {
                dataParsing(data)?.let { info ->
                    if (info is GameRequestDataInfo) {
                        val map = mutableMapOf<String, Any>()
                        val result = GameMethodManager.invokeMethod(info.method, info.parameters)
                        if (result?.isNotEmpty() == true) {
                            map["data"] = result
                            map["code"] = GameCode.SUCCESS.code
                            map["message"] = GameCode.SUCCESS.description
                        } else {
                            map["code"] = GameCode.GET_DATA_FAILED.code
                            map["message"] = GameCode.GET_DATA_FAILED.description
                        }
                        map["eventId"] = info.eventId ?: ""
                        clientCallGameController?.clientCallGameAsync(
                            handleReturnValues(ProtocolType.RESPONSE, map)
                        )
                    }
                }
                clientCallGameController?.clientCallGameAsync(dataParsingError(ProtocolType.RESPONSE))
            }
        }

        override fun setClientCallGameController(controller: ClientCallGameController?) {
            clientCallGameController = controller
            GameMethodManager.setClientCallGameController(controller)
        }
    }

    override fun onUnbind(intent: Intent?): Boolean {
        return super.onUnbind(intent)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return super.onStartCommand(intent, flags, startId)
    }

    override fun onRebind(intent: Intent?) {
        super.onRebind(intent)
    }
}