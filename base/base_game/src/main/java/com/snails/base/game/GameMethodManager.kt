package com.snails.base.game

import com.blankj.utilcode.util.GsonUtils
import com.google.gson.reflect.TypeToken
import com.snails.base.game.annotation.GameMethod
import com.snails.base.game.bean.GameProtocolInfo
import com.snails.base.game.bean.ProtocolType
import com.snails.base.game.utils.getGson
import kotlin.reflect.full.declaredMemberFunctions

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年09月25日 16:21:00
 */
object GameMethodManager {

    private var clientCallGameController: ClientCallGameController? = null

    private val map = mutableMapOf<String, MethodSignatureInfo>()

    fun setClientCallGameController(gameController: ClientCallGameController? = null) {
        this.clientCallGameController = gameController
    }

    fun addGameBridgeMethod(bridge: GameBridge) {
        // 获取bridge的KClass
        val kClass = bridge::class
        // 遍历类中声明的所有函数
        for (function in kClass.declaredMemberFunctions) {
            if (function.annotations.any { it is GameMethod }) {
                val methodName = function.name
                val parameterNames = mutableListOf<String>()
                function.parameters.forEach { parameter ->
                    val name = parameter.name
                    if (name?.isNotEmpty() == true) {
                        parameterNames.add(name)
                    }
                }
                val info = MethodSignatureInfo(bridge, function.parameters.size, parameterNames)
                map[methodName] = info
            }
        }
    }

    fun invokeMethod(method: String, parameters: Map<String, Any>?): Map<String, Any>? {
        kotlin.runCatching {
            // 获取类的 KClass
            val method2 = map[method]
            if (method2 != null) {
                val kClass = method2.clz::class.java
                // 查找要调用的方法
                val function = kClass.declaredMethods.find { it.name == method }

                if (function != null) {
                    // 确保方法可以被访问
                    function.isAccessible = true

                    // 准备参数列表，确保按正确的顺序传递参数
                    val sortedArgs = method2.parameterNames.map { parameter ->
                        parameters?.get(parameter)
                    }

                    // 动态调用方法
                    val result =
                        function.invoke(method2.clz, *sortedArgs.toTypedArray()) ?: return null
                    val type = object : TypeToken<Map<String, Any>>() {}.type
                    val parametersMap: Map<String, Any> =
                        GsonUtils.fromJson(result.toString(), type)
                    return parametersMap
                }
            }
        }
        return null
    }

    fun callGameMethod(data: Map<String, Any>, methodName: String) {
        try {
            if (methodName.isEmpty()) {
                return
            }
            val map = mutableMapOf<String, Any>()
            map["parameters"] = data
            map["method"] = methodName
            val gameProtocolInfo = GameProtocolInfo(
                type = ProtocolType.REQUEST.name,
                content = map
            )
            val protocol = getGson().toJson(gameProtocolInfo)
            clientCallGameController?.clientCallGame(protocol)
        } catch (t: Throwable) {
            t.printStackTrace()
        }
    }
}