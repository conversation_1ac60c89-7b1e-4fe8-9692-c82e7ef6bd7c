package com.snails.base.game.ext

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.os.Environment
import android.os.StatFs
import android.text.format.Formatter
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.PathUtils
import com.blankj.utilcode.util.Utils
import com.snails.base.game.bean.GameProtocolInfo
import com.snails.base.game.bean.ProtocolType
import com.snails.base.network.repository.storage.AppSettingStorage
import com.snails.base.network.repository.storage.UserStorage
import java.util.Arrays

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年09月27日 15:17:05
 */

val deviceInfo = collectionDeviceInfo()

/**
 * 启动游戏
 * @param data 数据
 */
fun lunchGame(data: Map<String, Any>, methodName: String): String {
    val map = mutableMapOf<String, Any>()
    map["parameters"] = data
    map["method"] = methodName
    val gameProtocolInfo = GameProtocolInfo(
        type = ProtocolType.REQUEST.name,
        content = map
    )
    return GsonUtils.toJson(gameProtocolInfo)
}

/**
 * 启动游戏，并调用游戏initStudyData()方法传递参数
 * @param route 游戏路由
 */
fun lunchGameAndInitStudyData(route: String): String {
    val map = extractUrlParams(route).toMutableMap()
    map["eventId"] = "${System.currentTimeMillis()}"
    map["sandboxPath"] = PathUtils.getInternalAppFilesPath()
    map["userId"] = UserStorage.me.getUserInfo()?.uid ?: ""
    map["videoRecordType"] = AppSettingStorage.me.getReallyVideoRecordType() //DEFAULT、APP
    map["otherInfo"] = deviceInfo
    return lunchGame(map, "initStudyData")
}

private fun extractUrlParams(url: String): Map<String, Any> {
    // 获取 URL 中 "?" 之后的参数部分
    val query = url.substringAfter("?", "")
    if (query.isEmpty()) return emptyMap()

    // 使用 "&" 分割每个参数对
    return query.split("&").associate { param ->
        val (key, value) = param.split("=")
        key to value
    }
}

/**
 * 收集设备类型、OS本版、线程名、前后台、使用时长、App版本、升级渠道,CPU架构、内存信息、存储信息、permission权限
 */
@SuppressLint("WrongConstant")
private fun collectionDeviceInfo(): String {
    val sb = StringBuilder()
    try {
        sb.append("brand=${Build.BRAND} ")//huawei,xiaomi
        sb.append("rom=${Build.MODEL} ")
        sb.append("os=${Build.VERSION.RELEASE} ")//9/0
        sb.append("sdk=${Build.VERSION.SDK_INT} ")//28
        sb.append("cpu_arch=${Build.CPU_ABI} ")//armv7 armv8

        val context = Utils.getApp()
        //app 信息
        val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
        sb.append("version_code=${packageInfo.versionCode} ")
        sb.append("version_name=${packageInfo.versionName} ")
        sb.append("package_name=${packageInfo.packageName} ")
        sb.append("requested_permission=${packageInfo.requestedPermissions.contentToString()} ")//已申请到那些权限

        //统计一波存储空间
        val memInfo = android.app.ActivityManager.MemoryInfo()
        val ams = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
        ams.getMemoryInfo(memInfo)
        sb.append("availMem=${Formatter.formatFileSize(context, memInfo.availMem)} ")//可用内存
        sb.append("totalMem=${Formatter.formatFileSize(context, memInfo.totalMem)} ")//设备总内存

        val file = Environment.getExternalStorageDirectory()

        val statFs = StatFs(file.path)
        val availableSize = statFs.availableBlocks * statFs.blockSize
        sb.append(
            "availStorage=${
                Formatter.formatFileSize(
                    context,
                    availableSize.toLong()
                )
            } "
        )//存储空间
        return sb.toString()
    } catch (t: Throwable) {
        sb.append(t.message)
        t.printStackTrace()
    }
    return sb.toString()
}


