package com.snails.base.game.bean

/**
 * 客户端、游戏通信协议
 */
data class GameProtocolNewInfo(
    var type: String, //请求类型。REQUEST：请求，RESPONSE：响应
    var content: GameContentInfo, //content：请求或者响应的数据，map来接收content，需要通过type进行判断。
)

data class GameContentInfo(
    val eventId: String? = null,
    val method: String? = null,
    val parameters: ParametersInfo?
)

data class ParametersInfo(val callBackName: String? = null, val path: String? = null)