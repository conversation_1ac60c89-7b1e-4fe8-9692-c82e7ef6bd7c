package com.snails.base.game.utils

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.ToNumberPolicy
import com.snails.base.game.bean.GameProtocol
import com.snails.base.game.bean.GameProtocolInfo
import com.snails.base.game.bean.GameRequestDataInfo
import com.snails.base.game.bean.GameResponseDataInfo
import com.snails.base.game.bean.ProtocolType

/**
 * @Description 解析游戏传递过来的参数，解析为 GameRequestInfo 对象
 * <AUTHOR>
 * @CreateTime 2024年09月24日 17:13:01
 */

fun getGson(): Gson {
    val gsonBuilder = GsonBuilder()
    gsonBuilder.setObjectToNumberStrategy(ToNumberPolicy.LAZILY_PARSED_NUMBER)
    return gsonBuilder.create()
}

fun dataParsing(protocol: String): GameProtocol? {
    runCatching {
        val gameProtocolInfo = getGson().fromJson(protocol, GameProtocolInfo::class.java)
        if (gameProtocolInfo.type == "REQUEST") {
            return parseRequestData(gameProtocolInfo.content)
        }
        return parseResponseData(gameProtocolInfo.content)
    }
    return null
}

/**
 * 解析请求数据
 * {
 *   "eventId":"请求事件唯一 id",
 *   "type":"REQUEST",
 *   "content":"{\"method\":\"openCamera\",\"parameters\":{\"parameter1\":\"参数 1\",\"parameter2\":\"2\",\"parameter3\":true}}"
 * }
 */
private fun parseRequestData(content: Map<String, Any>?): GameRequestDataInfo? {
    try {
        val data = content ?: return null
        val gson = getGson()
        val gameRequestInfo = gson.fromJson(gson.toJson(data), GameRequestDataInfo::class.java)
        return gameRequestInfo
    } catch (t: Throwable) {
        t.printStackTrace()
    }
    return null
}

/**
 * 解析响应数据
 * {
 *   "eventId":"请求事件唯一 id",
 *   "type":"RESPONSE",
 *   "content":"{\"data\":{},\"code\":200,\"message\":\"succeed\"}"
 * }
 */
private fun parseResponseData(content: Map<String, Any>?): GameResponseDataInfo? {
    runCatching {
        val data = content ?: return null
        val gson = getGson()
        val gameRequestInfo = gson.fromJson(gson.toJson(data), GameResponseDataInfo::class.java)
        return gameRequestInfo
    }
    return null
}

fun dataParsingError(type: ProtocolType): String {
    val map = mutableMapOf<String, Any>()
    map["code"] = GameCode.REQUEST_DATA_ERROR.code //40003
    map["msg"] = GameCode.REQUEST_DATA_ERROR.description //请求数据解析错误
    return handleReturnValues(type, map)
}

fun dataDeadError(type: ProtocolType): String {
    val map = mutableMapOf<String, Any>()
    map["code"] = GameCode.OBJECT_DEAD.code //40003
    map["msg"] = GameCode.OBJECT_DEAD.description //请求数据解析错误
    return handleReturnValues(type, map)
}

fun handleReturnValues(type: ProtocolType, map: Map<String, Any>): String {
    val gameProtocolInfo = GameProtocolInfo(
        type = type.name,
        content = map
    )
    return getGson().toJson(gameProtocolInfo)
}