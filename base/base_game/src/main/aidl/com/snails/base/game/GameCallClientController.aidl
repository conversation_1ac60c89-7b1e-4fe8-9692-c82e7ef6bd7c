package com.snails.base.game;
import com.snails.base.game.ClientCallGameController;
/**
* 游戏调用客户端的接口
*/
interface GameCallClientController {

    /**
    * 游戏调用客户端的接口,同步方法
    */
    String gameCallClient(String data);

     /**
     * 游戏调用客户端的接口,异步方法
     */
    void gameCallClientAsync(String data);

    /**
     * 设置客户端调用游戏的控制器
     */
    void setClientCallGameController(in ClientCallGameController controller);
}