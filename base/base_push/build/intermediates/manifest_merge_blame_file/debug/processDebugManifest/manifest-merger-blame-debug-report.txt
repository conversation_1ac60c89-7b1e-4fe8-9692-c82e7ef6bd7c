1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.snails.base.push" >
4
5    <uses-sdk android:minSdkVersion="29" />
6
7    <application>
7-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:4:5-45:19
8
9        <!-- 阿里推送 -->
10        <meta-data
10-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:6:9-8:41
11            android:name="com.alibaba.app.appkey"
11-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:7:13-50
12            android:value="335493147" />
12-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:8:13-38
13        <meta-data
13-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:9:9-11:64
14            android:name="com.alibaba.app.appsecret"
14-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:10:13-53
15            android:value="7f5aa14cbc4c4aaa9e3012ca72768b91" />
15-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:11:13-61
16
17        <!-- 厂商推送推送-华为 -->
18        <meta-data
18-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:14:9-16:41
19            android:name="com.huawei.hms.client.appid"
19-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:15:13-55
20            android:value="113695331" />
20-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:16:13-38
21
22        <!-- 厂商推送推送-荣耀 -->
23        <meta-data
23-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:19:9-21:41
24            android:name="com.hihonor.push.app_id"
24-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:20:13-51
25            android:value="900909625" />
25-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:21:13-38
26
27        <!-- 厂商推送推送-vivo -->
28        <meta-data
28-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:24:9-26:64
29            android:name="com.vivo.push.api_key"
29-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:25:13-49
30            android:value="e70b1122e91e23e6b3f07211ffde7825" />
30-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:26:13-61
31        <meta-data
31-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:27:9-29:41
32            android:name="com.vivo.push.app_id"
32-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:28:13-48
33            android:value="105875745" />
33-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:29:13-38
34
35        <service
35-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:31:9-44:19
36            android:name="com.snails.base.push.service.PushMessageService"
36-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:32:13-55
37            android:exported="false"
37-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:33:13-37
38            android:process="@string/aliyun_accs_target_process" >
38-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:34:13-65
39            <intent-filter>
39-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:35:13-37:29
40                <action android:name="com.alibaba.push2.action.NOTIFICATION_OPENED" />
40-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:36:17-87
40-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:36:25-84
41            </intent-filter>
42            <intent-filter>
42-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:38:13-40:29
43                <action android:name="com.alibaba.push2.action.NOTIFICATION_REMOVED" />
43-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:39:17-88
43-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:39:25-85
44            </intent-filter>
45            <intent-filter>
45-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:41:13-43:29
46                <action android:name="com.alibaba.sdk.android.push.RECEIVE" />
46-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:42:17-79
46-->/Users/<USER>/CodeRepo/WorkCode2/SnailComing/base/base_push/src/main/AndroidManifest.xml:42:25-76
47            </intent-filter>
48        </service>
49    </application>
50
51</manifest>
