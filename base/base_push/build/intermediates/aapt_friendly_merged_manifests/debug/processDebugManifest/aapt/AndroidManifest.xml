<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.snails.base.push" >

    <uses-sdk android:minSdkVersion="29" />

    <application>

        <!-- 阿里推送 -->
        <meta-data
            android:name="com.alibaba.app.appkey"
            android:value="335493147" />
        <meta-data
            android:name="com.alibaba.app.appsecret"
            android:value="7f5aa14cbc4c4aaa9e3012ca72768b91" />

        <!-- 厂商推送推送-华为 -->
        <meta-data
            android:name="com.huawei.hms.client.appid"
            android:value="113695331" />

        <!-- 厂商推送推送-荣耀 -->
        <meta-data
            android:name="com.hihonor.push.app_id"
            android:value="900909625" />

        <!-- 厂商推送推送-vivo -->
        <meta-data
            android:name="com.vivo.push.api_key"
            android:value="e70b1122e91e23e6b3f07211ffde7825" />
        <meta-data
            android:name="com.vivo.push.app_id"
            android:value="105875745" />

        <service
            android:name="com.snails.base.push.service.PushMessageService"
            android:exported="false"
            android:process="@string/aliyun_accs_target_process" >
            <intent-filter>
                <action android:name="com.alibaba.push2.action.NOTIFICATION_OPENED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.alibaba.push2.action.NOTIFICATION_REMOVED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.alibaba.sdk.android.push.RECEIVE" />
            </intent-filter>
        </service>
    </application>

</manifest>