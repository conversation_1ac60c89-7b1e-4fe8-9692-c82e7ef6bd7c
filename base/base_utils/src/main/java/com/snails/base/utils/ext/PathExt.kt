package com.snails.base.utils.ext

import com.snails.base.utils.constants.AppConstants
import java.io.File

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月08日 09:50:31
 */

val downloadPath: String = AppConstants.getDownloadPath()

fun String.getRealPath(): String {
    try {
        if (this.isEmpty()) {
            return this
        }
        val fileName = if (this.contains("?")) {
            this.substringBefore("?").substringAfterLast("/")
        } else {
            this.substringAfterLast("/")
        }
        val file = File(downloadPath, fileName)
        if (file.exists()) {
            return file.path
        }
        return this
    } catch (t: Throwable) {
        t.printStackTrace()
    }
    return this
}

fun String.getFileName(): String {
    return if (this.contains("?")) {
        this.substringBefore("?").substringAfterLast("/")
    } else {
        this.substringAfterLast("/")
    }
}