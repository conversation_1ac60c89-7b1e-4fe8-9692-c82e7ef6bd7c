package com.snails.base.utils.utils

import com.blankj.utilcode.util.ThreadUtils
import java.util.Timer
import java.util.TimerTask

/**
 * @Description 定时管理器
 * <AUTHOR>
 * @CreateTime 2024年11月13日 16:19:21
 */
class TimerTaskManager {
    private var timer: Timer? = null
    private var timerTask: TimerTask? = null
    private var remainingTimeMillis = 0L
    var timerListener: TimerListener? = null

    // 开始计时
    fun start(minutes: Int) {
        if (minutes < 0) {
            return
        }
        remainingTimeMillis = minutes * 60 * 1000L
        stop() // 确保上次的计时器已停止
        timer = Timer()
        timerTask = object : TimerTask() {
            override fun run() {
                if (remainingTimeMillis > 0) {
                    remainingTimeMillis -= 1000
                    ThreadUtils.runOnUiThread {
                        timerListener?.onTick(remainingTimeMillis)
                    }
                } else {
                    ThreadUtils.runOnUiThread {
                        timerListener?.onFinish()
                    }
                    stop() // 计时完成后停止
                }
            }
        }
        timer?.schedule(timerTask, 0, 1000)
    }

    // 停止计时器
    fun stop() {
        timerTask?.cancel()
        timer?.cancel()
        timerTask = null
        timer = null
    }

    // 释放计时器
    fun release() {
        stop()
    }

    // 可动态重设分钟数
    fun resetMinutes(newMinutes: Int) {
        remainingTimeMillis = newMinutes * 60 * 1000L
    }

    interface TimerListener {
        fun onTick(time: Long)
        fun onFinish()
    }
}