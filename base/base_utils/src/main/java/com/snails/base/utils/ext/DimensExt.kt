package com.snails.base.utils.ext

import android.content.Context

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年10月29日 23:04:03
 */

fun Context.getDimens(size: Int?): Int? {
    val resourceName = "base_sw_dp_$size"
    // 获取 dimen 资源 ID
    val resourceId = this.resources.getIdentifier(resourceName, "dimen", this.packageName)

    return if (resourceId != 0) {
        // 如果资源 ID 存在，读取对应的像素值
        this.resources.getDimension(resourceId).toInt()
        // 使用 dimensionValue，这里返回的值是 px
    } else {
        null
    }
}