package com.snails.base.utils.ext

import android.annotation.SuppressLint
import java.security.MessageDigest


inline fun <reified T> Any.saveAs(): T {
    return this as T
}

@Suppress("UNCHECKED_CAST")
fun <T> Any.saveAsUnChecked(): T {
    return this as T
}

inline fun <reified T> Any.isEqualType(): Bo<PERSON>an {
    return this is T
}

// 递归提取以.mp3或.png结尾的String字段
fun Any.extractMediaFilesFromObject(onlyMp3: Boolean = false): Set<String> {
    val result = mutableSetOf<String>()
    val visitedObjects = mutableSetOf<Any>() // 用于记录已访问的对象，避免死循环

    fun extract(item: Any?) {
        when (item) {
            is String -> {
                if (onlyMp3) {
                    if (item.lowercase().contains(".mp3")) {
                        result.add(item)
                    }
                } else {
                    if (item.lowercase().contains(".mp3")
                        || item.lowercase().contains(".mp4")
                        || item.lowercase().contains(".wav")
                        || item.lowercase().contains(".png")
                        || item.lowercase().contains(".jpg")
                        || item.lowercase().contains(".jpeg")
                        || item.lowercase().contains(".json")
                    ) {
                        result.add(item)
                    }
                }
            }

            is List<*> -> {
                item.forEach { nestedItem -> extract(nestedItem) }
            }

            is Map<*, *> -> {
                item.values.forEach { nestedItem -> extract(nestedItem) }
            }

            else -> {
                // 如果是对象，使用反射遍历其字段
                if (item != null && item !in visitedObjects) {
                    visitedObjects.add(item) // 将当前对象添加到已访问集合
                    item.javaClass.declaredFields.forEach { field ->
                        field.isAccessible = true
                        val value = field.get(item)
                        extract(value)
                    }
                }
            }
        }
    }

    extract(this)
    return result
}

fun List<String>.toUniqueHash(algorithm: String = "MD5"): String {
    val list = mutableListOf<String>()
    this.sorted().forEach {
        list.add(it.split(".mp3?")[0])
    }
    // 将文件名排序以确保顺序一致
    val sortedFiles = list.joinToString("")
    // 创建哈希
    val digest = MessageDigest.getInstance(algorithm)
    val hashBytes = digest.digest(sortedFiles.toByteArray())
    // 转换为16进制字符串
    return hashBytes.joinToString("") { "%02x".format(it) }
}

const val megabyte = 1024 * 1024

@SuppressLint("DefaultLocale")
fun Long.bytesToMegabytesFormatted(): Float {
    val mb = this.toDouble() / megabyte
    return String.format("%.1f", mb).toFloat() // 保留两位小数
}

