package com.snails.base.utils.ext

import android.os.Bundle
import androidx.annotation.IdRes
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import androidx.navigation.Navigator

fun NavController.safeNavigate(
    @IdRes resId: Int,
    args: Bundle? = null,
    navOptions: NavOptions? = null,
    navigatorExtras: Navigator.Extras? = null
) {
    runCatching {
        val action = currentDestination?.getAction(resId) ?: graph.getAction(resId)
        if (action != null) {
            navigate(resId, args, navOptions, navigatorExtras)
        }
    }
}