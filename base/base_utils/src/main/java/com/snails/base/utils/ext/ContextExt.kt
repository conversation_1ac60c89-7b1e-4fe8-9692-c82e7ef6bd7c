package com.snails.base.utils.ext

import android.content.Context
import com.snails.base.utils.R

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年12月31日 10:49:11
 */

private const val SCREEN_WIDTH_DP = 460

/**
 * 是否是竖屏pad模式
 * 判断屏幕最短的一边，如果大于等于 460dp。则认为是平板
 */
fun Context?.isPortraitPadMode(): Bo<PERSON>an {
    val ctx = this ?: return false
    ctx.resources?.displayMetrics?.also { displayMetrics ->
        val screenWidthDp = displayMetrics.widthPixels / displayMetrics.density
        val screenHeightDp = displayMetrics.heightPixels / displayMetrics.density
        //获取短的一边
        val minLengthDp = screenHeightDp.coerceAtMost(screenWidthDp)
        if (minLengthDp >= SCREEN_WIDTH_DP) {
            return true
        }
    }
    return false
}

/**
 * 根据是否是 竖屏pad模式 设置边距是16 or 24
 */
fun Context?.commonMargin16Or24(): Int {
    this ?: return 0
    if (isPortraitPadMode()) {
        return this.resources.getDimension(R.dimen.base_sw_dp_24).toInt()
    }
    return this.resources.getDimension(R.dimen.base_sw_dp_16).toInt()
}

 fun Context?.showItemSize(): Int {
    if (this.isPortraitPadMode()) {
        return 5
    }
    return 3
}