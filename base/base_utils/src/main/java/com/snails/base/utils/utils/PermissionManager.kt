package com.snails.base.utils.utils


import android.content.Context
import android.content.pm.PackageManager
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import java.util.concurrent.ConcurrentHashMap

/**
 * 权限管理类，支持Android 8.0及以上版本的权限检测和申请
 */
object PermissionManager {
    // 存储权限请求回调
    private val permissionCallbacks = ConcurrentHashMap<Int, PermissionCallback>()
    private var requestCodeCounter = 0

    /**
     * 权限请求回调接口
     */
    interface PermissionCallback {
        fun onPermissionsResult(grantedPermissions: List<String>, deniedPermissions: List<String>)
    }

    /**
     * 检查单个权限是否被授予
     * @param context 上下文
     * @param permission 要检查的权限
     * @return 是否授予
     */
    fun isPermissionGranted(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            permission
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 批量检查权限是否被授予
     * @param context 上下文
     * @param permissions 要检查的权限列表
     * @return Pair<授予的权限, 未授予的权限>
     */
    fun checkPermissions(
        context: Context,
        permissions: Array<String>
    ): Pair<List<String>, List<String>> {
        val granted = mutableListOf<String>()
        val denied = mutableListOf<String>()

        permissions.forEach { permission ->
            if (isPermissionGranted(context, permission)) {
                granted.add(permission)
            } else {
                denied.add(permission)
            }
        }
        return Pair(granted, denied)
    }

    /**
     * 检查权限是否被用户拒绝且选择了“不再询问”
     * @param activity FragmentActivity
     * @param permission 要检查的权限
     * @return 是否被拒绝且不再询问
     */
    fun isPermissionPermanentlyDenied(activity: FragmentActivity, permission: String): Boolean {
        return !isPermissionGranted(activity, permission) &&
                !activity.shouldShowRequestPermissionRationale(permission)
    }

    /**
     * 批量检查权限是否被永久拒绝
     * @param activity FragmentActivity
     * @param permissions 要检查的权限列表
     * @return 包含被永久拒绝的权限列表
     */
    fun getPermanentlyDeniedPermissions(
        activity: FragmentActivity,
        permissions: Array<String>
    ): Boolean {
        val list = permissions.filter { isPermissionPermanentlyDenied(activity, it) }
        return list.isNotEmpty()
    }

    /**
     * 请求单个权限
     * @param activity FragmentActivity
     * @param permission 要请求的权限
     * @param callback 权限请求结果回调
     */
    fun requestPermission(
        activity: FragmentActivity,
        permission: String,
        callback: PermissionCallback
    ) {
        requestPermissions(activity, arrayOf(permission), callback)
    }

    /**
     * 批量请求权限
     * @param activity FragmentActivity
     * @param permissions 要请求的权限列表
     * @param callback 权限请求结果回调
     */
    fun requestPermissions(
        activity: FragmentActivity,
        permissions: Array<String>,
        callback: PermissionCallback
    ) {
        // 如果所有权限都已授予，直接回调
        val (granted, denied) = checkPermissions(activity, permissions)
        if (denied.isEmpty()) {
            callback.onPermissionsResult(granted, emptyList())
            return
        }

        // 生成唯一的请求码
        val requestCode = requestCodeCounter++
        permissionCallbacks[requestCode] = callback

        // 使用 ActivityResult API 发起权限请求
        val launcher = activity.registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { result ->
            val grantedPermissions = mutableListOf<String>()
            val deniedPermissions = mutableListOf<String>()

            result.forEach { (permission, isGranted) ->
                if (isGranted) {
                    grantedPermissions.add(permission)
                } else {
                    deniedPermissions.add(permission)
                }
            }

            // 回调结果并清理
            permissionCallbacks[requestCode]?.onPermissionsResult(
                grantedPermissions,
                deniedPermissions
            )
            permissionCallbacks.remove(requestCode)
        }

        // 发起权限请求
        launcher.launch(permissions)
    }

    /**
     * Fragment 版本的权限请求
     * @param fragment Fragment
     * @param permissions 要请求的权限列表
     * @param callback 权限请求结果回调
     */
    fun requestPermissionsFromFragment(
        fragment: Fragment,
        permissions: Array<String>,
        callback: PermissionCallback
    ) {
        // 如果所有权限都已授予，直接回调
        val (granted, denied) = checkPermissions(fragment.requireContext(), permissions)
        if (denied.isEmpty()) {
            callback.onPermissionsResult(granted, emptyList())
            return
        }

        // 生成唯一的请求码
        val requestCode = requestCodeCounter++
        permissionCallbacks[requestCode] = callback

        // 使用 ActivityResult API 发起权限请求
        val launcher = fragment.registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { result ->
            val grantedPermissions = mutableListOf<String>()
            val deniedPermissions = mutableListOf<String>()

            result.forEach { (permission, isGranted) ->
                if (isGranted) {
                    grantedPermissions.add(permission)
                } else {
                    deniedPermissions.add(permission)
                }
            }

            // 回调结果并清理
            permissionCallbacks[requestCode]?.onPermissionsResult(
                grantedPermissions,
                deniedPermissions
            )
            permissionCallbacks.remove(requestCode)
        }

        // 发起权限请求
        launcher.launch(permissions)
    }
}