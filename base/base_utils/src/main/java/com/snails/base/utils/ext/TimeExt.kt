package com.snails.base.utils.ext

import android.annotation.SuppressLint
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.Locale

//秒的long时间转化
fun Long.getDay(): String = (this / (3600 * 24)).asTwoDigit()
fun Long.getHour(): String = (this % (3600 * 24) / 3600).asTwoDigit()
fun Long.getMin(): String = (this % (3600 * 24) % 3600 / 60).asTwoDigit()
fun Long.getSec(): String = (this % (3600 * 24) % 3600 % 60).asTwoDigit()

fun Long.formattedTime(keepHours: Boolean = false): String {
    val seconds = this
    val h = seconds / 3600
    val m = (seconds % 3600) / 60
    val s = (seconds % 3600) % 60

    return if (!keepHours && h == 0L) {
        "${m.asTwoDigit()}:${s.asTwoDigit()}"
    } else {
        "${h.asTwoDigit()}:${m.asTwoDigit()}:${s.asTwoDigit()}"
    }
}

fun Long.asTwoDigit(): String {
    val value = StringBuilder()
    if (this < 10) {
        value.append("0")
    }
    value.append(toString())
    return value.toString()
}

@SuppressLint("DefaultLocale")
fun Long.formatTime(): String {
    val minutes = this / 60
    val secs = this % 60
    return String.format("%02d:%02d", minutes, secs)
}

fun String.toCustomFormat(): String {
    // 定义输入和输出的日期格式
    val inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    val outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")

    // 解析输入日期字符串并转换为所需格式
    return try {
        val dateTime = LocalDateTime.parse(this.split(".")[0], inputFormatter)
        dateTime.format(outputFormatter)
    } catch (e: Exception) {
        // 如果解析失败，返回空字符串或错误提示
        "0000-00-00 00:00"
    }
}

// 扩展函数
fun String.toCustomFormatStr(): String {
    // 原始格式的解析器
    val inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    // 目标格式的格式化器
    val outputFormatter = DateTimeFormatter.ofPattern("MM月dd日 HH:mm:ss")

    // 将字符串解析为 LocalDateTime，再格式化为目标字符串
    val dateTime = LocalDateTime.parse(this.split(".")[0], inputFormatter)
    return dateTime.format(outputFormatter)
}

fun String?.toFormattedDate(): String {
    if (this.isNullOrEmpty()) return "0000-00-00 00:00" // 异常处理：空值或无效时间戳

    return try {
        // 尝试解析时间戳
        val date = Date(this.toLong()) // 转换为时间戳的 Long 类型
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        dateFormat.format(date)
    } catch (e: Exception) {
        "0000-00-00 00:00" // 异常处理：无法解析的时间戳
    }
}

fun Long?.toTimeFormat(): String {
    try {
        if (this == null) {
            return "00:00:00"
        }
        // 计算小时、分钟、秒
        val hours = this / 3600
        val minutes = (this % 3600) / 60
        val seconds = this % 60

        // 使用 String.format 格式化为 HH:mm:ss
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    } catch (t: Throwable) {
        t.printStackTrace()
    }
    return "00:00:00"
}

fun String.toDateFormat(): String {
    // 定义输入格式
    val inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    // 解析日期
    val date = LocalDate.parse(this, inputFormatter)
    // 定义输出格式
    val outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
    // 返回格式化后的日期
    return date.format(outputFormatter)
}