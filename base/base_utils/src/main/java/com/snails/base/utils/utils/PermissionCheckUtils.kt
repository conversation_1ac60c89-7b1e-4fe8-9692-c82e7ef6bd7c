package com.snails.base.utils.utils

import android.content.Context
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions

/**
 * @Description 权限检查Utils
 * <AUTHOR>
 * @CreateTime 2025年03月17日 16:44:46
 */
object PermissionCheckUtils {

    fun havaPermission(context: Context?, name: String): <PERSON><PERSON><PERSON> {
        val ctx = context ?: return false
        return XXPermissions.isGranted(ctx, name)
    }

    fun havaRecord(context: Context?): <PERSON><PERSON>an {
        val ctx = context ?: return false
        return XXPermissions.isGranted(ctx, Permission.RECORD_AUDIO)
    }

    fun havaPhoto(context: Context?): <PERSON><PERSON><PERSON> {
        val ctx = context ?: return false
        return XXPermissions.isGranted(ctx, Permission.READ_MEDIA_IMAGES)
    }

    fun havaCamera(context: Context?): <PERSON><PERSON><PERSON> {
        val ctx = context ?: return false
        return XXPermissions.isGranted(ctx, Permission.CAMERA)
    }
}