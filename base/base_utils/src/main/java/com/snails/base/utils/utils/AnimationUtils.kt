package com.snails.base.utils.utils

import android.animation.ObjectAnimator
import android.view.View
import android.view.animation.Animation
import android.view.animation.LinearInterpolator

/**
 * @Description APP 级别的一些常量
 * <AUTHOR>
 * @CreateTime 2024年08月14日 17:04:07
 */
class AnimationUtils private constructor() {

    companion object {
        fun createAnimation() = AnimationUtils()
    }

    private var objectAnimator: ObjectAnimator? = null
    private var mCurrentView: View? = null

    fun startCircleAnim(view: View) {

        if (mCurrentView != view) {
            cancelCircleAnim()
            mCurrentView = view
        }

        if (objectAnimator == null) {
            objectAnimator = ObjectAnimator.ofFloat(view, "rotation", 0f, 720f)
            objectAnimator?.let {
                it.repeatCount = Animation.INFINITE
                it.interpolator = LinearInterpolator()
                it.duration = 12000
                it.start()
            }
        } else {
            objectAnimator?.let {
                if (it.isPaused) {
                    it.resume()
                }
            }
        }
    }

    fun pauseCircleAnim() {
        objectAnimator?.let {
            if (it.isRunning) {
                it.pause()
            }
        }
    }

    fun cancelCircleAnim() {
        objectAnimator?.cancel()
        objectAnimator = null
    }
}
