package com.snails.base.utils.ext

import android.graphics.Outline
import android.view.View
import android.view.ViewOutlineProvider
import android.widget.Checkable
import androidx.annotation.DimenRes
import com.blankj.utilcode.util.Utils


inline fun <T : View> T.singleClick(time: Long = 500, crossinline block: (T) -> Unit) {
    setOnClickListener {
        val currentTimeMillis = System.currentTimeMillis()
        if (currentTimeMillis - lastClickTime > time || this is Checkable) {
            lastClickTime = currentTimeMillis
            block(this)
        }
    }
}

//兼容点击事件设置为this的情况
fun <T : View> T.singleClick(onClickListener: View.OnClickListener, time: Long = 500) {
    setOnClickListener {
        val currentTimeMillis = System.currentTimeMillis()
        if (currentTimeMillis - lastClickTime > time || this is Checkable) {
            lastClickTime = currentTimeMillis
            onClickListener.onClick(this)
        }
    }
}

var <T : View> T.lastClickTime: Long
    set(value) = setTag(**********, value)
    get() = getTag(**********) as? Long ?: 0


/**
 * view 隐藏
 */
fun View.gone() {
    this.visibility = View.GONE
}

/**
 * view 显示
 */
fun View.visible() {
    this.visibility = View.VISIBLE
}

/**
 * 不显示
 */
fun View.invisible() {
    this.visibility = View.INVISIBLE
}

/**
 * 给任意 View 加圆角
 */
fun View.addRadius(@DimenRes id: Int) {
    kotlin.runCatching {
        val radius = Utils.getApp().resources.getDimension(id)
        this.post {
            this.clipToOutline = true
            this.outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View?, outline: Outline?) {
                    view ?: return
                    outline?.setRoundRect(
                        0, 0, view.width, view.height, radius
                    )
                }
            }
        }
    }
}
