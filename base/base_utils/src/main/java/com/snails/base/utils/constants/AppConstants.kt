package com.snails.base.utils.constants

import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.PathUtils

/**
 * @Description APP 级别的一些常量
 * <AUTHOR>
 * @CreateTime 2024年08月14日 17:04:07
 */
object AppConstants {

    /**
     * App 缓存日志 路径
     */
    fun getCacheLogPath(): String {
        return PathUtils.getInternalAppCachePath() + "/AppRunLog/CacheLog/"
    }

    /**
     * App 真正日志 路径
     */
    fun getLogPath(): String {
        return PathUtils.getInternalAppCachePath() + "/AppRunLog/ReallyLog/"
    }

    /**
     * App 下载保存 路径
     */
    fun getDownloadPath(): String {
        val path = PathUtils.getInternalAppCachePath() + "/download"
        FileUtils.createOrExistsDir(path)
        return path
    }

    /**
     * App 音频合并 路径
     */
    fun getMergeAudioPath(): String {
        val path = PathUtils.getInternalAppCachePath() + "/mergeAudio"
        FileUtils.createOrExistsDir(path)
        return path
    }

    /**
     * App 视频压缩保存 路径
     */
    fun getVideoCompressPath(): String {
        val path = PathUtils.getInternalAppCachePath() + "/videoCompress"
        FileUtils.createOrExistsDir(path)
        return path
    }

    /**
     * App 视频临时缓存 路径
     */
    fun getVideoTempPath(): String {
        val path = PathUtils.getInternalAppCachePath() + "/videoTemp"
        FileUtils.createOrExistsDir(path)
        return path
    }

    /**
     * APP 视频首帧保存地址
     */
    fun getVideoFirstFrameSavePath(): String {
        return PathUtils.getInternalAppCachePath() + "/VideoFirstFrame/"
    }

    /**
     * 游戏日志保存路径
     */
    fun gameLogSavePath(): String {
        return PathUtils.getInternalAppFilesPath() + "/GameLog"
    }
}