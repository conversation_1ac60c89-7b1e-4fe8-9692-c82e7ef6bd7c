package com.snails.base.utils.utils

import android.annotation.SuppressLint
import java.util.concurrent.TimeUnit

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月13日 16:51:15
 */
object TimesUtils {
    // 获取当前播放时长和音频总时长，并转换为 mm:ss 格式
    @SuppressLint("DefaultLocale")
    fun formatTime(milliseconds: Long): String {
        var time = milliseconds
        if (time < 0) {
            time = 0
        }
        val minutes = TimeUnit.MILLISECONDS.toMinutes(time)
        val seconds = TimeUnit.MILLISECONDS.toSeconds(time) % 60
        return String.format("%02d:%02d", minutes, seconds)
    }
}