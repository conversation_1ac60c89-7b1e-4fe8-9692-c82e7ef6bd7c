package com.snails.base.multi_state_page.state

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import com.snails.base.multi_state_page.MultiState
import com.snails.base.multi_state_page.MultiStateContainer

/**
 * @author: yanz
 */
class SuccessState : MultiState() {
    override fun onCreateView(
        context: Context,
        inflater: LayoutInflater,
        container: MultiStateContainer
    ): View {
        return View(context)
    }

    override fun onViewCreated(view: View) = Unit

}