package com.snails.base.multi_state_page.state

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import com.airbnb.lottie.LottieAnimationView
import com.snails.base.multi.state.page.R
import com.snails.base.multi_state_page.MultiState
import com.snails.base.multi_state_page.MultiStateContainer

class LoadingState : MultiState() {
    private lateinit var loadingLottie: LottieAnimationView
    override fun onCreateView(
        context: Context,
        inflater: LayoutInflater,
        container: MultiStateContainer,
    ): View {
        return inflater.inflate(R.layout.mult_state_loading, container, false)
    }

    override fun onViewCreated(view: View) {
        loadingLottie = view.findViewById(R.id.loadingLottie)
    }
}