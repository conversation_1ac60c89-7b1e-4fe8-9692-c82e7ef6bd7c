package com.snails.base.multi_state_page

import com.snails.base.multi_state_page.state.EmptyState
import com.snails.base.multi_state_page.state.ErrorState
import com.snails.base.multi_state_page.state.LoadingState
import com.snails.base.multi_state_page.state.SuccessState

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年03月01日 10:51:26
 */
object MultiStateFactory {
    fun createState(clazz: Class<out MultiState>): MultiState? {
        return when (clazz) {
            LoadingState::class.java -> LoadingState()
            SuccessState::class.java -> SuccessState()
            EmptyState::class.java -> EmptyState()
            ErrorState::class.java -> ErrorState()
            // 其他状态类
            else -> null
        }
    }
}