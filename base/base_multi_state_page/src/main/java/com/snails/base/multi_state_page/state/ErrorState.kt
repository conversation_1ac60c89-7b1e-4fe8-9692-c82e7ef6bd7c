package com.snails.base.multi_state_page.state

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import com.snails.base.multi.state.page.R
import com.snails.base.multi_state_page.MultiState
import com.snails.base.multi_state_page.MultiStateContainer
import com.snails.base.multi_state_page.MultiStatePage


class ErrorState : MultiState() {

    private lateinit var tvErrorMsg: TextView
    private lateinit var ivError: ImageView
    private lateinit var tvRetry: TextView

    private var retry: OnRetryClickListener? = null

    override fun onCreateView(
        context: Context, inflater: LayoutInflater, container: MultiStateContainer
    ): View {
        return inflater.inflate(R.layout.mult_state_error, container, false)
    }

    override fun onViewCreated(view: View) {
        tvErrorMsg = view.findViewById(R.id.tvErrorMsg)
        ivError = view.findViewById(R.id.ivError)
        tvRetry = view.findViewById(R.id.tvRetry)

        setErrorMsg(MultiStatePage.config.errorMsg)
        setErrorIcon(MultiStatePage.config.errorIcon)

        tvRetry.setOnClickListener { retry?.retry() }
    }

    fun setErrorMsg(errorMsg: String) {
        tvErrorMsg.text = errorMsg
    }

    fun setErrorIcon(@DrawableRes errorIcon: Int?) {
        errorIcon?.let {
            ivError.setImageResource(it)
        }
    }

    fun retry(retry: OnRetryClickListener) {
        this.retry = retry
    }

    fun interface OnRetryClickListener {
        fun retry()
    }
}