<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivError"
        android:layout_width="@dimen/base_sw_dp_200"
        android:layout_height="@dimen/base_sw_dp_160"
        android:background="@drawable/svg_common_load_error" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvErrorMsg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:gravity="center"
        android:text="@string/str_load_failed_retry"
        android:textColor="@color/text_body"
        android:textSize="@dimen/text_body_medium" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvRetry"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:background="@drawable/shape_click_retry_bg"
        android:text="@string/str_click_refresh"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/text_body_medium"
        android:textStyle="bold" />
</androidx.appcompat.widget.LinearLayoutCompat>