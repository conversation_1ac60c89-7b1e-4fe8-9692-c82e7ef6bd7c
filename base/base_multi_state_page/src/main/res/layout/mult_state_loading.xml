<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/base_sw_dp_112"
    android:layout_height="@dimen/base_sw_dp_122"
    android:layout_gravity="center"
    android:elevation="@dimen/base_sw_dp_10"
    android:gravity="center">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/loadingLottie"
        android:layout_width="@dimen/base_sw_dp_68"
        android:layout_height="@dimen/base_sw_dp_68"
        android:layout_gravity="center"
        app:lottie_autoPlay="true"
        app:lottie_fileName="lottie_loading.json"
        app:lottie_loop="true" />
</com.google.android.material.card.MaterialCardView>