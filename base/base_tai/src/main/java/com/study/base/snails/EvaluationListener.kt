package com.study.base.snails

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年10月19日 09:34:51
 */
interface EvaluationListener {
    /**
     * 评测结果
     * @param audioFile 录音文件
     */
    fun evaluationResult(result: String, audioFile: String? = null)

    /**
     * 评测出错
     */
    fun onError(error: String?)

    /**
     * 评测出错日志
     */
    fun onLog(error: String?)

    /**
     * 录音开始
     */
    fun startRecord()

    /**
     * 录音取消
     */
    fun cancelRecord()

    /**
     * 录音结束
     */
    fun stopRecord()

    /**
     * 音量
     */
    fun volumeDb(volumeDb: Float)

    /**
     * 静音
     */
    fun onVad()
}