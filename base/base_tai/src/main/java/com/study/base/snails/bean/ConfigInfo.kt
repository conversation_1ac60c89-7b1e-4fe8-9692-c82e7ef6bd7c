package com.study.base.snails.bean


import com.google.gson.annotations.SerializedName

data class ConfigInfo(
    @SerializedName("audioFile")
    val audioFile: String? = null,
    @SerializedName("connectTimeout")
    val connectTimeout: String? = null,
    @SerializedName("eval_mode")
    val evalMode: String? = null,
    @SerializedName("keyword")
    val keyword: String? = null,
    @SerializedName("ref_text")
    val refText: String? = null,
    @SerializedName("sentence_info_enabled")
    val sentenceInfoEnabled: String? = null,
    @SerializedName("server_engine_type")
    val serverEngineType: String? = null,
    @SerializedName("text_mode")
    val textMode: String? = null,
    @SerializedName("vadInterval")
    val vadInterval: String? = null,
    @SerializedName("vadVolume")
    val vadVolume: String? = null,
    @SerializedName("voice_format")
    val voiceFormat: String? = null
)