package com.study.base.snails

import com.blankj.utilcode.util.GsonUtils
import com.snails.base.record.utils.MP3Recorder
import com.snails.base.tai.BuildConfig
import com.tencent.cloud.soe.TAIOralController
import com.tencent.cloud.soe.audio.data.TAIDataSource
import com.tencent.cloud.soe.entity.ClientException
import com.tencent.cloud.soe.entity.OralEvaluationRequest
import com.tencent.cloud.soe.entity.ServerException
import com.tencent.cloud.soe.entity.TAIConfig
import com.tencent.cloud.soe.listener.OralEvaluationStateListener
import com.tencent.cloud.soe.listener.TAIListener
import java.io.File
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.RejectedExecutionException
import java.util.concurrent.TimeUnit

/**
 * @Description 腾讯智聆录音评测
 * <AUTHOR>
 * @CreateTime 2024年10月18日 22:19:23
 */
object TaiEvaluation {

    @Volatile
    private var isRecording = false //是否正在录音

    private val APP_ID: Int = BuildConfig.AppId.toInt()

    private const val SECRET_KEY: String = BuildConfig.Secretkey

    private const val SECRET_ID: String = BuildConfig.SecretId

    private var taiOralController: TAIOralController? = null

    private var oralEvaluationStateListener: OralEvaluationStateListener? = null

    private var evaluationListener: EvaluationListener? = null

    private var taiListener: TAIListener? = null

    private var audioFile: File? = null //录音文件
    private var mp3Recorder: MP3Recorder? = null

    @Volatile
    private var mExecutorService: ExecutorService? = null

    /**
     * 初始化评测
     */
    fun initEvaluation(config: Map<String, Any>?, listener: EvaluationListener? = null) {

        // 设置音频文件和录音器
        setupAudioRecording(config)

        // 构建TAI配置
        val taiConfig = buildTaiConfig(config)

        // 初始化控制器和监听器
        taiOralController = TAIOralController(taiConfig)

        this.evaluationListener = listener

        // 初始化线程池
        initExecutorService()

        initTaiListener()
        initEvaluationStateListener()
    }

    /**
     * 开始录音评测
     */
    fun startOralEvaluation() {
        if (isRecording) {
            taiOralController?.stopOralEvaluation()
            return
        }
        taiOralController?.startOralEvaluation(taiListener, oralEvaluationStateListener)
    }

    /**
     * 取消评测
     */
    fun cancelOralEvaluation() {
        taiOralController?.cancelOralEvaluation()
        isRecording = false
    }

    /**
     * 停止评测
     */
    fun stopOralEvaluation() {
        taiOralController?.stopOralEvaluation()
        isRecording = false
    }

    /**
     * 强制停止，解决智聆SDK 没有stop回调
     */
    fun forceStop() {
        safeExecute {
            mp3Recorder?.stop(
                success = {
                    isRecording = false
                    val map = mutableMapOf<String, String>()
                    map["final"] = "1"
                    val result = GsonUtils.toJson(map)
                    evaluationListener?.evaluationResult(result, audioFile = audioFile?.path)
                },
                failed = { t ->
                    evaluationListener?.onError("停止录音时，发生异常。${t.message}")
                }
            )
        }
    }

    /**
     * 释放所有资源
     */
    fun release() {
        taiOralController?.release()
        isRecording = false

        // 安全关闭线程池
        shutdownExecutorService()
        
        taiOralController = null
        oralEvaluationStateListener = null
        evaluationListener = null
        taiListener = null
        audioFile = null
        mExecutorService = null
    }

    /**
     * 设置音频录制相关配置
     */
    private fun setupAudioRecording(config: Map<String, Any>?) {
        val audioFilePath = config?.get("audioFile")?.toString()
        if (audioFilePath != null) {
            audioFile = File(audioFilePath)
            audioFile?.let { file ->
                mp3Recorder = MP3Recorder(file).apply { init() }
            }
        }
    }

    /**
     * 构建TAI配置
     */
    private fun buildTaiConfig(config: Map<String, Any>?): TAIConfig {
        return TAIConfig.Builder()
            .appID(APP_ID)
            .secretID(SECRET_ID)
            .secretKey(SECRET_KEY)
            .apiParams(config)
            .apply {
                configureVAD(config)
                configureStopOnVadDetected(config)
                configureDataSource()
            }
            .build()
    }

    /**
     * 配置VAD（静音检测）
     */
    private fun TAIConfig.Builder.configureVAD(config: Map<String, Any>?) {
        val vadInterval = config?.get("vadInterval")
        val vadVolume = config?.get("vadVolume")

        if (vadInterval != null && vadVolume != null) {
            enableVAD(true) // 开启静音检测
            vadInterval(vadInterval.toString().toLong()) // 静音检测时间间隔(ms)
            vadDBThreshold(vadVolume.toString().toInt()) // 静音检测阈值
        }
    }

    /**
     * 配置是否检测到静音就停止评测
     */
    private fun TAIConfig.Builder.configureStopOnVadDetected(config: Map<String, Any>?) {
        val stopOnVadDetected = config?.get("stopOnVadDetected")?.toString()?.toBoolean() ?: true
        stopOnVadDetected(stopOnVadDetected)
    }

    /**
     * 配置数据源
     */
    private fun TAIConfig.Builder.configureDataSource() {
        // 是否需要回调音频数据
        val needsAudioCallback = (audioFile != null)
        dataSource(TAIDataSource(needsAudioCallback))
    }

    /**
     * 初始化腾讯智聆回调，录音数据回调
     */
    private fun initEvaluationStateListener(): OralEvaluationStateListener? {
        if (oralEvaluationStateListener == null) {
            oralEvaluationStateListener = object : OralEvaluationStateListener {

                override fun onStartRecord(request: OralEvaluationRequest) {
                    isRecording = true
                    evaluationListener?.startRecord()
                }

                /**
                 * 返回音频流，
                 * 用于返回宿主层做录音缓存业务。
                 * 由于方法跑在sdk线程上，这里多用于文件操作，宿主需要新开一条线程专门用于实现业务逻辑
                 * @param audioData
                 */
                override fun onAudioData(audioData: ShortArray, readBufferLen: Int) {
                    safeExecute {
                        mp3Recorder?.onAudioData(audioData, readBufferLen)
                    }
                }

                override fun onStopRecord(request: OralEvaluationRequest) {
                    safeExecute {
                        mp3Recorder?.stop(
                            success = {
                                isRecording = false
                                evaluationListener?.stopRecord()
                            },
                            failed = { t ->
                                evaluationListener?.onError("停止录音时，发生异常。${t.message}")
                            }
                        )
                    }
                }
            }
        }
        return oralEvaluationStateListener
    }

    /**
     * 初始化腾讯智聆回调，评测数据
     */
    private fun initTaiListener(): TAIListener? {
        if (taiListener == null) {
            taiListener = object : TAIListener {
                override fun onMessage(msg: String?) {
                }

                override fun onVad() {
                    evaluationListener?.onVad()
                }

                override fun onFinish(msg: String?) {
                    if (msg != null) {
                        evaluationListener?.evaluationResult(msg, audioFile = audioFile?.path)
                    }
                }

                override fun onError(
                    request: OralEvaluationRequest?,
                    clientException: ClientException?,
                    serverException: ServerException?,
                    response: String?
                ) {
                    response?.let {
                        evaluationListener?.onError("response=$response")
                    }
                    evaluationListener?.onLog("clientException=${clientException?.message}...serverException=${serverException?.message}...response=$response")
                }

                override fun onVolumeDb(volumeDb: Float) {
                    evaluationListener?.volumeDb(volumeDb)
                }
            }
        }
        return taiListener
    }

    /**
     * 初始化线程池
     */
    private fun initExecutorService() {
        if (mExecutorService == null || mExecutorService?.isShutdown == true) {
            mExecutorService = Executors.newSingleThreadExecutor { r ->
                Thread(r, "TaiEvaluation-Worker").apply {
                    isDaemon = true
                }
            }
        }
    }

    /**
     * 安全执行任务
     */
    private fun safeExecute(task: Runnable) {
        try {
            mExecutorService?.let { executor ->
                if (!executor.isShutdown && !executor.isTerminated) {
                    executor.execute(task)
                } else {
                    evaluationListener?.onError("线程池已关闭，无法执行任务")
                }
            } ?: run {
                evaluationListener?.onError("线程池未初始化")
            }
        } catch (e: RejectedExecutionException) {
            evaluationListener?.onError("任务执行被拒绝: ${e.message}")
        } catch (e: Exception) {
            evaluationListener?.onError("执行任务时发生异常: ${e.message}")
        }
    }

    /**
     * 安全关闭线程池
     */
    private fun shutdownExecutorService() {
        mExecutorService?.let { executor ->
            try {
                // 停止接受新任务
                executor.shutdown()

                // 等待已提交任务完成，最多等待3秒
                if (!executor.awaitTermination(3, TimeUnit.SECONDS)) {
                    // 强制停止所有任务
                    executor.shutdownNow()

                    // 再等待2秒确保完全停止
                    if (!executor.awaitTermination(2, TimeUnit.SECONDS)) {
                        evaluationListener?.onLog("线程池无法在规定时间内完全关闭")
                    }
                }
            } catch (e: InterruptedException) {
                // 当前线程被中断，强制关闭线程池
                executor.shutdownNow()
                // 恢复中断状态
                Thread.currentThread().interrupt()
                evaluationListener?.onLog("关闭线程池时被中断: ${e.message}")
            } catch (e: Exception) {
                evaluationListener?.onLog("关闭线程池时发生异常: ${e.message}")
            }
        }
    }
}