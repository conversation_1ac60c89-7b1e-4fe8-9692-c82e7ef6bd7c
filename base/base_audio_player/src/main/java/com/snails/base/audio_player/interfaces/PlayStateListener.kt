package com.snails.base.audio_player.interfaces

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年02月13日 16:48:52
 */
interface PlayStateListener {
    /**
     * 缓冲中
     */
    fun onBuffering()

    /**
     * 播放中
     */
    fun onPlay()

    /**
     * 停止
     */
    fun onStop()

    /**
     * 播放出错
     */
    fun onError(t: Throwable?)
}

open class DefaultPlayStateListener : PlayStateListener {

    override fun onBuffering() {}

    override fun onPlay() {}

    override fun onStop() {}

    override fun onError(t: Throwable?) {}
}