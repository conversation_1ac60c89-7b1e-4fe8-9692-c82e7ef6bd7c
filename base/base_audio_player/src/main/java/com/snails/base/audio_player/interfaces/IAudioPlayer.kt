package com.snails.base.audio_player.interfaces

import com.snails.base.audio_player.bean.PlayState

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年02月13日 16:48:38
 */
interface IAudioPlayer {
    fun play(url: String?)
    fun play(url: String?, speed: Float)
    fun setPlaySpeed(speed: Float)
    fun pause()
    fun resume()
    fun stop()
    fun seekTo(positionMs: Long)
    fun isPlaying(): Boolean
    fun getCurrentPlayState(): PlayState
    fun getDuration(): Long
    fun getCurrentPosition(): Long
    fun release()
    fun addPlayStateListener(listener: PlayStateListener)
    fun removePlayStateListener(listener: PlayStateListener)
    fun addPlayInfoListener(listener: PlayInfoListener)
    fun removePlayInfoListener(listener: PlayInfoListener)
}