package com.snails.base.audio_player.interfaces

interface PlayInfoListener {
    /**
     * 播放进度回调
     * @param currentDuration 当前时长（毫秒）
     * @param totalDuration 总时长（毫秒）
     *
     * TimesUtils.formatTime(currentTime)
     */
    fun onProgressChanged(currentDuration: Long, totalDuration: Long)

    /**
     * 播放进度
     * @param progress 进度
     */
    fun onPlayProgress(progress: Int)

    /**
     * 缓冲进度
     * @param progress 进度
     */
    fun onBufferProgress(progress: Int)
}

open class DefaultPlayInfoImpl : PlayInfoListener {

    override fun onProgressChanged(currentDuration: Long, totalDuration: Long) {}

    override fun onBufferProgress(progress: Int) {}

    override fun onPlayProgress(progress: Int) {}
}