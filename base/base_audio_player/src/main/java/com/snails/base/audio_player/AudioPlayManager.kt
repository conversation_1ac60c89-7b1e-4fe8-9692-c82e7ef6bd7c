package com.snails.base.audio_player

import com.blankj.utilcode.util.Utils
import com.snails.base.audio_player.bean.PlayState
import com.snails.base.audio_player.interfaces.IAudioPlayer
import com.snails.base.audio_player.interfaces.PlayInfoListener
import com.snails.base.audio_player.interfaces.PlayStateListener
import com.snails.base.audio_player.player.ExoPlayerPlayer

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年02月13日 16:53:20
 */
class AudioPlayManager private constructor() {

    private var audioPlayer: IAudioPlayer? = null
    private val playStateListeners = mutableSetOf<PlayStateListener>()
    private val playInfoListeners = mutableSetOf<PlayInfoListener>()

    // 延迟初始化播放器
    private fun ensurePlayerInitialized(): IAudioPlayer {
        if (audioPlayer == null) {
            audioPlayer = ExoPlayerPlayer(Utils.getApp())
            // 重新注册所有监听器
            playStateListeners.forEach { audioPlayer?.addPlayStateListener(it) }
            playInfoListeners.forEach { audioPlayer?.addPlayInfoListener(it) }
        }
        return audioPlayer!!
    }

    companion object {
        @Volatile
        private var instance: AudioPlayManager? = null

        fun getInstance(): AudioPlayManager {
            return instance ?: synchronized(this) {
                instance ?: AudioPlayManager().also { instance = it }
            }
        }
    }

    // 切换播放器内核
    fun setAudioPlayer(player: IAudioPlayer) {
        // 释放之前的播放器资源
        audioPlayer?.release()
        audioPlayer = player
        // 重新注册所有监听器到新的播放器
        playStateListeners.forEach { audioPlayer?.addPlayStateListener(it) }
        playInfoListeners.forEach { audioPlayer?.addPlayInfoListener(it) }
    }

    // 播放音频
    fun play(url: String?) {
        ensurePlayerInitialized().play(url)
    }

    // 播放音频
    fun play(url: String?, speed: Float) {
        ensurePlayerInitialized().play(url, speed)
    }

    /**
     * 设置播放速度
     */
    fun setPlaySpeed(speed: Float) {
        ensurePlayerInitialized().setPlaySpeed(speed)
    }

    // 暂停播放
    fun pause() {
        ensurePlayerInitialized().pause()
    }

    // 恢复播放
    fun resume() {
        ensurePlayerInitialized().resume()
    }

    // 停止播放
    fun stop() {
        ensurePlayerInitialized().stop()
    }

    // seek 到某个时间点
    fun seekTo(progress: Int?) {
        //进度转换
        val pro = progress ?: return
        val duration = audioPlayer?.getDuration() ?: 0
        if (duration <= 0) {
            return
        }
        val pos = (pro.toFloat() / 100f) * duration
        ensurePlayerInitialized().seekTo(pos.toLong())
    }

    // 检查是否正在播放
    fun isPlaying(): Boolean {
        return ensurePlayerInitialized().isPlaying()
    }

    fun getCurrentPlayState(): PlayState? {
        return ensurePlayerInitialized().getCurrentPlayState()
    }

    // 释放播放器
    fun release() {
        audioPlayer?.release()
        audioPlayer = null
        // 清空监听器列表
        playStateListeners.clear()
        playInfoListeners.clear()
    }

    // 添加播放状态监听器
    fun addPlayStateListener(listener: PlayStateListener) {
        if (playStateListeners.add(listener)) {
            // 只有成功添加到集合中才注册到播放器
            audioPlayer?.addPlayStateListener(listener)
        }
    }

    // 移除播放状态监听器
    fun removePlayStateListener(listener: PlayStateListener) {
        if (playStateListeners.remove(listener)) {
            // 只有成功从集合中移除才从播放器中移除
            audioPlayer?.removePlayStateListener(listener)
        }
    }

    fun addPlayInfoListener(listener: PlayInfoListener) {
        if (playInfoListeners.add(listener)) {
            // 只有成功添加到集合中才注册到播放器
            audioPlayer?.addPlayInfoListener(listener)
        }
    }

    fun removePlayInfoListener(listener: PlayInfoListener) {
        if (playInfoListeners.remove(listener)) {
            // 只有成功从集合中移除才从播放器中移除
            audioPlayer?.removePlayInfoListener(listener)
        }
    }

    // 获取当前监听器数量（用于调试）
    fun getPlayStateListenerCount(): Int = playStateListeners.size
    fun getPlayInfoListenerCount(): Int = playInfoListeners.size
}