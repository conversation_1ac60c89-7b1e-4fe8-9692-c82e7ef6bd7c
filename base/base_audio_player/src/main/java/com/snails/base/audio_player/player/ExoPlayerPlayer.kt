package com.snails.base.audio_player.player

import android.content.Context
import android.os.Handler
import android.os.Looper
import androidx.annotation.OptIn
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import com.snails.base.audio_player.bean.PlayState
import com.snails.base.audio_player.interfaces.IAudioPlayer
import com.snails.base.audio_player.interfaces.PlayInfoListener
import com.snails.base.audio_player.interfaces.PlayStateListener
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年02月13日 16:50:01
 */
class ExoPlayerPlayer(private val context: Context) : IAudioPlayer {

    // 播放器实例
    private var player: ExoPlayer? = null

    private var isPrepared = false

    private val _playState = MutableStateFlow(PlayState.IDLE)
    val playState: StateFlow<PlayState> = _playState.asStateFlow()

    // 用于保存状态监听器
    private val playStateListener = mutableListOf<PlayStateListener>()

    // 用于保存播放信息的监听器
    private val playInfoListener = mutableListOf<PlayInfoListener>()

    // 用于更新进度的Handler
    private val handler: Handler = Handler(Looper.getMainLooper())
    private val progressRunnable: Runnable = object : Runnable {
        override fun run() {
            getPlayDurationInfo()
        }

        /**
         * 获取播放时长信息
         */
        private fun getPlayDurationInfo() {
            player?.let { player ->
                if (player.isPlaying) {
                    // 获取当前播放位置和总时长
                    val currentPosition: Long = getCurrentPosition()
                    val duration: Long = getDuration()

                    val progressPercentage = getPlayProgress(duration, currentPosition)
                    val bufferPercentage = getBuProgress()
                    // 回调进度
                    playInfoListener.forEach {
                        it.onProgressChanged(currentPosition, duration)
                        it.onPlayProgress(progressPercentage)
                        it.onBufferProgress(bufferPercentage)
                    }
                    // 每100毫秒更新一次进度
                    handler.postDelayed(this, 100)
                }
            }
        }

        /**
         * 获取播放进度
         */
        private fun getPlayProgress(duration: Long, currentPosition: Long): Int {
            return if (duration > 0) {
                (currentPosition * 100 / duration).toInt()
            } else {
                0 // 如果duration未知或为0，默认为0%
            }
        }

        /**
         * 获取缓冲进度
         */
        private fun getBuProgress(): Int {
            return player?.bufferedPercentage ?: 0
        }
    }

    // 获取 ExoPlayer 实例
    private fun getPlayer(context: Context): ExoPlayer {
        if (player == null) {
            player = ExoPlayer.Builder(context).build().apply {
                addPlayerListener()
            }
        }
        return player!!
    }

    // 播放音频
    override fun play(url: String?) {
        reallyPlay(url)
    }

    override fun play(url: String?, speed: Float) {
        reallyPlay(url, speed)
    }

    override fun setPlaySpeed(speed: Float) {
        pause()
        player?.setPlaybackSpeed(speed)
        resume()
    }

    // 暂停播放
    override fun pause() {
        player?.pause()
    }

    // 恢复播放
    override fun resume() {
        player?.play()
    }

    // 停止播放
    override fun stop() {
        player?.stop()
        player?.clearMediaItems()
    }

    override fun seekTo(positionMs: Long) {
        player?.seekTo(positionMs)
    }

    // 检查是否正在播放
    override fun isPlaying(): Boolean {
        return player?.isPlaying == true
    }

    /**
     * 获取当前播放位置
     * @return 当前位置（毫秒）
     */
    override fun getCurrentPosition(): Long {
        if (isPrepared) {
            return player?.currentPosition ?: 0
        }
        return 0
    }

    /**
     * 获取音频总时长
     * @return 总时长（毫秒）
     */
    override fun getDuration(): Long {
        if (isPrepared) {
            return player?.duration ?: 0
        }
        return 0
    }

    override fun getCurrentPlayState() = playState.value

    // 释放播放器资源
    override fun release() {
        player?.release()
        player = null
        // 停止进度更新
        stopProgressUpdate()
    }

    // 添加播放状态监听器
    override fun addPlayStateListener(listener: PlayStateListener) {
        playStateListener.add(listener)
    }

    // 移除播放状态监听器
    override fun removePlayStateListener(listener: PlayStateListener) {
        playStateListener.remove(listener)
    }

    override fun addPlayInfoListener(listener: PlayInfoListener) {
        playInfoListener.add(listener)
    }

    override fun removePlayInfoListener(listener: PlayInfoListener) {
        playInfoListener.remove(listener)
    }

    /**
     * 真正播放
     */
    @OptIn(UnstableApi::class)
    private fun reallyPlay(url: String?, speed: Float = 1.0f) {
        if (url.isNullOrEmpty()) {
            return
        }
        try {
            val player = getPlayer(context)
            val mediaItem = MediaItem.fromUri(url)
            val mediaSource =
                ProgressiveMediaSource.Factory(DefaultDataSource.Factory(context))
                    .createMediaSource(mediaItem)

            player.setMediaSource(mediaSource)
            player.setPlaybackSpeed(speed)
            player.prepare()
            player.play()
        } catch (e: Exception) {
            // 当播放器发生错误时，通知所有监听器
            notifyPlayState(PlayState.ERROR, e)
        }
    }

    /**
     * 给播放器添加监听器
     */
    private fun ExoPlayer.addPlayerListener() {
        addListener(object : Player.Listener {
            override fun onIsPlayingChanged(isPlaying: Boolean) {
                updatePlayState(playbackState, isPlaying)
            }

            override fun onPlayerError(error: PlaybackException) {
                notifyPlayState(PlayState.ERROR, error)
            }

            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    Player.STATE_READY -> {
                        isPrepared = true
                        updatePlayState(playbackState, isPlaying)
                    }

                    Player.STATE_ENDED -> {
                        notifyPlayState(PlayState.FINISH)
                    }

                    Player.STATE_BUFFERING -> {
                        notifyPlayState(PlayState.BUFFERING)
                    }

                    Player.STATE_IDLE -> {
                        notifyPlayState(PlayState.IDLE)
                    }
                }
            }
        })
    }

    /**
     * 统一更新播放状态
     */
    private fun updatePlayState(playbackState: Int, isPlaying: Boolean) {
        if (playbackState == Player.STATE_READY) {
            val newState = if (isPlaying) PlayState.PLAYING else PlayState.PAUSE
            notifyPlayState(newState)
            if (isPlaying) {
                startProgressUpdate()
            } else {
                stopProgressUpdate()
            }
        }
    }
    /**
     * 通知播放状态更新
     */

    /**
     * 通知播放状态更新
     */
    private fun notifyPlayState(state: PlayState, t: Throwable? = null) {
        if (_playState.value == PlayState.FINISH && state == PlayState.PAUSE) {
            return
        }
        _playState.value = state

        if (playStateListener.isEmpty()) {
            return
        }
        runCatching {
            playStateListener.forEach {
                when (state) {
                    PlayState.PLAYING -> it.onPlay()
                    PlayState.BUFFERING -> it.onBuffering()
                    PlayState.PAUSE,
                    PlayState.STOP,
                    PlayState.IDLE,
                    PlayState.READY,
                    PlayState.FINISH -> {
                        it.onStop()
                    }

                    PlayState.ERROR -> {
                        it.onError(t)
                    }
                }
            }
        }
    }

    /**
     * 开始进度更新
     */
    private fun startProgressUpdate() {
        // 先移除之前的回调，避免重复
        stopProgressUpdate()
        // 开始进度更新
        handler.post(progressRunnable)
    }

    /**
     * 停止进度更新
     */
    private fun stopProgressUpdate() {
        handler.removeCallbacks(progressRunnable)
    }
}