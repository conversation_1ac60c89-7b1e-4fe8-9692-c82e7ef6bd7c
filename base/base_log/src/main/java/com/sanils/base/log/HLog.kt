package com.sanils.base.log

import android.annotation.SuppressLint
import com.snails.base.utils.constants.AppConstants
import okio.BufferedSink
import okio.buffer
import okio.sink
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit

/**
 * @Description 如果需要进行日志保存或上传，请使用下面的类进行日志打印
 * <AUTHOR>
 * @CreateTime 2024年08月13日 13:58:33
 */
object HLog : ILog {
    private const val LOG_DIR_NAME = "Logs"
    private const val LOG_FILE_PREFIX = "log_"
    private const val LOG_FILE_SUFFIX = ".txt"
    private const val MAX_FILE_SIZE = 1 * 1024 * 1024 // 2MB
    private const val MAX_LOG_FILES = 100 // 最多保留7个日志文件
    private const val QUEUE_CAPACITY = 1000
    private const val FLUSH_INTERVAL_MS = 2000L
    private const val OFFER_TIMEOUT_MS = 100L
    private const val BATCH_WRITE_SIZE = 100

    private val logQueue = LinkedBlockingQueue<String>(QUEUE_CAPACITY)

    @Volatile
    private var running = false

    @Volatile
    private var isInit = false
    private var logThread: Thread? = null
    private var sink: BufferedSink? = null
    private var currentLogFile: File? = null

    @SuppressLint("ConstantLocale")
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())

    @SuppressLint("ConstantLocale")
    private val fileDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    private lateinit var logDir: File
    private val lock = Any()

    override fun initLog() {
        synchronized(lock) {
            if (isInit) return
            try {
                // 清空队列中的旧日志数据
                logQueue.clear()
                
                logDir = File(AppConstants.getCacheLogPath(), LOG_DIR_NAME)
                if (!logDir.exists() && !logDir.mkdirs()) {
                    // 目录创建失败
                    return
                }
                running = true
                isInit = true
                logThread = Thread { writeLoop() }.apply { isDaemon = true; start() }
                cleanOldFiles()
            } catch (e: Exception) {
                isInit = false
                running = false
            }
        }
    }

    override fun close() {
        synchronized(lock) {
            if (!isInit) return
            running = false
            logThread?.interrupt()
            logThread = null
            flushRemainingLogs()
            try {
                sink?.flush()
            } catch (_: Exception) {
            }
            try {
                sink?.close()
            } catch (_: Exception) {
            }
            sink = null
            isInit = false
        }
    }

    override fun flushLog(isSync: Boolean) {
        if (isSync) {
            try {
                sink?.flush()
            } catch (_: Exception) {
            }
        }
    }

    override fun i(tag: String, info: String) = enqueueLog("INFO", tag, info)
    override fun i(tag: String, info: Exception) = enqueueLog("INFO", tag, info.toString())
    override fun i(tag: String, info: Throwable) = enqueueLog("INFO", tag, info.toString())
    override fun d(tag: String, info: String) = enqueueLog("DEBUG", tag, info)
    override fun d(tag: String, info: Exception) = enqueueLog("DEBUG", tag, info.toString())
    override fun d(tag: String, info: Throwable) = enqueueLog("DEBUG", tag, info.toString())
    override fun e(tag: String, info: String) = enqueueLog("ERROR", tag, info)
    override fun e(tag: String, info: Exception) = enqueueLog("ERROR", tag, info.toString())
    override fun e(tag: String, info: Throwable) = enqueueLog("ERROR", tag, info.toString())
    override fun w(tag: String, info: String) = enqueueLog("WARN", tag, info)
    override fun w(tag: String, info: Exception) = enqueueLog("WARN", tag, info.toString())
    override fun w(tag: String, info: Throwable) = enqueueLog("WARN", tag, info.toString())
    override fun v(tag: String, info: String) = enqueueLog("VERBOSE", tag, info)
    override fun v(tag: String, info: Exception) = enqueueLog("VERBOSE", tag, info.toString())
    override fun v(tag: String, info: Throwable) = enqueueLog("VERBOSE", tag, info.toString())

    private fun enqueueLog(level: String, tag: String, msg: String) {
        if (!isInit) return
        val logLine = try {
            val time = dateFormat.format(Date())
            "$time [$level]/$tag: $msg\n"
        } catch (e: Exception) {
            return
        }
        try {
            if (!logQueue.offer(logLine, OFFER_TIMEOUT_MS, TimeUnit.MILLISECONDS)) {
                // offer 超时，日志丢弃，可选：写入 fallback 文件
            }
        } catch (_: Exception) {
            // offer 异常保护
        }
    }

    private fun writeLoop() {
        try {
            while (running || logQueue.isNotEmpty()) {
                val logs = mutableListOf<String>()
                logQueue.drainTo(logs, BATCH_WRITE_SIZE)
                if (logs.isEmpty()) {
                    val log = logQueue.poll(FLUSH_INTERVAL_MS, TimeUnit.MILLISECONDS)
                    if (log != null) logs.add(log)
                }
                if (logs.isNotEmpty()) {
                    try {
                        logs.forEach { writeLog(it) }
                        sink?.flush()
                        checkRotate()
                    } catch (_: Exception) {
                        // 写入异常保护
                    }
                }
            }
        } catch (_: InterruptedException) {
            // 线程被中断，安全退出
        } finally {
            try {
                sink?.flush()
            } catch (_: Exception) {
            }
            try {
                sink?.close()
            } catch (_: Exception) {
            }
        }
    }

    private fun writeLog(logLine: String) {
        try {
            if (sink == null) {
                openNewLogFile()
            }
            sink?.writeUtf8(logLine)
        } catch (_: Exception) {
            // 写入异常保护
        }
    }

    private fun checkRotate() {
        val file = currentLogFile ?: return
        if (file.length() > 0 && file.length() > MAX_FILE_SIZE) {
            try {
                sink?.flush()
            } catch (_: Exception) {
            }
            try {
                sink?.close()
            } catch (_: Exception) {
            }
            openNewLogFile()
            cleanOldFiles()
        }
    }

    private fun openNewLogFile() {
        val fileName = "$LOG_FILE_PREFIX${fileDateFormat.format(Date())}$LOG_FILE_SUFFIX"
        val file = File(logDir, fileName)
        currentLogFile = file
        try {
            sink = file.sink(append = true).buffer()
        } catch (_: Exception) {
            sink = null
        }
    }

    private fun cleanOldFiles() {
        val files = logDir.listFiles { f ->
            f.name.startsWith(LOG_FILE_PREFIX) && f.name.endsWith(LOG_FILE_SUFFIX)
        }
        if (files != null && files.size > MAX_LOG_FILES) {
            files.sortedBy { it.lastModified() }
                .take(files.size - MAX_LOG_FILES)
                .forEach {
                    try {
                        it.delete()
                    } catch (_: Exception) {
                    }
                }
        }
    }

    private fun flushRemainingLogs() {
        try {
            while (logQueue.isNotEmpty()) {
                val logs = mutableListOf<String>()
                logQueue.drainTo(logs, BATCH_WRITE_SIZE)
                logs.forEach { writeLog(it) }
                try {
                    sink?.flush()
                } catch (_: Exception) {
                }
            }
        } catch (_: Exception) {
        }
    }
}