package com.sanils.base.log

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月13日 13:55:52
 */
interface ILog {

    fun initLog()
    fun close()
    fun flushLog(isSync: Boolean)

    fun i(tag: String, info: String)
    fun d(tag: String, info: String)
    fun e(tag: String, info: String)
    fun w(tag: String, info: String)
    fun v(tag: String, info: String)

    fun i(tag: String, info: Exception)
    fun d(tag: String, info: Exception)
    fun e(tag: String, info: Exception)
    fun w(tag: String, info: Exception)
    fun v(tag: String, info: Exception)

    fun i(tag: String, info: Throwable)
    fun d(tag: String, info: Throwable)
    fun e(tag: String, info: Throwable)
    fun w(tag: String, info: Throwable)
    fun v(tag: String, info: Throwable)
}