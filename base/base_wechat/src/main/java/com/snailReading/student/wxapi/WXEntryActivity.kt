package com.snailReading.student.wxapi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.snails.base.wechat.WeChatBaseHelper
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.tencent.mm.opensdk.openapi.WXAPIFactory

class WXEntryActivity : Activity(), IWXAPIEventHandler {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WXAPIFactory.createWXAPI(this, WeChatBaseHelper.WECHAT_APP_ID, true)
            .handleIntent(intent, this)
    }

    override fun onResume() {
        super.onResume()
        finish()
    }

    override fun onResp(baseResp: BaseResp?) {
        when (baseResp?.type) {
            // 授权登录
            ConstantsAPI.COMMAND_SENDAUTH -> {
                authLogin(baseResp)
            }
            // 分享
            ConstantsAPI.COMMAND_SENDMESSAGE_TO_WX -> {
                val resp = baseResp as? SendMessageToWX.Resp?
                share(resp)
            }
        }
    }

    private fun share(resp: SendMessageToWX.Resp?) {
        val listener = WeChatBaseHelper.mOnWeChatShareListener
        when (resp?.errCode) {
            // 用户同意
            BaseResp.ErrCode.ERR_OK -> {
                listener?.onWeChatShareSuccess(resp)
            }
            // 用户拒绝授权
            BaseResp.ErrCode.ERR_AUTH_DENIED -> {
                listener?.onWeChatShareAuthDenied(resp)
            }
            // 用户取消
            BaseResp.ErrCode.ERR_USER_CANCEL -> {
                listener?.onWeChatShareCancel(resp)
            }
            // 分享发送失败
            BaseResp.ErrCode.ERR_SENT_FAILED -> {
                listener?.onWeChatShareSentFailed(resp)
            }
            // 其他错误
            else -> {
                listener?.onWeChatShareError(resp)
            }
        }
        WeChatBaseHelper.mOnWeChatShareListener = null
    }

    private fun authLogin(baseResp: BaseResp) {
        val listener = WeChatBaseHelper.mOnWeChatAuthLoginListener
        when (baseResp.errCode) {
            // 用户同意了授权
            BaseResp.ErrCode.ERR_OK -> {
                val code = (baseResp as? SendAuth.Resp)?.code
                if (code.isNullOrEmpty()) {
                    listener?.onWeChatAuthLoginError(null, null)
                } else {
                    listener?.onWeChatAuthLoginSuccess(code)
                }
            }
            // 用户取消的授权登录
            BaseResp.ErrCode.ERR_USER_CANCEL -> {
                listener?.onWeChatAuthLoginCancel()
            }
            // 用户拒绝授权
            BaseResp.ErrCode.ERR_AUTH_DENIED -> {
                listener?.onWeChatAuthLoginAuthDenied()
            }

            else -> {
                listener?.onWeChatAuthLoginError(null, null)
            }
        }
        WeChatBaseHelper.mOnWeChatAuthLoginListener = null
    }

    override fun onReq(baseReq: BaseReq?) {
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
    }
}