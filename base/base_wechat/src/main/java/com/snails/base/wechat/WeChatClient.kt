package com.snails.base.wechat

import android.graphics.Bitmap
import com.blankj.utilcode.util.Utils
import com.snails.base.wechat.enums.Scene
import com.snails.base.wechat.listener.IPaymentParams
import com.snails.base.wechat.listener.OnWeChatAuthLoginListener
import com.snails.base.wechat.listener.OnWeChatPaymentListener
import com.snails.base.wechat.listener.OnWeChatShareListener
import com.snails.base.wechat.listener.WeChatScanCodeListener
import com.snails.base.wechat.utils.WeChatUtils
import com.tencent.mm.opensdk.diffdev.DiffDevOAuthFactory
import com.tencent.mm.opensdk.diffdev.OAuthErrCode
import com.tencent.mm.opensdk.diffdev.OAuthListener

object WeChatClient {

    private val applicationContext = Utils.getApp()

    /**
     * 分享文字内容
     */
    fun shareText(content: String, scene: Scene, listener: OnWeChatShareListener?): Boolean {
        if (!WeChatUtils.isInstalled()) {
            listener?.onNotInstall()
            return false
        }
        return WeChatBaseHelper(applicationContext).shareText(content, scene, listener)
    }

    /**
     * 分享图片
     */
    fun shareImage(
        bmp: Bitmap,
        scene: Scene,
        listener: OnWeChatShareListener,
        thumbWidth: Int = WeChatBaseHelper.THUMB_SIZE,
        thumbHeight: Int = WeChatBaseHelper.THUMB_SIZE
    ): Boolean {
        if (!WeChatUtils.isInstalled()) {
            listener.onNotInstall()
            return false
        }
        return WeChatBaseHelper(applicationContext).shareImage(
            bmp,
            scene,
            listener,
            thumbWidth,
            thumbHeight
        )
    }

    /**
     * 分享音乐
     */
    fun shareMusic(
        bitmap: Bitmap,
        scene: Scene,
        musicUrl: String,
        title: String,
        description: String,
        listener: OnWeChatShareListener,
        thumbWidth: Int = WeChatBaseHelper.THUMB_SIZE,
        thumbHeight: Int = WeChatBaseHelper.THUMB_SIZE
    ): Boolean {
        if (!WeChatUtils.isInstalled()) {
            listener.onNotInstall()
            return false
        }
        return WeChatBaseHelper(applicationContext)
            .shareMusic(
                bitmap,
                scene,
                musicUrl,
                title,
                description,
                listener,
                thumbWidth,
                thumbHeight
            )
    }

    /**
     * 分享视频
     */
    fun shareVideo(
        bitmap: Bitmap,
        scene: Scene,
        videoUrl: String,
        title: String,
        description: String,
        listener: OnWeChatShareListener,
        thumbWidth: Int = WeChatBaseHelper.THUMB_SIZE,
        thumbHeight: Int = WeChatBaseHelper.THUMB_SIZE
    ): Boolean {
        if (!WeChatUtils.isInstalled()) {
            listener.onNotInstall()
            return false
        }
        return WeChatBaseHelper(applicationContext).shareVideo(
            bitmap,
            scene,
            videoUrl,
            title,
            description,
            listener,
            thumbWidth,
            thumbHeight
        )
    }

    /**
     * 网页分享
     */
    fun shareWebPage(
        scene: Scene,
        bitmap: Bitmap?,
        webPageUrl: String?,
        title: String?,
        description: String?,
        listener: OnWeChatShareListener?,
        thumbWidth: Int = WeChatBaseHelper.THUMB_SIZE,
        thumbHeight: Int = WeChatBaseHelper.THUMB_SIZE
    ): Boolean {
        if (!WeChatUtils.isInstalled()) {
            listener?.onNotInstall()
            return false
        }
        return WeChatBaseHelper(applicationContext).shareWebPage(
            scene,
            bitmap,
            webPageUrl,
            title,
            description,
            listener,
            thumbWidth,
            thumbHeight
        )
    }

    /**
     * 授权登录
     */
    fun authLogin(listener: OnWeChatAuthLoginListener) {
        WeChatBaseHelper(applicationContext).authLogin(listener)
    }

    /**
     * 微信扫码登录
     */
    fun authLoginCode(
        appId: String,
        scope: String,
        nonceStr: String,
        timestamp: String,
        signature: String,
        listener: WeChatScanCodeListener? = null
    ) {
        DiffDevOAuthFactory.getDiffDevOAuth().stopAuth()
        DiffDevOAuthFactory.getDiffDevOAuth().auth(
            appId,
            scope,
            nonceStr,
            timestamp,
            signature,
            object : OAuthListener {
                override fun onAuthGotQrcode(p0: String?, p1: ByteArray?) {
                    listener?.onAuthGotQrcode(p1)
                }

                override fun onQrcodeScanned() {
                    listener?.onQrcodeScanned()
                }

                override fun onAuthFinish(p0: OAuthErrCode?, p1: String?) {
                    listener?.onAuthFinish(p0, p1)
                }
            })
    }

    /**
     * 微信支付
     */
    fun payment(params: IPaymentParams, listener: OnWeChatPaymentListener) {
        if (!WeChatUtils.isInstalled()) {
            listener.onNotInstall()
            return
        }
        WeChatBaseHelper(applicationContext).payment(params, listener)
    }
}