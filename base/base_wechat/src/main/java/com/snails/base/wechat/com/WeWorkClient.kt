package com.snails.base.wechat.com

import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.Utils
import com.snails.base.wechat.listener.OnWeChatShareListener
import com.tencent.wework.api.IWWAPI
import com.tencent.wework.api.WWAPIFactory
import com.tencent.wework.api.model.WWAuthMessage
import com.tencent.wework.api.model.WWBaseMessage
import com.tencent.wework.api.model.WWMediaLink
import com.tencent.wework.api.model.WWMediaText


/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年02月28日 10:13:38
 */
object WeWorkClient {
    private const val SCHEMA = "wwauth4f52ff78584af479000019"
    private const val APP_ID = "ww4f52ff78584af479"
    private const val AGENT_ID = "1000019"

    private var wwApi: IWWAPI = WWAPIFactory.createWWAPI(Utils.getApp()).apply {
        registerApp(SCHEMA)
    }

    fun shareText(content: String, listener: OnWeChatShareListener?) {
        val text = WWMediaText()
        text.text = content
        reallyShare(text, listener)
    }

    fun shareWeb(
        thumbUrl: String?,
        webPageUrl: String?,
        title: String?,
        description: String?,
        listener: OnWeChatShareListener?
    ) {
        val link = WWMediaLink()
        link.thumbUrl = thumbUrl
        link.webpageUrl = webPageUrl
        link.title = title
        link.description = description
        reallyShare(link, listener)
    }

    private fun reallyShare(message: WWBaseMessage, listener: OnWeChatShareListener?) {
        message.appName = "蜗牛阅读"
        message.appPkg = AppUtils.getAppPackageName()
        message.appId = APP_ID  //企业唯一标识。创建企业后显示在，我的企业 CorpID字段
        message.agentId = AGENT_ID  //应用唯一标识。显示在具体应用下的 AgentId字段
        wwApi.sendMessage(message) { resp ->
            if (resp is WWAuthMessage.Resp) {
                when (resp.errCode) {
                    WWAuthMessage.ERR_CANCEL -> {
                        listener?.onWeChatShareCancel(null)
                    }

                    WWAuthMessage.ERR_FAIL -> {
                        listener?.onWeChatShareSentFailed(null)
                    }

                    WWAuthMessage.ERR_OK -> {
                        listener?.onWeChatShareSuccess(null)
                    }
                }
            }
        }
    }

}