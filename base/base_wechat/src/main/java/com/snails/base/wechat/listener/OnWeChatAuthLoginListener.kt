package com.snails.base.wechat.listener

interface OnWeChatAuthLoginListener {
    /**
     * 微信授权登录成功
     * @param code 微信授权 code
     */
    fun onWeChatAuthLoginSuccess(code: String)

    /**
     * 用户取消微信授权登录
     */
    fun onWeChatAuthLoginCancel()

    /**
     * 微信授权登录被拒绝
     * 检查包名或签名与注册信息是否相符
     */
    fun onWeChatAuthLoginAuthDenied()

    /**
     * 微信授权登录错误
     */
    fun onWeChatAuthLoginError(errorCode: Int?, errorMessage: String?)
}