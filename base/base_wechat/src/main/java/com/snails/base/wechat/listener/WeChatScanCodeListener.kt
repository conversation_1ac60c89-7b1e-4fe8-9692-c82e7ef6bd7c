package com.snails.base.wechat.listener

import com.tencent.mm.opensdk.diffdev.OAuthErrCode

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年09月20日 09:47:46
 */
interface WeChatScanCodeListener {
    /**
     * auth之后返回的二维码接口
     * @param byteArray 二维码图片数据
     */
    fun onAuthGotQrcode(byteArray: ByteArray?)

    /**
     * 用户扫描二维码之后，回调该接口
     */
    fun onQrcodeScanned()

    /**
     * 用户点击授权后，回调该接口
     */
    fun onAuthFinish(p0: OAuthErrCode?, p1: String?)
}