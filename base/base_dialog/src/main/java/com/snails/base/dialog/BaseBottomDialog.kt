package com.snails.base.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.snails.base.utils.ext.getDimens
import java.lang.reflect.ParameterizedType

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月16日 10:10:50
 */
open class BaseBottomDialog<VB : ViewBinding>(private val heightDimen: Int? = null) :
    BottomSheetDialogFragment() {
    protected lateinit var binding: VB

    override fun onStart() {
        super.onStart()
        dialog?.let { dialog ->
            val bottomSheet =
                dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            context?.getDimens(heightDimen)?.let {
                bottomSheet?.layoutParams?.height = it
            }
            bottomSheet?.setBackgroundResource(R.drawable.shape_default_dialog_bg)
            bottomSheet.requestLayout()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        //设置背景透明
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        binding = createBinding(inflater, container)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        initView()
        initClick()
        initObserve()
    }

    open fun initData() {}
    open fun initView() {}
    open fun initObserve() {}
    open fun initClick() {}

    @Suppress("UNCHECKED_CAST")
    private fun createBinding(inflater: LayoutInflater, container: ViewGroup?): VB {
        val vbClass = getVBClass()
        val inflateMethod = vbClass.getMethod(
            "inflate", LayoutInflater::class.java, ViewGroup::class.java, Boolean::class.java
        )
        return inflateMethod.invoke(null, inflater, container, false) as VB
    }

    @Suppress("UNCHECKED_CAST")
    private fun getVBClass(): Class<VB> {
        val type = javaClass.genericSuperclass as ParameterizedType
        val vbClass = type.actualTypeArguments[0] as Class<VB>
        return vbClass
    }
}