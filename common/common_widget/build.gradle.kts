plugins {
    alias(libs.plugins.common.gradle)
}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.constraintlayout)
    implementation(libs.recyclerview)
    implementation(libs.material)
    implementation(libs.lottie)

    implementation(project(":base:base_utils"))
    implementation(project(":base:base_res"))

    compileOnly(project(":base:base_network"))
    compileOnly(project(":base:base_router"))
    compileOnly(project(":base:base_image_loader"))
}