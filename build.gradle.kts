// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.jetbrains.kotlin.android) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.kotlin.ksp) apply false
    alias(libs.plugins.router.agp8) apply false
}
//生成依赖图
apply("gradle/projectDependencyGraph.gradle")

//tasks.register("publishAllToMavenLocalDebug") {
//    group = "HQL"
//    description = "Publishes all specified modules to Maven Local in order"
//    //严格按照顺序执行以下，因为存在依赖问题
//    val tasksList = listOf(
//        ":base:base_sw:publishToMavenLocal",
//        ":base:base_res:publishToMavenLocal",
//        ":base:base_utils:publishToMavenLocal",
//        ":base:base_audio:publishToMavenLocal",
//        ":base:base_audio_player:publishToMavenLocal",
//        ":base:base_image_loader:publishToMavenLocal",
//        ":base:base_complex_imageview:publishToMavenLocal",
//        ":base:base_dialog:publishToMavenLocal",
//        ":base:base_game:publishToMavenLocal",
//        ":base:base_lame:publishToMavenLocal",
//        ":base:base_storage:publishToMavenLocal",
//        ":base:base_log:publishToMavenLocal",
//        ":base:base_multi_type:publishToMavenLocal",
//        ":base:base_multi_state_page:publishToMavenLocal",
//        ":base:base_network:publishToMavenLocal",
//        ":base:base_record:publishToMavenLocal",
//        ":base:base_router:publishToMavenLocal",
//        ":base:base_wechat:publishToMavenLocal",
//        ":base:base_share:publishToMavenLocal",
//        ":base:base_share_ui:publishToMavenLocal",
//        ":base:base_tai:publishToMavenLocal",
//        ":base:base_video_player:publishToMavenLocal",
//        ":common:common_widget:publishToMavenLocal",
//        ":module:module_base:publishToMavenLocal"
//    )
//
//    doLast {
//        tasksList.forEach { taskName ->
//            println("当前执行的任务： $taskName ...")
//            exec {
//                commandLine = listOf("sh", "-c", "./gradlew $taskName")
//            }
//        }
//    }
//}
