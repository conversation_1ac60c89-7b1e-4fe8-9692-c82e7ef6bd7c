pluginManagement {
    includeBuild("plugins")
    repositories {
        mavenLocal()
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        mavenLocal()
        google()
        mavenCentral()
        maven {
            url = uri("https://maven.aliyun.com/repository/public")
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/jcenter")
        }
        maven {
            url = uri("https://jitpack.io")
        }
        maven {
            url = uri("https://s01.oss.sonatype.org/content/groups/public")
        }
        maven {
            url  = uri("https://maven.pkg.github.com/CarGuo/GSYVideoPlayer")
            credentials {
                // your github name
                username = "提莫"
                // your github generate new token
                password = "****************************************"
            }
        }
    }
}

rootProject.name = "SnailComing"

include(":app")

include(":module:module_teacher")
include(":module:module_teacher_homework")

include(":module:module_login")
include(":module:module_main")
include(":module:module_course")
include(":module:module_webview")
include(":module:module_setting")
include(":module:module_audio")
include(":module:module_video")
include(":module:module_picturebook")
include(":module:module_aitalk")
include(":module:module_bookshelf")
include(":module:module_game")
include(":module:module_base")

include(":common:common_widget")

include(":base:base_sw")
include(":base:base_network")
include(":base:base_multi_state_page")
include(":base:base_router")
include(":base:base_log")
include(":base:base_storage")
include(":base:base_utils")
include(":base:base_res")
include(":base:base_tai")
include(":base:base_multi_type")
include(":base:base_image_loader")
include(":base:base_record")
include(":base:base_complex_imageview")
include(":base:base_lame")
include(":base:base_game")
include(":base:base_video_player")
include(":base:base_audio")
include(":base:base_audio_player")
include(":base:base_share")
include(":base:base_share_ui")
include(":base:base_wechat")
include(":base:base_dialog")
include(":base:base_crash")
include(":base:base_ffmpeg")
