import com.android.build.gradle.LibraryExtension
import org.gradle.api.JavaVersion
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.withType
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile

class AndroidCommonGradle : Plugin<Project> {

    override fun apply(project: Project) {
        with(project) {
            with(pluginManager) {
                if (project.name.contains("app")) {
                    apply("com.android.application")
                } else {
                    apply("maven-publish")  // 仅 library 适用
                    apply("com.android.library")
                }
                apply("org.jetbrains.kotlin.android")
            }

            extensions.configure<LibraryExtension> {
                configureKotlinAndroid(project, this)
                defaultConfig.targetSdk = 35
            }
        }
    }

    private fun Project.configureKotlinAndroid(
        project: Project,
        commonExtension: LibraryExtension
    ) {
        commonExtension.apply {
            compileSdk = 35
            namespace = "com.snails.${project.name.replace("_", ".")}"
            // 打印当前项目的名字
            println("Current namespace: $namespace")
            defaultConfig {
                minSdk = 26
                testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
                consumerProguardFiles("consumer-rules.pro")
            }

            buildTypes {
                release {
                    isMinifyEnabled = false //是否启动混淆 true:打开,false:关闭
                    proguardFiles(
                        getDefaultProguardFile("proguard-android-optimize.txt"),
                        "proguard-rules.pro"
                    )
                    buildConfigField("String", "Environment", "\"PROD\"")
                }
                debug {
                    buildConfigField("String", "Environment", "\"DEV\"")
                }

                create("uat") {
                    buildConfigField("String", "Environment", "\"UAT\"")
                }

                create("pre") {
                    buildConfigField("String", "Environment", "\"PRE\"")
                }
            }

            compileOptions {
                sourceCompatibility = JavaVersion.VERSION_17
                targetCompatibility = JavaVersion.VERSION_17
            }
            if (project.name.contains("module") || project.name.contains("app")) {
                buildFeatures {
                    viewBinding = true
                }
            }
            buildFeatures {
                buildConfig = true
            }
        }

        configureKotlin()
    }

    private fun Project.configureKotlin() {
        // Use withType to workaround https://youtrack.jetbrains.com/issue/KT-55947
        tasks.withType<KotlinJvmCompile>().configureEach {
            compilerOptions {
                jvmTarget.set(JvmTarget.JVM_17)
                freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
            }
        }
    }
}
