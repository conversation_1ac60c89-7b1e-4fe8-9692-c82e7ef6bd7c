tasks.register('projectDependencyGraph') {
    doLast {
        def dot = new File(rootProject.buildDir, 'reports/dependency-graph/project.dot')
        dot.parentFile.mkdirs()
        dot.delete()

        dot << 'digraph {\n'
        dot << "  graph [label=\"${rootProject.name}\\n \",labelloc=t,fontsize=30,ranksep=1.4];\n"
        dot << '  node [style=filled, fillcolor="#bbbbbb"];\n'
        dot << '  rankdir=TB;\n'

        def rootProjects = []
        def queue = [rootProject]
        while (!queue.isEmpty()) {
            def project = queue.remove(0)
            rootProjects.add(project)
            queue.addAll(project.childProjects.values())
        }

        def projects = new LinkedHashSet<Project>()
        def dependencies = new LinkedHashMap<Tuple2<Project, Project>, List<String>>()
        def multiplatformProjects = []
        def jsProjects = []
        def androidProjects = []
        def javaProjects = []

        queue = [rootProject]
        while (!queue.isEmpty()) {
            def project = queue.remove(0)
            queue.addAll(project.childProjects.values())

            if (project.plugins.hasPlugin('org.jetbrains.kotlin.multiplatform')) {
                multiplatformProjects.add(project)
            }
            if (project.plugins.hasPlugin('org.jetbrains.kotlin.js')) {
                jsProjects.add(project)
            }
            if (project.plugins.hasPlugin('com.android.library') || project.plugins.hasPlugin('com.android.application')) {
                androidProjects.add(project)
            }
            if (project.plugins.hasPlugin('java-library') || project.plugins.hasPlugin('java')) {
                javaProjects.add(project)
            }

            project.configurations.configureEach { config ->
                config.dependencies
                        .withType(ProjectDependency)
                        .collect { it.dependencyProject }
                        .each { dependency ->
                            projects.add(project)
                            projects.add(dependency)
                            rootProjects.remove(dependency)

                            def graphKey = new Tuple2<Project, Project>(project, dependency)
                            def traits = dependencies.computeIfAbsent(graphKey) { new ArrayList<String>() }
                            if (config.name.toLowerCase().endsWith('implementation')) {
                                traits.add('style=dashed')
                            } else if (config.name.toLowerCase().endsWith('compileonly')) {
                                traits.add('style=dotted')
                            }
                        }
            }
        }

        projects = projects.sort { it.path }

        dot << '\n  # Projects\n\n'
        for (project in projects) {
            def traits = []

            if (rootProjects.contains(project)) {
                traits.add('shape=box')
            }

            if (multiplatformProjects.contains(project)) {
                traits.add('fillcolor="#ffd2b3"')
            } else if (jsProjects.contains(project)) {
                traits.add('fillcolor="#ffffba"')
            } else if (androidProjects.contains(project)) {
                traits.add('fillcolor="#baffc9"')
            } else if (javaProjects.contains(project)) {
                traits.add('fillcolor="#ffb3ba"')
            } else {
                traits.add('fillcolor="#eeeeee"')
            }

            dot << "  \"${project.path}\" [${traits.join(", ")}];\n"
        }

        dot << '\n  {rank = same;'
        for (project in projects) {
            if (rootProjects.contains(project)) {
                dot << " \"${project.path}\";"
            }
        }
        dot << '}\n'

        dot << '\n  # Dependencies\n\n'
        dependencies.forEach { key, traits ->
            def first = key.first.path
            def second = key.second.path
            if (first != second) {
                dot << "  \"$first\" -> \"$second\""
            }
            if (!traits.isEmpty()) {
                dot << " [${traits.join(", ")}]"
            }
            dot << '\n'
        }

        dot << '}\n'

        // 诊断和执行 dot 命令
        try {
            // 首先检查 dot 命令是否在 PATH 中
            def whichDot = 'which dot'.execute()
            whichDot.waitFor()

            if (whichDot.exitValue() != 0) {
                println("Warning: 'dot' command not found in PATH")
                println("DOT file created at ${dot.absolutePath}")
                return
            }

            def dotPath = whichDot.text.trim()
            println("Found dot at: ${dotPath}")

            // 检查目录权限
            def parentDir = dot.parentFile
            if (!parentDir.canWrite()) {
                println("Warning: No write permission for directory ${parentDir.absolutePath}")
            }

            // 使用完整路径执行 dot 命令
            def cmd = [dotPath, '-Tpng', '-O', 'project.dot']
            println("Executing: ${cmd.join(' ')}")

            def processBuilder = new ProcessBuilder(cmd)
            processBuilder.directory(dot.parentFile)
            processBuilder.redirectErrorStream(true)

            def process = processBuilder.start()
            def output = process.inputStream.text
            def exitCode = process.waitFor()

            if (exitCode != 0) {
                println("Error executing dot command (exit code: ${exitCode})")
                println("Output: ${output}")
                println("DOT file created at ${dot.absolutePath}")
            } else {
                println("Project module dependency graph created at ${dot.absolutePath}.png")
            }

        } catch (Exception e) {
            println("Error executing dot command: ${e.message}")
            println("DOT file created at ${dot.absolutePath}")
            println("You can manually convert it using: dot -Tpng -O project.dot")
        }
    }
}
