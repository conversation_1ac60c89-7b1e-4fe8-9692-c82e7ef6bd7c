[versions]
agp = "8.11.1"
bannerVersion = "2.2.3"
coil = "3.2.0"
ffmpegKitFull = "6.0-2"
gsyvideoplayer = "10.0.0"
kotlin = "2.2.0"
coreKtx = "1.16.0"
annotation = "1.9.1"
ksp = "2.2.0-2.0.2"
appcompat = "1.7.1"
material = "1.12.0"
constraintlayout = "2.2.1"
navigation = "2.9.1"
okio = "3.15.0"
pictureselectorVersion = "v3.11.2"
refreshLayoutVersion = "3.0.0-alpha"
router = "1.2.4"
media3 = "1.7.1"
wechatSdkAndroidVersion = "6.8.34"
xlog = "1.2.5"
mmkv = "2.2.2"
utilcodex = "1.31.1"
gson = "2.13.1"
recyclerview = "1.4.0"
glide = "4.16.0"
lottie = "6.6.7"
immersionbar = "3.2.2"
xxpermissions = "21.0"

# 单元测试
junit = "4.13.2"
robolectric = "4.15.1"

# JetPack
lifecycle_version = "2.9.1"

# NetWork
retrofit_version = "3.0.0"
okhttp_version = "5.1.0"
coroutines_version = "1.10.2"
fragmentKtxVersion = "1.8.8"
junitVersion = "1.2.1"
espressoCore = "3.6.1"

[libraries]

banner = { module = "io.github.youth5201314:banner", version.ref = "bannerVersion" }
coil = { module = "io.coil-kt.coil3:coil", version.ref = "coil" }
coil-network-okhttp = { module = "io.coil-kt.coil3:coil-network-okhttp", version.ref = "coil" }
ffmpeg-kit-full-gpl = { module = "com.arthenica:ffmpeg-kit-full-gpl", version.ref = "ffmpegKitFull" }
gsyvideoplayer-arm64 = { module = "com.shuyu:gsyvideoplayer-arm64", version.ref = "gsyvideoplayer" }
gsyvideoplayer-exo2 = { module = "com.shuyu:gsyvideoplayer-exo2", version.ref = "gsyvideoplayer" }
gsyvideoplayer-java = { module = "com.shuyu:gsyvideoplayer-java", version.ref = "gsyvideoplayer" }

okio = { module = "com.squareup.okio:okio", version.ref = "okio" }
pictureselector-ucrop = { module = "io.github.lucksiege:ucrop", version.ref = "pictureselectorVersion" }
pictureselector = { module = "io.github.lucksiege:pictureselector", version.ref = "pictureselectorVersion" }
refresh-layout-kernel = { module = "io.github.scwang90:refresh-layout-kernel", version.ref = "refreshLayoutVersion" }
wechat-sdk-android = { module = "com.tencent.mm.opensdk:wechat-sdk-android", version.ref = "wechatSdkAndroidVersion" }

permissions = { module = "com.github.getActivity:XXPermissions", version.ref = "xxpermissions" }

immersionbar = { module = "com.geyifeng.immersionbar:immersionbar", version.ref = "immersionbar" }

lottie = { module = "com.airbnb.android:lottie", version.ref = "lottie" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "recyclerview" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
utilcodex = { module = "com.blankj:utilcodex", version.ref = "utilcodex" }

kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect", version.ref = "kotlin" }
annotation = { module = "androidx.annotation:annotation", version.ref = "annotation" }
# 单元测试
junit = { module = "junit:junit", version.ref = "junit" }
robolectric = { module = "org.robolectric:robolectric", version.ref = "robolectric" }

media3-exoplayer = { group = "androidx.media3", name = "media3-exoplayer", version.ref = "media3" }
media3-datasource-okhttp = { group = "androidx.media3", name = "media3-datasource-okhttp", version.ref = "media3" }
media3-session = { group = "androidx.media3", name = "media3-session", version.ref = "media3" }
media3-ui = { group = "androidx.media3", name = "media3-ui", version.ref = "media3" }

# Jetpack

# ViewModel
viewmodel = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycle_version" }
# livedata
livedata = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycle_version" }
# navigation - fragment
navigation-fragment = { module = "androidx.navigation:navigation-fragment-ktx", version.ref = "navigation" }
# navigation
navigation-ui = { module = "androidx.navigation:navigation-ui-ktx", version.ref = "navigation" }

# Android X
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtx" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }

# Android UI
material = { module = "com.google.android.material:material", version.ref = "material" }
constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }

android-gradlePlugin = { module = "com.android.tools.build:gradle", version.ref = "agp" }
kotlin-gradlePlugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }

# NetWork
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit_version" }
retrofit_gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit_version" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp_version" }
coroutines = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "coroutines_version" }
androidx-fragment-ktx = { module = "androidx.fragment:fragment-ktx", version.ref = "fragmentKtxVersion" }

router = { module = "cn.therouter:router", version.ref = "router" }
router_ksp = { module = "cn.therouter:apt", version.ref = "router" }

# Tencent
xlog = { module = "com.tencent.mars:mars-xlog", version.ref = "xlog" }
mmkv = { module = "com.tencent:mmkv", version.ref = "mmkv" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }

[plugins]
# Android application 插件
android-application = { id = "com.android.application", version.ref = "agp" }
# Android library 插件
android-library = { id = "com.android.library", version.ref = "agp" }
# Android kotlin 插件
jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }

router-agp8 = { id = "cn.therouter.agp8", version.ref = "router" }

# 自定义插件，统一 build.gradle文件
common_gradle = { id = "com.hql.plugins", version = "unspecified" }


[bundles]
network = [
    "retrofit",
    "retrofit_gson",
    "okhttp",
    "coroutines"
]
coil = [
    "coil",
    "coil-network-okhttp"
]
