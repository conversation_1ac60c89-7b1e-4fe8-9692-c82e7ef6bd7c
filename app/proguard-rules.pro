# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-dontwarn a.*
-dontwarn com.sanils.base.log.HLog
-dontwarn com.snails.base.image_loader.ImageViewExtKt
-dontwarn com.snails.base.multi_type.ItemViewDelegate
-dontwarn com.snails.base.multi_type.MultiTypeAdapter
-dontwarn com.snails.base.multi_type.Types
-dontwarn com.snails.base.network.repository.info.login.PhoneAreaCodInfo
-dontwarn com.snails.base.network.repository.storage.UserStorage$Companion
-dontwarn com.snails.base.network.repository.storage.UserStorage
-dontwarn com.snails.base.router.HRouter
-dontwarn com.snails.base.storage.Storage
-dontwarn com.snails.base.utils.ext.ViewExtKt
-dontwarn com.snails.module.base.BaseStateActivity
-dontwarn com.snails.module.base.BaseVBActivity
-dontwarn com.snails.module.base.BaseViewModel
-dontwarn com.snails.module.base.utils.ViewBindingDelegate