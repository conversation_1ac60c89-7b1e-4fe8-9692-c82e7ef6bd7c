package com.snails.study.viewmodel

import com.blankj.utilcode.util.AppUtils
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.info.setting.AppVersionInfo
import com.snails.module.base.BaseViewModel
import com.snails.module.base.bean.StateType

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年03月28日 10:59:39
 */
class SplashViewModel : BaseViewModel() {

    fun getUpdateInfo(success: (AppVersionInfo) -> Unit, failed: () -> Unit) {
        request(
            stateType = StateType.DIALOG,
            request = {
                SnailRepository.me.getUpdateInfo(AppUtils.getAppVersionName())
            },
            success = { data ->
                if (data != null) {
                    success.invoke(data)
                } else {
                    failed.invoke()
                }
            },
            failed = {
                failed.invoke()
            }
        )
    }
}