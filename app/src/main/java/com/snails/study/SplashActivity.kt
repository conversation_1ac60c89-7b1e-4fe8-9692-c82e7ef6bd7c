package com.snails.study

import android.annotation.SuppressLint
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.chuanglan.shanyan_sdk.OneKeyLoginManager
import com.snails.base.crash.CrashKitManager
import com.snails.base.network.repository.storage.AppStorage
import com.snails.base.network.repository.storage.OneKeyLoginStorage
import com.snails.base.network.repository.storage.UserStorage
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.module.base.BaseVBActivity
import com.snails.module.base.dialog.AppUpdateDialog
import com.snails.module.login.dialog.PrivacyPolicyDialog
import com.snails.study.databinding.ActivitySplashBinding
import com.snails.study.viewmodel.SplashViewModel
import com.therouter.router.Route
import kotlin.system.exitProcess

@SuppressLint("CustomSplashScreen")
@Route(path = RouterPath.SPLASH_HOME)
class SplashActivity : BaseVBActivity<ActivitySplashBinding>() {

    private val splashViewModel: SplashViewModel by viewModels()

    override fun initData() {
        val allowedPolicy = AppStorage.me.getAllowedPolicy()
        if (!allowedPolicy) {
            PrivacyPolicyDialog(
                callBack = {
                    AppStorage.me.setAllowedPolicy(true)
                    requestAppInfo()
                },
                close = {
                    ActivityUtils.finishAllActivities()
                    kotlin.runCatching {
                        exitProcess(0)
                    }
                }
            ).show(supportFragmentManager, "")
        } else {
            requestAppInfo()
        }
    }

    private fun requestAppInfo() {
        CrashKitManager.init()
        splashViewModel.getUpdateInfo(
            success = { data ->
                if (data.needUpdate == true) {
                    try {
                        AppUpdateDialog(
                            data,
                            next = { path ->
                                //安装
                                AppUtils.installApp(path)
                            },
                            close = {
                                init()
                            },
                            lifecycleScope = lifecycleScope
                        ).show(supportFragmentManager, "")
                    } catch (t: Throwable) {
                        init()
                    }
                } else {
                    init()
                }
            },
            failed = {
                init()
            }
        )
    }

    private fun init() {
        //获取是否存在登录 token
        val accessToken = UserStorage.me.getAccessToken()
        if (accessToken?.isNotEmpty() == true) {
            val isTeacher = UserStorage.me.isTeacher()
            if (isTeacher) {
                HRouter.navigation(RouterPath.TEACHER_MAIN_BASE)
            } else {
                HRouter.navigation(RouterPath.MAIN_BASE)
            }
            finish()
        } else {
            //不存在。则初始化一键登录闪验 SDK。
            initShanyanSDK()
        }
    }

    private fun initShanyanSDK() {
        if (BuildConfig.BUILD_TYPE != "release") {
            //闪验SDK配置debug开关 （必须放在初始化之前，开启后可打印闪验SDK更加详细日志信息）
            OneKeyLoginManager.getInstance().setDebug(true)
        }
        OneKeyLoginManager.getInstance()
            .init(applicationContext, BuildConfig.SHANYAN_APP_ID) { _, _ ->
                OneKeyLoginManager.getInstance().getPhoneInfo { code, result ->
                    try {
                        //code为1022:成功；其他：失败
                        if (code == 1022) {
                            OneKeyLoginStorage.me.putOneKeyLoginInfo(result)
                            //跳转一键登录页
                            HRouter.navigation(RouterPath.LOGIN_HOME)
                        } else {
                            //跳转手机号登录页
                            HRouter.navigation(RouterPath.PHONE_LOGIN)
                        }
                    } catch (t: Throwable) {
                        //跳转手机号登录页
                        HRouter.navigation(RouterPath.PHONE_LOGIN)
                    } finally {
                        finish()
                    }
                }
            }
    }
}