<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.SnailComing" parent="Theme.MaterialComponents.DayNight.NoActionBar.Bridge">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/white</item>
        <item name="colorPrimaryVariant">@color/white</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/white</item>
        <item name="colorSecondaryVariant">@color/white</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- 定义默认背景颜色 -->
        <item name="android:windowBackground">@color/surface_background</item>
    </style>
</resources>