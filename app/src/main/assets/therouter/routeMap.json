[{"path": "snail-router://com.snail.reading/splash", "className": "com.snails.study.SplashActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/web", "className": "com.snails.module.webview.WebViewActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/video", "className": "com.snails.module.video.VideoActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/teacherHomeWork", "className": "com.snails.module.teacher.homework.TeacherHomeWorkActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/teacherMain", "className": "com.snails.module.teacher.TeacherMainActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/setting", "className": "com.snails.module.setting.SettingActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/pictureBook", "className": "com.snails.module.picturebook.PictureBookActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/main", "className": "com.snails.module.main.MainActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/login", "className": "com.snails.module.login.LoginActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/game", "className": "com.snails.module.game.GameActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/course", "className": "com.snails.module.course.CourseActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/bookshelf", "className": "com.snails.module.bookshelf.BookshelfActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/audio", "className": "com.snails.module.audio.AudioActivity", "action": "", "description": "", "params": {}}, {"path": "snail-router://com.snail.reading/aitalk", "className": "com.snails.module.aitalk.AiTalkActivity", "action": "", "description": "", "params": {}}]