import java.io.FileInputStream
import java.util.Locale
import java.util.Properties

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
    alias(libs.plugins.kotlin.ksp)
    id("therouter")
}

android {
    val configPropertiesFile = rootProject.file("config.properties")
    val config = Properties()
    config.load(FileInputStream(configPropertiesFile))

    val compileSDK = (config["compileSdk"] as String).toInt()
    val minSDK = (config["minSdk"] as String).toInt()
    val targetSDK = (config["targetSdk"] as String).toInt()
    val vCode = (config["versionCode"] as String).toInt()
    val vName = config["versionName"] as String
    val jvmTargets = config["jvmTarget"] as String

    val alias = config["keyAlias"] as String
    val password = config["keyPassword"] as String
    val storeFileJks = config["storeFile"] as String
    val shanyanAppId = config["shanyanAppId"] as String
    val shanyanAppKey = config["shanyanAppKey"] as String

    namespace = "com.snails.study"
    compileSdk = compileSDK

    defaultConfig {
        applicationId = "com.snailReading.student"
        minSdk = minSDK
        targetSdk = targetSDK
        versionCode = vCode
        versionName = vName
        ndk {
            abiFilters.add("arm64-v8a")
        }
    }

    packaging {
        resources {
            pickFirsts.addAll(
                listOf(
                    "lib/arm64-v8a/libc++_shared.so"
                )
            )
        }
    }

    signingConfigs {
        create("release") {
            keyAlias = alias
            keyPassword = password
            storeFile = file("${rootDir.absolutePath}/$storeFileJks")
            storePassword = password
        }
    }

    buildTypes {
        val mySignConfig = signingConfigs.getByName("release")
        release {
            isMinifyEnabled = false //是否启动混淆 ture:打开   false:关闭
            //release 版本是否可以debug
            isDebuggable = false
            buildConfigField("String", "Environment", "\"PRO\"")
            buildConfigField("String", "SHANYAN_APP_ID", shanyanAppId)
            buildConfigField("String", "SHANYAN_APP_KEY", shanyanAppKey)
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            // 配置release 的签名信息
            signingConfig = mySignConfig
        }
        debug {
            isMinifyEnabled = false //是否启动混淆 true:打开,false:关闭
            buildConfigField("String", "Environment", "\"DEV\"")
            buildConfigField("String", "SHANYAN_APP_ID", shanyanAppId)
            buildConfigField("String", "SHANYAN_APP_KEY", shanyanAppKey)
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            // 配置debug的签名信息
            signingConfig = mySignConfig
        }
        // 新增的 dev、uat 构建类型
        create("uat") {
            initWith(getByName("debug")) // 基于 debug 类型继承配置
            buildConfigField("String", "Environment", "\"UAT\"")
            isMinifyEnabled = false // 不混淆
            isDebuggable = true // 允许调试
        }

        // 新增的 pre 构建类型
        create("pre") {
            initWith(getByName("debug")) // 基于 debug 类型继承配置
            buildConfigField("String", "Environment", "\"PRE\"")
            isMinifyEnabled = false // 不混淆
            isDebuggable = true // 允许调试
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = jvmTargets
    }
    buildFeatures {
        viewBinding = true
    }
    buildFeatures {
        buildConfig = true
    }

    android.buildTypes.forEach { buildType ->
        val typeName = buildType.name
        val versionName = android.defaultConfig.versionName
        val versionCode = android.defaultConfig.versionCode
        val appName = "蜗牛阅读" // 替换为你的应用名称

        android.productFlavors.map { it.name }
            .ifEmpty { listOf("") }
            .forEach { flavorName ->
                val combineName = "${
                    flavorName.replaceFirstChar {
                        if (it.isLowerCase()) it.titlecase(
                            Locale.getDefault()
                        ) else it.toString()
                    }
                }${typeName.replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }}"

                // 添加自定义 APK 打包任务
                val apkTaskName = "pack$combineName"
                val apkOriginName = "assemble$combineName"
                project.task(apkTaskName) {
                    group = "Pack apk"
                    dependsOn(apkOriginName)
                    doLast {
                        copy {
                            from(
                                File(
                                    project.layout.buildDirectory.get().asFile,
                                    "outputs/apk/$typeName"
                                )
                            )
                            into(File(rootDir, "apks"))
                            rename { "$appName ($versionName-$versionCode-$typeName).apk" }
                            include("*.apk")
                        }
                    }
                }
            }
    }
}

dependencies {
    ksp(libs.router.ksp)
    implementation(project(":module:module_login"))
    implementation(project(":module:module_main"))
    implementation(project(":module:module_course"))
    implementation(project(":module:module_audio"))
    implementation(project(":module:module_webview"))
    implementation(project(":module:module_setting"))
    implementation(project(":module:module_video"))
    implementation(project(":module:module_game"))
    implementation(project(":module:module_aitalk"))
    implementation(project(":module:module_picturebook"))
    implementation(project(":module:module_bookshelf"))
    implementation(project(":module:module_teacher"))
    implementation(project(":module:module_teacher_homework"))

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.constraintlayout)
    implementation(libs.livedata)
    implementation(libs.viewmodel)
    implementation(libs.androidx.fragment.ktx)

    implementation(files("../base/base_aar/libs/NativeCamera.aar"))
    implementation(files("../base/base_aar/libs/NativeGallery.aar"))
    implementation(files("../base/base_aar/libs/qcloud-soe-release-v2.0.5_4979bff.aar"))
    implementation(files("../base/base_aar/libs/shanyan_sdk_v2.4.5.4.aar"))
    implementation(files("../base/base_aar/libs/unityLibrary-release.aar"))
    implementation(files("../base/base_aar/libs/lib_wwapi-3.0.0.7.aar"))
}